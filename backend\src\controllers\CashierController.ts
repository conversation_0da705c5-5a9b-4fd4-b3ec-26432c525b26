import { Request, Response } from 'express';
import { CashSessionModel } from '../models/CashSession';
import { CashRegisterModel } from '../models/CashRegister';
import { 
  CreateCashSessionRequest, 
  CloseCashSessionRequest, 
  CashSessionFilters,
  CreateCashMovementRequest,
  CreateCashRegisterRequest,
  UpdateCashRegisterRequest,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class CashierController {
  // ===== SESSÕES DE CAIXA =====

  // Listar sessões de caixa
  static async getSessions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: CashSessionFilters = {
        status: req.query.status as any,
        user_id: req.query.user_id ? parseInt(req.query.user_id as string) : undefined,
        cash_register_id: req.query.cash_register_id ? parseInt(req.query.cash_register_id as string) : undefined,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        search: req.query.search as string,
      };

      const sessions = await CashSessionModel.findAll(filters);

      res.status(200).json({
        success: true,
        data: sessions,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get cash sessions error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get cash sessions',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar sessão de caixa por ID
  static async getSession(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid cash session ID');
      }

      const session = await CashSessionModel.findById(id);
      if (!session) {
        throw new NotFoundError('Cash session not found');
      }

      // Incluir movimentos se solicitado
      if (req.query.include_movements === 'true') {
        (session as any).movements = await CashSessionModel.getMovements(id);
      }

      res.status(200).json({
        success: true,
        data: session,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get cash session error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get cash session',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Abrir sessão de caixa
  static async openSession(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const sessionData: CreateCashSessionRequest = req.body;

      // Validações básicas
      if (!sessionData.cash_register_id) {
        throw new ValidationError('Cash register ID is required');
      }

      if (sessionData.opening_balance < 0) {
        throw new ValidationError('Opening balance cannot be negative');
      }

      // Verificar se usuário pode abrir sessão
      const canOpen = await CashSessionModel.canUserOpenSession(req.user!.id, sessionData.cash_register_id);
      if (!canOpen.can_open) {
        throw new ValidationError(canOpen.reason || 'Cannot open cash session');
      }

      const session = await CashSessionModel.open(sessionData, req.user!.id);

      console.log(`Cash session opened by user ${req.user?.username} on register ${sessionData.cash_register_id}`);

      res.status(201).json({
        success: true,
        data: session,
        message: 'Cash session opened successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Open cash session error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to open cash session',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Fechar sessão de caixa
  static async closeSession(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const closeData: CloseCashSessionRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid cash session ID');
      }

      if (closeData.closing_balance < 0) {
        throw new ValidationError('Closing balance cannot be negative');
      }

      const session = await CashSessionModel.close(id, closeData, req.user!.id);

      console.log(`Cash session ${id} closed by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: session,
        message: 'Cash session closed successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Close cash session error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to close cash session',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar sessão ativa do usuário
  static async getActiveSession(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const session = await CashSessionModel.findActiveByUser(req.user!.id);

      if (!session) {
        res.status(200).json({
          success: true,
          data: null,
          message: 'No active session found',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
        return;
      }

      // Incluir movimentos se solicitado
      if (req.query.include_movements === 'true') {
        (session as any).movements = await CashSessionModel.getMovements(session.id);
      }

      res.status(200).json({
        success: true,
        data: session,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get active session error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get active session',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Adicionar movimento de caixa
  static async addMovement(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const movementData: CreateCashMovementRequest = req.body;

      // Validações básicas
      if (!movementData.cash_session_id) {
        throw new ValidationError('Cash session ID is required');
      }

      if (!movementData.type || !movementData.category) {
        throw new ValidationError('Movement type and category are required');
      }

      if (movementData.amount <= 0) {
        throw new ValidationError('Amount must be greater than 0');
      }

      const movement = await CashSessionModel.addMovement(movementData, req.user!.id);

      console.log(`Cash movement added by user ${req.user?.username}: ${movementData.type} ${movementData.amount}`);

      res.status(201).json({
        success: true,
        data: movement,
        message: 'Cash movement added successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Add cash movement error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to add cash movement',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar movimentos da sessão
  static async getMovements(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const sessionId = parseInt(req.params.id);

      if (isNaN(sessionId)) {
        throw new ValidationError('Invalid cash session ID');
      }

      const movements = await CashSessionModel.getMovements(sessionId);

      res.status(200).json({
        success: true,
        data: movements,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get cash movements error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get cash movements',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter métricas de caixa
  static async getMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = await CashSessionModel.getMetrics();

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get cash metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get cash metrics',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== CAIXAS REGISTRADORAS =====

  // Listar caixas
  static async getRegisters(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const activeOnly = req.query.active_only !== 'false';
      const withStatus = req.query.with_status === 'true';

      let registers;
      if (withStatus) {
        registers = await CashRegisterModel.findWithStatus();
      } else {
        registers = await CashRegisterModel.findAll(activeOnly);
      }

      res.status(200).json({
        success: true,
        data: registers,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get cash registers error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get cash registers',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar caixa por ID
  static async getRegister(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid cash register ID');
      }

      const register = await CashRegisterModel.findById(id);
      if (!register) {
        throw new NotFoundError('Cash register not found');
      }

      // Incluir estatísticas se solicitado
      if (req.query.with_stats === 'true') {
        const dateFrom = req.query.date_from as string;
        const dateTo = req.query.date_to as string;
        const stats = await CashRegisterModel.getStats(id, dateFrom, dateTo);
        
        res.status(200).json({
          success: true,
          data: { ...register, stats },
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(200).json({
          success: true,
          data: register,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }

    } catch (error) {
      console.error('Get cash register error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get cash register',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar caixas disponíveis
  static async getAvailableRegisters(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const registers = await CashRegisterModel.findAvailable();

      res.status(200).json({
        success: true,
        data: registers,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get available registers error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get available registers',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }
}
