const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

const PORT = 3001;

// Dados mock para teste
const mockData = {
  users: [
    { id: 1, username: 'admin', password: 'password', role: 'manager', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 2, username: 'caix<PERSON>', password: 'password', role: 'cashier', name: 'Operador de Caixa' },
    { id: 3, username: 'cozin<PERSON>', password: 'password', role: 'kitchen', name: '<PERSON><PERSON><PERSON>' }
  ],
  products: [
    { id: 1, name: 'Hamb<PERSON>rguer Clássico', price: 25.90, category: 'Hambúrgueres', active: true },
    { id: 2, name: 'Pizza Margherita', price: 35.00, category: 'Pizzas', active: true },
    { id: 3, name: 'Refrigerante Lata', price: 5.50, category: 'Bebidas', active: true },
    { id: 4, name: 'Batata Frita', price: 12.00, category: 'Acompanhamentos', active: true },
    { id: 5, name: 'So<PERSON><PERSON>', price: 8.50, category: 'Sobremesas', active: true }
  ],
  orders: []
};

// Função para responder J<PERSON><PERSON>
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data, null, 2));
}

// Função para servir arquivos estáticos
function serveFile(res, filePath, contentType) {
  fs.readFile(filePath, (err, data) => {
    if (err) {
      res.writeHead(404, { 'Content-Type': 'text/plain' });
      res.end('Arquivo não encontrado');
      return;
    }
    
    res.writeHead(200, { 
      'Content-Type': contentType,
      'Access-Control-Allow-Origin': '*'
    });
    res.end(data);
  });
}

// Função para ler body da requisição
function readBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const data = body ? JSON.parse(body) : {};
      callback(null, data);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Criar servidor
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} - ${method} ${path}`);

  // CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // Servir página principal
  if (path === '/' && method === 'GET') {
    serveFile(res, './public/index.html', 'text/html');
    return;
  }
  
  // API Routes
  if (path === '/api/health' && method === 'GET') {
    sendJSON(res, {
      success: true,
      message: 'Sistema PDV Adib funcionando!',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      status: 'online'
    });
  }
  
  else if (path === '/api/status' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: {
        system: 'PDV Adib',
        version: '1.0.0',
        environment: 'development',
        features: [
          'PDV (Ponto de Venda)',
          'Sistema de Cozinha (KDS)',
          'Gestão de Produtos',
          'Relatórios e Analytics',
          'TEF',
          'Offline-First',
          'Sincronização'
        ],
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      }
    });
  }
  
  else if (path === '/api/auth/login' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }
      
      const { username, password } = data;
      const user = mockData.users.find(u => u.username === username && u.password === password);
      
      if (user) {
        sendJSON(res, {
          success: true,
          data: {
            token: 'demo-token-' + Date.now(),
            user: {
              id: user.id,
              username: user.username,
              name: user.name,
              role: user.role
            }
          },
          message: 'Login realizado com sucesso!'
        });
      } else {
        sendJSON(res, {
          success: false,
          error: 'Credenciais inválidas'
        }, 401);
      }
    });
  }
  
  else if (path === '/api/products' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.products.filter(p => p.active),
      total: mockData.products.filter(p => p.active).length
    });
  }
  
  else if (path === '/api/orders' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.orders,
      total: mockData.orders.length
    });
  }
  
  else if (path === '/api/orders' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }
      
      const { items, total } = data;
      
      if (!items || !Array.isArray(items) || items.length === 0) {
        return sendJSON(res, { success: false, error: 'Itens são obrigatórios' }, 400);
      }
      
      const order = {
        id: mockData.orders.length + 1,
        number: 'PED' + String(mockData.orders.length + 1).padStart(3, '0'),
        status: 'pending',
        items,
        total: total || items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        created_at: new Date().toISOString()
      };
      
      mockData.orders.push(order);
      
      sendJSON(res, {
        success: true,
        data: order,
        message: 'Pedido criado com sucesso!'
      });
    });
  }
  
  else if (path === '/api/sync/metrics' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: {
        lastSync: new Date().toISOString(),
        pendingItems: 0,
        syncStatus: 'up_to_date',
        conflicts: 0,
        totalSynced: 150
      }
    });
  }
  
  else if (path === '/api/reports/sales-summary' && method === 'GET') {
    const totalSales = mockData.orders.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = mockData.orders.length;
    const averageTicket = totalOrders > 0 ? totalSales / totalOrders : 0;
    
    sendJSON(res, {
      success: true,
      data: {
        totalSales,
        totalOrders,
        averageTicket,
        topProducts: mockData.products.slice(0, 3),
        period: 'today'
      }
    });
  }
  
  else {
    sendJSON(res, {
      success: false,
      error: 'Endpoint não encontrado',
      message: `${method} ${path} não existe`
    }, 404);
  }
});

// Iniciar servidor
server.listen(PORT, () => {
  console.log('\n🚀 Sistema PDV Adib - Interface Web iniciada!');
  console.log(`📡 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`🌐 Interface Web: http://localhost:${PORT}`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Status: http://localhost:${PORT}/api/status`);
  console.log('\n👤 Credenciais de teste:');
  console.log('   Admin: admin / password');
  console.log('   Caixa: caixa / password');
  console.log('   Cozinha: cozinha / password');
  console.log('\n✨ Sistema com interface gráfica pronto!');
  console.log('\n🌐 Abra no navegador: http://localhost:' + PORT);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🔄 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🔄 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});
