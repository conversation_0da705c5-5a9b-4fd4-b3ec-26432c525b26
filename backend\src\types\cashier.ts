// Tipos para caixa e movimentações financeiras

export interface CashRegister {
  id: number;
  name: string;
  description?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateCashRegisterRequest {
  name: string;
  description?: string;
}

export interface UpdateCashRegisterRequest {
  name?: string;
  description?: string;
  is_active?: boolean;
}

export interface CashSession {
  id: number;
  cash_register_id: number;
  cash_register_name?: string;
  user_id: number;
  user_name?: string;
  status: CashSessionStatus;
  opening_balance: number;
  closing_balance?: number;
  expected_balance?: number;
  difference?: number;
  total_sales: number;
  total_cash_sales: number;
  total_card_sales: number;
  total_pix_sales: number;
  total_other_sales: number;
  total_withdrawals: number;
  total_deposits: number;
  opened_at: string;
  closed_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type CashSessionStatus = 'open' | 'closed';

export interface CreateCashSessionRequest {
  cash_register_id: number;
  opening_balance: number;
  notes?: string;
}

export interface CloseCashSessionRequest {
  closing_balance: number;
  notes?: string;
}

export interface CashMovement {
  id: number;
  cash_session_id: number;
  type: CashMovementType;
  category: CashMovementCategory;
  amount: number;
  description: string;
  reference_type?: string;
  reference_id?: number;
  user_id: number;
  user_name?: string;
  created_at: string;
  updated_at: string;
}

export type CashMovementType = 'in' | 'out';
export type CashMovementCategory = 
  | 'sale' 
  | 'withdrawal' 
  | 'deposit' 
  | 'change' 
  | 'opening' 
  | 'closing' 
  | 'adjustment' 
  | 'refund'
  | 'expense'
  | 'other';

export interface CreateCashMovementRequest {
  cash_session_id: number;
  type: CashMovementType;
  category: CashMovementCategory;
  amount: number;
  description: string;
  reference_type?: string;
  reference_id?: number;
}

export interface UpdateCashMovementRequest {
  amount?: number;
  description?: string;
}

// Filtros
export interface CashSessionFilters {
  status?: CashSessionStatus;
  user_id?: number;
  cash_register_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CashMovementFilters {
  cash_session_id?: number;
  type?: CashMovementType;
  category?: CashMovementCategory;
  user_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// Relatórios
export interface CashReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_sessions: number;
    total_sales: number;
    total_cash: number;
    total_card: number;
    total_pix: number;
    total_withdrawals: number;
    total_deposits: number;
    average_ticket: number;
  };
  by_user: Array<{
    user_id: number;
    user_name: string;
    sessions_count: number;
    total_sales: number;
    total_cash: number;
    accuracy_percentage: number;
  }>;
  by_register: Array<{
    register_id: number;
    register_name: string;
    sessions_count: number;
    total_sales: number;
    utilization_hours: number;
  }>;
  by_payment_method: Array<{
    method: string;
    amount: number;
    percentage: number;
    transactions_count: number;
  }>;
}

export interface CashFlowReport {
  period: {
    start: string;
    end: string;
  };
  opening_balance: number;
  closing_balance: number;
  net_movement: number;
  movements: {
    sales: number;
    withdrawals: number;
    deposits: number;
    adjustments: number;
    refunds: number;
    expenses: number;
    other: number;
  };
  by_day: Array<{
    date: string;
    opening: number;
    sales: number;
    withdrawals: number;
    deposits: number;
    closing: number;
    net: number;
  }>;
}

// Configurações de caixa
export interface CashierSettings {
  require_opening_balance: boolean;
  allow_negative_balance: boolean;
  max_cash_amount: number;
  auto_close_session: boolean;
  session_timeout_hours: number;
  require_manager_approval_withdrawal: boolean;
  max_withdrawal_amount: number;
  enable_cash_counting: boolean;
  denominations: CashDenomination[];
}

export interface CashDenomination {
  value: number;
  type: 'coin' | 'note';
  is_active: boolean;
}

// Contagem de caixa
export interface CashCount {
  id: number;
  cash_session_id: number;
  type: CashCountType;
  total_counted: number;
  total_expected: number;
  difference: number;
  denominations: CashCountDenomination[];
  user_id: number;
  user_name?: string;
  created_at: string;
  updated_at: string;
}

export type CashCountType = 'opening' | 'closing' | 'intermediate';

export interface CashCountDenomination {
  value: number;
  quantity: number;
  total: number;
}

export interface CreateCashCountRequest {
  cash_session_id: number;
  type: CashCountType;
  denominations: Array<{
    value: number;
    quantity: number;
  }>;
}

// Sangrias e suprimentos
export interface CashWithdrawal {
  id: number;
  cash_session_id: number;
  amount: number;
  reason: string;
  authorized_by?: number;
  authorized_by_name?: string;
  user_id: number;
  user_name?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCashWithdrawalRequest {
  cash_session_id: number;
  amount: number;
  reason: string;
  authorized_by?: number;
}

export interface CashDeposit {
  id: number;
  cash_session_id: number;
  amount: number;
  reason: string;
  user_id: number;
  user_name?: string;
  created_at: string;
  updated_at: string;
}

export interface CreateCashDepositRequest {
  cash_session_id: number;
  amount: number;
  reason: string;
}

// Métricas de caixa
export interface CashMetrics {
  active_sessions: number;
  total_cash_today: number;
  total_sales_today: number;
  average_ticket_today: number;
  transactions_today: number;
  largest_sale_today: number;
  cash_in_registers: number;
  pending_withdrawals: number;
  sessions_by_status: {
    open: number;
    closed: number;
  };
  payment_methods_today: Array<{
    method: string;
    amount: number;
    percentage: number;
  }>;
}

// Validações
export interface CashValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
  balance_info?: {
    current_balance: number;
    expected_balance: number;
    difference: number;
    within_tolerance: boolean;
  };
}

// Auditoria de caixa
export interface CashAudit {
  id: number;
  cash_session_id: number;
  audit_type: CashAuditType;
  findings: string;
  recommendations?: string;
  auditor_id: number;
  auditor_name?: string;
  status: CashAuditStatus;
  created_at: string;
  updated_at: string;
}

export type CashAuditType = 'routine' | 'discrepancy' | 'complaint' | 'random';
export type CashAuditStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

export interface CreateCashAuditRequest {
  cash_session_id: number;
  audit_type: CashAuditType;
  findings: string;
  recommendations?: string;
}

// Reconciliação
export interface CashReconciliation {
  id: number;
  date: string;
  total_expected: number;
  total_counted: number;
  difference: number;
  status: ReconciliationStatus;
  reconciled_by?: number;
  reconciled_by_name?: string;
  reconciled_at?: string;
  notes?: string;
  sessions: CashSession[];
  created_at: string;
  updated_at: string;
}

export type ReconciliationStatus = 'pending' | 'reconciled' | 'discrepancy';

export interface CreateReconciliationRequest {
  date: string;
  notes?: string;
}

export interface UpdateReconciliationRequest {
  status?: ReconciliationStatus;
  notes?: string;
}
