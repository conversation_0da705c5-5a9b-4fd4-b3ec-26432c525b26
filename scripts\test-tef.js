#!/usr/bin/env node

/**
 * Script para testar as APIs TEF
 * Testa CRUD de transações TEF e configurações
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar configurações TEF
async function testTefConfigs() {
  log('\n⚙️ Testando Configurações TEF...', 'blue');

  try {
    // Listar configurações
    log('Listando configurações TEF...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/tef/configs`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} configurações encontradas`, 'green');

    // Buscar configuração ativa
    log('Buscando configuração ativa...', 'cyan');
    const activeResponse = await axios.get(`${API_BASE_URL}/tef/configs/active`, {
      headers: getHeaders()
    });
    
    if (activeResponse.data.data) {
      log(`✅ Configuração ativa encontrada: ${activeResponse.data.data.provider_name}`, 'green');
    } else {
      log(`✅ Nenhuma configuração ativa (esperado)`, 'green');
    }

    // Validar configuração
    log('Validando configuração TEF...', 'cyan');
    const validateResponse = await axios.get(`${API_BASE_URL}/tef/configs/validate`, {
      headers: getHeaders()
    });
    const validation = validateResponse.data.data;
    log(`✅ Validação concluída:`, 'green');
    log(`   - Válida: ${validation.valid}`, 'cyan');
    log(`   - Erros: ${validation.errors.length}`, 'cyan');
    log(`   - Avisos: ${validation.warnings.length}`, 'cyan');

    // Testar conexão com provedor
    log('Testando conexão com provedor...', 'cyan');
    const providerResponse = await axios.get(`${API_BASE_URL}/tef/configs/test-provider`, {
      headers: getHeaders()
    });
    const providerTest = providerResponse.data.data;
    log(`✅ Teste provedor:`, 'green');
    log(`   - Sucesso: ${providerTest.success}`, 'cyan');
    log(`   - Tempo: ${providerTest.response_time}s`, 'cyan');

    // Testar conexão com PinPad
    log('Testando conexão com PinPad...', 'cyan');
    const pinpadResponse = await axios.get(`${API_BASE_URL}/tef/configs/test-pinpad`, {
      headers: getHeaders()
    });
    const pinpadTest = pinpadResponse.data.data;
    log(`✅ Teste PinPad:`, 'green');
    log(`   - Sucesso: ${pinpadTest.success}`, 'cyan');
    log(`   - Status: ${pinpadTest.status}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de configurações: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar transações TEF
async function testTefTransactions() {
  log('\n💳 Testando Transações TEF...', 'blue');

  try {
    // Listar transações
    log('Listando transações TEF...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/tef/transactions`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} transações encontradas`, 'green');

    // Obter métricas
    log('Obtendo métricas TEF...', 'cyan');
    const metricsResponse = await axios.get(`${API_BASE_URL}/tef/metrics`, {
      headers: getHeaders()
    });
    const metrics = metricsResponse.data.data;
    log(`✅ Métricas obtidas:`, 'green');
    log(`   - Transações hoje: ${metrics.transactions_today}`, 'cyan');
    log(`   - Aprovadas: ${metrics.transactions_approved}`, 'cyan');
    log(`   - Taxa aprovação: ${metrics.approval_rate.toFixed(1)}%`, 'cyan');
    log(`   - Status PinPad: ${metrics.pinpad_status}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de transações: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar fluxo completo de transação TEF
async function testTefTransactionFlow() {
  log('\n🔄 Testando Fluxo Completo de Transação TEF...', 'blue');

  try {
    // Criar transação de crédito
    log('Criando transação de crédito...', 'cyan');
    const creditTransactionData = {
      type: 'sale',
      operation: 'credit',
      amount: 89.90,
      installments: 3,
      card_type: 'credit',
      order_id: 1,
      cash_session_id: 1
    };

    const createCreditResponse = await axios.post(`${API_BASE_URL}/tef/transactions`, creditTransactionData, {
      headers: getHeaders()
    });
    
    const creditTransactionId = createCreditResponse.data.data.id;
    log(`✅ Transação de crédito criada com ID: ${creditTransactionId}`, 'green');

    // Buscar transação por ID
    log('Buscando transação por ID...', 'cyan');
    const getTransactionResponse = await axios.get(`${API_BASE_URL}/tef/transactions/${creditTransactionId}`, {
      headers: getHeaders()
    });
    log(`✅ Transação encontrada: ${getTransactionResponse.data.data.operation}`, 'green');

    // Atualizar transação para "aprovada"
    log('Aprovando transação...', 'cyan');
    await axios.put(`${API_BASE_URL}/tef/transactions/${creditTransactionId}`, {
      status: 'approved',
      card_brand: 'Visa',
      card_number_masked: '****1234',
      authorization_code: 'ABC123',
      nsu: '000001',
      processed_at: new Date().toISOString()
    }, {
      headers: getHeaders()
    });
    log('✅ Transação aprovada', 'green');

    // Criar transação de débito
    log('Criando transação de débito...', 'cyan');
    const debitTransactionData = {
      type: 'sale',
      operation: 'debit',
      amount: 45.50,
      installments: 1,
      card_type: 'debit'
    };

    const createDebitResponse = await axios.post(`${API_BASE_URL}/tef/transactions`, debitTransactionData, {
      headers: getHeaders()
    });
    
    const debitTransactionId = createDebitResponse.data.data.id;
    log(`✅ Transação de débito criada com ID: ${debitTransactionId}`, 'green');

    // Atualizar para aprovada
    await axios.put(`${API_BASE_URL}/tef/transactions/${debitTransactionId}`, {
      status: 'approved',
      card_brand: 'Mastercard',
      card_number_masked: '****5678',
      authorization_code: 'DEF456',
      nsu: '000002',
      processed_at: new Date().toISOString()
    }, {
      headers: getHeaders()
    });

    // Cancelar transação de crédito
    log('Cancelando transação de crédito...', 'cyan');
    await axios.delete(`${API_BASE_URL}/tef/transactions/${creditTransactionId}`, {
      data: {
        reason: 'Teste de cancelamento - transação criada para testes'
      },
      headers: getHeaders()
    });
    log('✅ Transação cancelada', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro no fluxo de transação TEF: ${error.response?.data?.error || error.message}`, 'red');
    if (error.response?.data?.details) {
      console.log('Detalhes:', error.response.data.details);
    }
    return false;
  }
}

// Testar filtros de transações
async function testTransactionFilters() {
  log('\n🔍 Testando Filtros de Transações...', 'blue');

  try {
    // Filtrar por tipo
    log('Filtrando transações por tipo...', 'cyan');
    const typeFilterResponse = await axios.get(`${API_BASE_URL}/tef/transactions?type=sale`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por tipo funcionando`, 'green');

    // Filtrar por operação
    log('Filtrando transações por operação...', 'cyan');
    const operationFilterResponse = await axios.get(`${API_BASE_URL}/tef/transactions?operation=credit`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por operação funcionando`, 'green');

    // Filtrar por status
    log('Filtrando transações por status...', 'cyan');
    const statusFilterResponse = await axios.get(`${API_BASE_URL}/tef/transactions?status=approved`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por status funcionando`, 'green');

    // Filtrar por data
    log('Filtrando transações por data...', 'cyan');
    const today = new Date().toISOString().slice(0, 10);
    const dateFilterResponse = await axios.get(`${API_BASE_URL}/tef/transactions?date_from=${today}&date_to=${today}`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por data funcionando`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de filtros: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar criar transação sem tipo
    log('Testando transação sem tipo...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/tef/transactions`, {
        operation: 'credit',
        amount: 50.00
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de tipo funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar criar transação com valor inválido
    log('Testando transação com valor inválido...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/tef/transactions`, {
        type: 'sale',
        operation: 'credit',
        amount: -10.00
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de valor funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar cancelar com motivo muito curto
    log('Testando cancelamento com motivo inválido...', 'cyan');
    try {
      await axios.delete(`${API_BASE_URL}/tef/transactions/1`, {
        data: {
          reason: 'abc'
        },
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de motivo de cancelamento funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs TEF - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      configs: await testTefConfigs(),
      transactions: await testTefTransactions(),
      flow: await testTefTransactionFlow(),
      filters: await testTransactionFilters(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs TEF funcionando corretamente.', 'green');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
