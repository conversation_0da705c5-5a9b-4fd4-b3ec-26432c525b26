import { executeQuery, executeQ<PERSON>ySingle, executeUpdate } from '../utils/database';
import { User, CreateUserRequest, UpdateUserRequest, UserFilters, UserRole } from '../types';
import bcrypt from 'bcryptjs';

export class UserModel {
  // Buscar usuário por ID
  static async findById(id: number): Promise<User | null> {
    const user = await executeQuerySingle<User>(
      'SELECT id, username, full_name, email, phone, role, is_active, created_at, updated_at FROM users WHERE id = ?',
      [id]
    );
    return user || null;
  }

  // Buscar usuário por username
  static async findByUsername(username: string): Promise<User | null> {
    const user = await executeQuerySingle<User>(
      'SELECT id, username, full_name, email, phone, role, is_active, created_at, updated_at FROM users WHERE username = ?',
      [username]
    );
    return user || null;
  }

  // Buscar usuário por username com senha (para autenticação)
  static async findByUsernameWithPassword(username: string): Promise<(User & { password_hash: string }) | null> {
    const user = await executeQuerySingle<User & { password_hash: string }>(
      'SELECT * FROM users WHERE username = ?',
      [username]
    );
    return user || null;
  }

  // Listar usuários com filtros
  static async findAll(filters: UserFilters = {}): Promise<User[]> {
    let query = 'SELECT id, username, full_name, email, phone, role, is_active, created_at, updated_at FROM users WHERE 1=1';
    const params: any[] = [];

    if (filters.role) {
      query += ' AND role = ?';
      params.push(filters.role);
    }

    if (filters.is_active !== undefined) {
      query += ' AND is_active = ?';
      params.push(filters.is_active ? 1 : 0);
    }

    if (filters.search) {
      query += ' AND (username LIKE ? OR full_name LIKE ? OR email LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY created_at DESC';

    return await executeQuery<User>(query, params);
  }

  // Criar usuário
  static async create(userData: CreateUserRequest): Promise<User> {
    // Verificar se username já existe
    const existingUser = await this.findByUsername(userData.username);
    if (existingUser) {
      throw new Error('Username already exists');
    }

    // Hash da senha
    const saltRounds = 10;
    const password_hash = await bcrypt.hash(userData.password, saltRounds);

    // Inserir usuário
    const result = await executeUpdate(
      `INSERT INTO users (username, password_hash, full_name, email, phone, role, is_active)
       VALUES (?, ?, ?, ?, ?, ?, 1)`,
      [
        userData.username,
        password_hash,
        userData.full_name,
        userData.email || null,
        userData.phone || null,
        userData.role,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create user');
    }

    // Retornar usuário criado
    const newUser = await this.findById(result.lastID);
    if (!newUser) {
      throw new Error('Failed to retrieve created user');
    }

    return newUser;
  }

  // Atualizar usuário
  static async update(id: number, userData: UpdateUserRequest): Promise<User> {
    const user = await this.findById(id);
    if (!user) {
      throw new Error('User not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    if (userData.full_name !== undefined) {
      updateFields.push('full_name = ?');
      params.push(userData.full_name);
    }

    if (userData.email !== undefined) {
      updateFields.push('email = ?');
      params.push(userData.email);
    }

    if (userData.phone !== undefined) {
      updateFields.push('phone = ?');
      params.push(userData.phone);
    }

    if (userData.role !== undefined) {
      updateFields.push('role = ?');
      params.push(userData.role);
    }

    if (userData.is_active !== undefined) {
      updateFields.push('is_active = ?');
      params.push(userData.is_active ? 1 : 0);
    }

    if (updateFields.length === 0) {
      return user; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE users SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar usuário atualizado
    const updatedUser = await this.findById(id);
    if (!updatedUser) {
      throw new Error('Failed to retrieve updated user');
    }

    return updatedUser;
  }

  // Atualizar senha
  static async updatePassword(id: number, newPassword: string): Promise<void> {
    const user = await this.findById(id);
    if (!user) {
      throw new Error('User not found');
    }

    const saltRounds = 10;
    const password_hash = await bcrypt.hash(newPassword, saltRounds);

    await executeUpdate(
      'UPDATE users SET password_hash = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [password_hash, id]
    );
  }

  // Verificar senha
  static async verifyPassword(username: string, password: string): Promise<User | null> {
    const user = await this.findByUsernameWithPassword(username);
    if (!user) {
      return null;
    }

    const isValid = await bcrypt.compare(password, user.password_hash);
    if (!isValid) {
      return null;
    }

    // Retornar usuário sem a senha
    const { password_hash, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }

  // Deletar usuário (soft delete)
  static async delete(id: number): Promise<void> {
    const user = await this.findById(id);
    if (!user) {
      throw new Error('User not found');
    }

    await executeUpdate(
      'UPDATE users SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Contar usuários por role
  static async countByRole(): Promise<Record<UserRole, number>> {
    const results = await executeQuery<{ role: UserRole; count: number }>(
      'SELECT role, COUNT(*) as count FROM users WHERE is_active = 1 GROUP BY role'
    );

    const counts: Record<UserRole, number> = {
      manager: 0,
      cashier: 0,
      kitchen: 0,
    };

    results.forEach(result => {
      counts[result.role] = result.count;
    });

    return counts;
  }

  // Verificar se é o último gerente
  static async isLastManager(id: number): Promise<boolean> {
    const user = await this.findById(id);
    if (!user || user.role !== 'manager') {
      return false;
    }

    const managerCount = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM users WHERE role = ? AND is_active = 1',
      ['manager']
    );

    return (managerCount?.count || 0) <= 1;
  }
}
