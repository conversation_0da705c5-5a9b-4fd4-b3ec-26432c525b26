import { Router } from 'express';
import { TefController } from '../controllers/TefController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para transações TEF
const createTransactionValidation = [
  body('type')
    .isIn(['sale', 'cancellation', 'administrative'])
    .withMessage('Invalid transaction type'),
  body('operation')
    .isIn(['credit', 'debit', 'voucher', 'cancel', 'reprint', 'administrative'])
    .withMessage('Invalid operation'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  body('installments')
    .optional()
    .isInt({ min: 1, max: 12 })
    .withMessage('Installments must be between 1 and 12'),
  body('card_type')
    .optional()
    .isIn(['credit', 'debit', 'voucher', 'unknown'])
    .withMessage('Invalid card type'),
  body('order_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Order ID must be a positive integer'),
  body('cash_session_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Cash session ID must be a positive integer'),
];

const updateTransactionValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Transaction ID must be a positive integer'),
  body('status')
    .optional()
    .isIn(['pending', 'processing', 'approved', 'denied', 'cancelled', 'error', 'timeout'])
    .withMessage('Invalid transaction status'),
  body('card_brand')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Card brand must be at most 50 characters'),
  body('card_number_masked')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Card number masked must be at most 20 characters'),
  body('authorization_code')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Authorization code must be at most 20 characters'),
  body('nsu')
    .optional()
    .isLength({ max: 20 })
    .withMessage('NSU must be at most 20 characters'),
  body('transaction_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Transaction ID must be at most 100 characters'),
];

const cancelTransactionValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Transaction ID must be a positive integer'),
  body('reason')
    .notEmpty()
    .withMessage('Cancellation reason is required')
    .isLength({ min: 10, max: 255 })
    .withMessage('Cancellation reason must be between 10 and 255 characters'),
  body('original_transaction_id')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Original transaction ID must be at most 100 characters'),
  body('authorization_code')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Authorization code must be at most 20 characters'),
];

// Validações para configurações TEF
const createConfigValidation = [
  body('provider')
    .isIn(['cielo', 'rede', 'stone', 'getnet', 'pagseguro', 'mercadopago', 'simulator'])
    .withMessage('Invalid TEF provider'),
  body('provider_name')
    .notEmpty()
    .withMessage('Provider name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Provider name must be between 2 and 100 characters'),
  body('terminal_id')
    .notEmpty()
    .withMessage('Terminal ID is required')
    .isLength({ min: 1, max: 20 })
    .withMessage('Terminal ID must be between 1 and 20 characters'),
  body('merchant_id')
    .notEmpty()
    .withMessage('Merchant ID is required')
    .isLength({ min: 1, max: 50 })
    .withMessage('Merchant ID must be between 1 and 50 characters'),
  body('api_key')
    .optional()
    .isLength({ max: 255 })
    .withMessage('API key must be at most 255 characters'),
  body('api_secret')
    .optional()
    .isLength({ max: 255 })
    .withMessage('API secret must be at most 255 characters'),
  body('endpoint_url')
    .optional()
    .isURL()
    .withMessage('Invalid endpoint URL'),
  body('pinpad_port')
    .optional()
    .isLength({ max: 20 })
    .withMessage('PinPad port must be at most 20 characters'),
  body('pinpad_model')
    .optional()
    .isLength({ max: 50 })
    .withMessage('PinPad model must be at most 50 characters'),
  body('timeout_seconds')
    .optional()
    .isInt({ min: 5, max: 300 })
    .withMessage('Timeout must be between 5 and 300 seconds'),
];

const updateConfigValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Config ID must be a positive integer'),
  body('provider_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Provider name must be between 2 and 100 characters'),
  body('terminal_id')
    .optional()
    .isLength({ min: 1, max: 20 })
    .withMessage('Terminal ID must be between 1 and 20 characters'),
  body('merchant_id')
    .optional()
    .isLength({ min: 1, max: 50 })
    .withMessage('Merchant ID must be between 1 and 50 characters'),
  body('timeout_seconds')
    .optional()
    .isInt({ min: 5, max: 300 })
    .withMessage('Timeout must be between 5 and 300 seconds'),
];

// ===== ROTAS DE TRANSAÇÕES TEF =====

/**
 * @route GET /api/tef/transactions
 * @desc Listar transações TEF
 * @access Private (read tef)
 */
router.get('/transactions',
  authenticate,
  authorize('tef', 'read'),
  TefController.getTransactions
);

/**
 * @route GET /api/tef/metrics
 * @desc Obter métricas TEF
 * @access Private (read tef)
 */
router.get('/metrics',
  authenticate,
  authorize('tef', 'read'),
  TefController.getMetrics
);

/**
 * @route GET /api/tef/transactions/:id
 * @desc Buscar transação TEF por ID
 * @access Private (read tef)
 */
router.get('/transactions/:id',
  authenticate,
  authorize('tef', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  TefController.getTransaction
);

/**
 * @route POST /api/tef/transactions
 * @desc Criar transação TEF
 * @access Private (create tef)
 */
router.post('/transactions',
  authenticate,
  authorize('tef', 'create'),
  createTransactionValidation,
  validateRequest,
  logUserAction('create_tef_transaction'),
  TefController.createTransaction
);

/**
 * @route PUT /api/tef/transactions/:id
 * @desc Atualizar transação TEF
 * @access Private (update tef)
 */
router.put('/transactions/:id',
  authenticate,
  authorize('tef', 'update'),
  updateTransactionValidation,
  validateRequest,
  logUserAction('update_tef_transaction'),
  TefController.updateTransaction
);

/**
 * @route DELETE /api/tef/transactions/:id
 * @desc Cancelar transação TEF
 * @access Private (delete tef)
 */
router.delete('/transactions/:id',
  authenticate,
  authorize('tef', 'delete'),
  cancelTransactionValidation,
  validateRequest,
  logUserAction('cancel_tef_transaction'),
  TefController.cancelTransaction
);

// ===== ROTAS DE CONFIGURAÇÕES TEF =====

/**
 * @route GET /api/tef/configs
 * @desc Listar configurações TEF
 * @access Private (read tef)
 */
router.get('/configs',
  authenticate,
  authorize('tef', 'read'),
  TefController.getConfigs
);

/**
 * @route GET /api/tef/configs/active
 * @desc Buscar configuração TEF ativa
 * @access Private (read tef)
 */
router.get('/configs/active',
  authenticate,
  authorize('tef', 'read'),
  TefController.getActiveConfig
);

/**
 * @route GET /api/tef/configs/validate/:id?
 * @desc Validar configuração TEF
 * @access Private (read tef)
 */
router.get('/configs/validate/:id?',
  authenticate,
  authorize('tef', 'read'),
  TefController.validateConfig
);

/**
 * @route GET /api/tef/configs/test-provider/:id?
 * @desc Testar conexão com provedor TEF
 * @access Private (read tef)
 */
router.get('/configs/test-provider/:id?',
  authenticate,
  authorize('tef', 'read'),
  TefController.testProviderConnection
);

/**
 * @route GET /api/tef/configs/test-pinpad/:id?
 * @desc Testar conexão com PinPad
 * @access Private (read tef)
 */
router.get('/configs/test-pinpad/:id?',
  authenticate,
  authorize('tef', 'read'),
  TefController.testPinPadConnection
);

export default router;
