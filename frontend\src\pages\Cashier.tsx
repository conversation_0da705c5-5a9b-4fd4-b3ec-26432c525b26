import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
} from '@mui/material';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  AttachMoney,
  TrendingUp,
  TrendingDown,
  Receipt,
  Close,
  Add,
  Remove,
  Print,
  Assessment,
  Lock,
  LockOpen,
  Refresh,
} from '@mui/icons-material';
import { useAuthStore } from '../services/auth';

interface CashSession {
  id: number;
  cash_register_id: number;
  cash_register_name: string;
  user_id: number;
  user_name: string;
  status: 'open' | 'closed';
  opening_balance: number;
  closing_balance?: number;
  expected_balance?: number;
  difference?: number;
  total_sales: number;
  total_cash_sales: number;
  total_card_sales: number;
  total_pix_sales: number;
  total_withdrawals: number;
  total_deposits: number;
  opened_at: string;
  closed_at?: string;
  notes?: string;
}

interface CashMovement {
  id: number;
  type: 'in' | 'out';
  category: string;
  amount: number;
  description: string;
  user_name: string;
  created_at: string;
}

interface CashRegister {
  id: number;
  name: string;
  description?: string;
  current_status: 'available' | 'in_use';
  active_user_name?: string;
  session_duration?: number;
}

interface CashMetrics {
  active_sessions: number;
  total_cash_today: number;
  total_sales_today: number;
  average_ticket_today: number;
  transactions_today: number;
  cash_in_registers: number;
  payment_methods_today: Array<{
    method: string;
    amount: number;
    percentage: number;
  }>;
}

const Cashier: React.FC = () => {
  const [activeSession, setActiveSession] = useState<CashSession | null>(null);
  const [registers, setRegisters] = useState<CashRegister[]>([]);
  const [movements, setMovements] = useState<CashMovement[]>([]);
  const [metrics, setMetrics] = useState<CashMetrics | null>(null);
  const [openDialog, setOpenDialog] = useState(false);
  const [closeDialog, setCloseDialog] = useState(false);
  const [movementDialog, setMovementDialog] = useState(false);
  const [selectedRegister, setSelectedRegister] = useState<number | null>(null);
  const [openingBalance, setOpeningBalance] = useState<number>(0);
  const [closingBalance, setClosingBalance] = useState<number>(0);
  const [movementType, setMovementType] = useState<'withdrawal' | 'deposit'>('withdrawal');
  const [movementAmount, setMovementAmount] = useState<number>(0);
  const [movementDescription, setMovementDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular métricas
        const mockMetrics: CashMetrics = {
          active_sessions: 3,
          total_cash_today: 1250.75,
          total_sales_today: 3480.50,
          average_ticket_today: 28.90,
          transactions_today: 120,
          cash_in_registers: 2100.25,
          payment_methods_today: [
            { method: 'Dinheiro', amount: 1250.75, percentage: 35.9 },
            { method: 'Cartão', amount: 1680.25, percentage: 48.3 },
            { method: 'PIX', amount: 549.50, percentage: 15.8 },
          ],
        };

        // Simular caixas
        const mockRegisters: CashRegister[] = [
          {
            id: 1,
            name: 'Caixa 01',
            description: 'Caixa principal',
            current_status: 'available',
          },
          {
            id: 2,
            name: 'Caixa 02',
            description: 'Caixa secundário',
            current_status: 'in_use',
            active_user_name: 'João Silva',
            session_duration: 4.5,
          },
          {
            id: 3,
            name: 'Caixa 03',
            description: 'Caixa delivery',
            current_status: 'available',
          },
        ];

        // Simular sessão ativa (se houver)
        const mockActiveSession: CashSession | null = null; // Usuário não tem sessão ativa

        // Simular movimentos
        const mockMovements: CashMovement[] = [
          {
            id: 1,
            type: 'in',
            category: 'opening',
            amount: 200.00,
            description: 'Saldo inicial',
            user_name: user?.full_name || 'Usuário',
            created_at: '2024-12-19T08:00:00Z',
          },
          {
            id: 2,
            type: 'in',
            category: 'sale',
            amount: 45.90,
            description: 'Venda #001',
            user_name: user?.full_name || 'Usuário',
            created_at: '2024-12-19T09:15:00Z',
          },
          {
            id: 3,
            type: 'out',
            category: 'withdrawal',
            amount: 50.00,
            description: 'Sangria - Troco',
            user_name: user?.full_name || 'Usuário',
            created_at: '2024-12-19T10:30:00Z',
          },
        ];

        setMetrics(mockMetrics);
        setRegisters(mockRegisters);
        setActiveSession(mockActiveSession);
        setMovements(mockMovements);
      } catch (error) {
        console.error('Error loading cashier data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [user]);

  // Abrir caixa
  const openCashSession = async () => {
    if (!selectedRegister || openingBalance < 0) {
      return;
    }

    // Simular abertura de caixa
    const newSession: CashSession = {
      id: Date.now(),
      cash_register_id: selectedRegister,
      cash_register_name: registers.find(r => r.id === selectedRegister)?.name || 'Caixa',
      user_id: user?.id || 0,
      user_name: user?.full_name || 'Usuário',
      status: 'open',
      opening_balance: openingBalance,
      total_sales: 0,
      total_cash_sales: 0,
      total_card_sales: 0,
      total_pix_sales: 0,
      total_withdrawals: 0,
      total_deposits: 0,
      opened_at: new Date().toISOString(),
    };

    setActiveSession(newSession);
    setOpenDialog(false);
    setSelectedRegister(null);
    setOpeningBalance(0);

    console.log('Cash session opened:', newSession);
  };

  // Fechar caixa
  const closeCashSession = async () => {
    if (!activeSession || closingBalance < 0) {
      return;
    }

    const expectedBalance = activeSession.opening_balance + activeSession.total_cash_sales - activeSession.total_withdrawals + activeSession.total_deposits;
    const difference = closingBalance - expectedBalance;

    const updatedSession: CashSession = {
      ...activeSession,
      status: 'closed',
      closing_balance: closingBalance,
      expected_balance: expectedBalance,
      difference: difference,
      closed_at: new Date().toISOString(),
    };

    setActiveSession(null);
    setCloseDialog(false);
    setClosingBalance(0);

    console.log('Cash session closed:', updatedSession);
  };

  // Adicionar movimento
  const addMovement = async () => {
    if (!activeSession || movementAmount <= 0 || !movementDescription.trim()) {
      return;
    }

    const newMovement: CashMovement = {
      id: Date.now(),
      type: movementType === 'withdrawal' ? 'out' : 'in',
      category: movementType,
      amount: movementAmount,
      description: movementDescription,
      user_name: user?.full_name || 'Usuário',
      created_at: new Date().toISOString(),
    };

    setMovements([...movements, newMovement]);
    
    // Atualizar totais da sessão
    if (movementType === 'withdrawal') {
      setActiveSession({
        ...activeSession,
        total_withdrawals: activeSession.total_withdrawals + movementAmount,
      });
    } else {
      setActiveSession({
        ...activeSession,
        total_deposits: activeSession.total_deposits + movementAmount,
      });
    }

    setMovementDialog(false);
    setMovementAmount(0);
    setMovementDescription('');

    console.log('Movement added:', newMovement);
  };

  // Calcular saldo atual
  const getCurrentBalance = (): number => {
    if (!activeSession) return 0;
    return activeSession.opening_balance + activeSession.total_cash_sales - activeSession.total_withdrawals + activeSession.total_deposits;
  };

  // Formatar moeda
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  // Formatar data/hora
  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              Controle de Caixa
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              {!activeSession ? (
                <Button
                  variant="contained"
                  startIcon={<LockOpen />}
                  onClick={() => setOpenDialog(true)}
                  color="success"
                >
                  Abrir Caixa
                </Button>
              ) : (
                <>
                  <Button
                    variant="outlined"
                    startIcon={<Add />}
                    onClick={() => {
                      setMovementType('deposit');
                      setMovementDialog(true);
                    }}
                  >
                    Suprimento
                  </Button>
                  <Button
                    variant="outlined"
                    startIcon={<Remove />}
                    onClick={() => {
                      setMovementType('withdrawal');
                      setMovementDialog(true);
                    }}
                  >
                    Sangria
                  </Button>
                  <Button
                    variant="contained"
                    startIcon={<Lock />}
                    onClick={() => setCloseDialog(true)}
                    color="error"
                  >
                    Fechar Caixa
                  </Button>
                </>
              )}
              <IconButton>
                <Refresh />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Status do Caixa */}
      {activeSession && (
        <Paper sx={{ m: 2, p: 2, bgcolor: 'success.light', color: 'success.contrastText' }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h6">
                Caixa Aberto: {activeSession.cash_register_name}
              </Typography>
              <Typography variant="body2">
                Aberto em: {formatDateTime(activeSession.opened_at)}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Typography variant="h5" align="right">
                Saldo: {formatCurrency(getCurrentBalance())}
              </Typography>
            </Grid>
          </Grid>
        </Paper>
      )}

      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
        <Grid container spacing={3}>
          {/* Métricas */}
          {metrics && (
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Resumo do Dia
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AccountBalance sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                      <Typography variant="h5" color="primary">
                        {metrics.active_sessions}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Caixas Ativos
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <AttachMoney sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                      <Typography variant="h5" color="success.main">
                        {formatCurrency(metrics.total_sales_today)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Vendas Hoje
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Receipt sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                      <Typography variant="h5" color="info.main">
                        {metrics.transactions_today}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Transações
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Card>
                    <CardContent sx={{ textAlign: 'center' }}>
                      <Assessment sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                      <Typography variant="h5" color="warning.main">
                        {formatCurrency(metrics.average_ticket_today)}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Ticket Médio
                      </Typography>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Grid>
          )}

          {/* Caixas Registradoras */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Status dos Caixas
            </Typography>
            <List>
              {registers.map((register) => (
                <ListItem key={register.id}>
                  <ListItemText
                    primary={register.name}
                    secondary={
                      register.current_status === 'in_use'
                        ? `Em uso por ${register.active_user_name} (${register.session_duration?.toFixed(1)}h)`
                        : 'Disponível'
                    }
                  />
                  <Chip
                    label={register.current_status === 'in_use' ? 'Em Uso' : 'Disponível'}
                    color={register.current_status === 'in_use' ? 'error' : 'success'}
                    size="small"
                  />
                </ListItem>
              ))}
            </List>
          </Grid>

          {/* Movimentos Recentes */}
          <Grid item xs={12} md={6}>
            <Typography variant="h6" gutterBottom>
              Movimentos Recentes
            </Typography>
            <TableContainer component={Paper} sx={{ maxHeight: 400 }}>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Tipo</TableCell>
                    <TableCell>Descrição</TableCell>
                    <TableCell align="right">Valor</TableCell>
                    <TableCell>Hora</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {movements.slice(-10).reverse().map((movement) => (
                    <TableRow key={movement.id}>
                      <TableCell>
                        <Chip
                          icon={movement.type === 'in' ? <TrendingUp /> : <TrendingDown />}
                          label={movement.type === 'in' ? 'Entrada' : 'Saída'}
                          color={movement.type === 'in' ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>{movement.description}</TableCell>
                      <TableCell align="right">
                        <Typography
                          color={movement.type === 'in' ? 'success.main' : 'error.main'}
                          fontWeight="bold"
                        >
                          {movement.type === 'in' ? '+' : '-'}{formatCurrency(movement.amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {new Date(movement.created_at).toLocaleTimeString('pt-BR')}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>
        </Grid>
      </Box>

      {/* Dialog Abrir Caixa */}
      <Dialog open={openDialog} onClose={() => setOpenDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Abrir Caixa</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Selecionar Caixa</InputLabel>
              <Select
                value={selectedRegister || ''}
                onChange={(e) => setSelectedRegister(Number(e.target.value))}
                label="Selecionar Caixa"
              >
                {registers
                  .filter(r => r.current_status === 'available')
                  .map(register => (
                    <MenuItem key={register.id} value={register.id}>
                      {register.name} - {register.description}
                    </MenuItem>
                  ))}
              </Select>
            </FormControl>

            <TextField
              fullWidth
              label="Saldo Inicial"
              type="number"
              value={openingBalance}
              onChange={(e) => setOpeningBalance(Number(e.target.value))}
              InputProps={{
                startAdornment: 'R$ ',
              }}
              inputProps={{ min: 0, step: 0.01 }}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setOpenDialog(false)}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            onClick={openCashSession}
            disabled={!selectedRegister || openingBalance < 0}
          >
            Abrir Caixa
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog Fechar Caixa */}
      <Dialog open={closeDialog} onClose={() => setCloseDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Fechar Caixa</DialogTitle>
        <DialogContent>
          {activeSession && (
            <Box sx={{ pt: 1 }}>
              <Alert severity="info" sx={{ mb: 2 }}>
                <Typography variant="body2">
                  <strong>Saldo Esperado:</strong> {formatCurrency(getCurrentBalance())}
                </Typography>
              </Alert>

              <TextField
                fullWidth
                label="Saldo Contado"
                type="number"
                value={closingBalance}
                onChange={(e) => setClosingBalance(Number(e.target.value))}
                InputProps={{
                  startAdornment: 'R$ ',
                }}
                inputProps={{ min: 0, step: 0.01 }}
              />

              {closingBalance > 0 && (
                <Alert 
                  severity={Math.abs(closingBalance - getCurrentBalance()) <= 1 ? 'success' : 'warning'} 
                  sx={{ mt: 2 }}
                >
                  <Typography variant="body2">
                    <strong>Diferença:</strong> {formatCurrency(closingBalance - getCurrentBalance())}
                  </Typography>
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCloseDialog(false)}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={closeCashSession}
            disabled={closingBalance <= 0}
          >
            Fechar Caixa
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog Movimento */}
      <Dialog open={movementDialog} onClose={() => setMovementDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          {movementType === 'withdrawal' ? 'Sangria' : 'Suprimento'}
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <TextField
              fullWidth
              label="Valor"
              type="number"
              value={movementAmount}
              onChange={(e) => setMovementAmount(Number(e.target.value))}
              InputProps={{
                startAdornment: 'R$ ',
              }}
              inputProps={{ min: 0.01, step: 0.01 }}
              sx={{ mb: 2 }}
            />

            <TextField
              fullWidth
              label="Descrição"
              value={movementDescription}
              onChange={(e) => setMovementDescription(e.target.value)}
              multiline
              rows={3}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setMovementDialog(false)}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            onClick={addMovement}
            disabled={movementAmount <= 0 || !movementDescription.trim()}
          >
            Confirmar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Cashier;
