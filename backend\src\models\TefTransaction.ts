import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  TefTransaction, 
  CreateTefTransactionRequest, 
  UpdateTefTransactionRequest, 
  CancelTefTransactionRequest,
  TefTransactionFilters,
  TefMetrics
} from '../types/tef';

export class TefTransactionModel {
  // Buscar transação TEF por ID
  static async findById(id: number): Promise<TefTransaction | null> {
    const transaction = await executeQuerySingle<TefTransaction>(
      `SELECT tt.*, u.full_name as user_name, o.order_number, cs.cash_register_id
       FROM tef_transactions tt
       LEFT JOIN users u ON tt.user_id = u.id
       LEFT JOIN orders o ON tt.order_id = o.id
       LEFT JOIN cash_sessions cs ON tt.cash_session_id = cs.id
       WHERE tt.id = ?`,
      [id]
    );
    return transaction || null;
  }

  // Buscar transação por NSU
  static async findByNsu(nsu: string): Promise<TefTransaction | null> {
    const transaction = await executeQuerySingle<TefTransaction>(
      'SELECT * FROM tef_transactions WHERE nsu = ?',
      [nsu]
    );
    return transaction || null;
  }

  // Buscar transação por ID da transação do provedor
  static async findByTransactionId(transactionId: string): Promise<TefTransaction | null> {
    const transaction = await executeQuerySingle<TefTransaction>(
      'SELECT * FROM tef_transactions WHERE transaction_id = ?',
      [transactionId]
    );
    return transaction || null;
  }

  // Buscar transações por pedido
  static async findByOrder(orderId: number): Promise<TefTransaction[]> {
    return await executeQuery<TefTransaction>(
      `SELECT tt.*, u.full_name as user_name
       FROM tef_transactions tt
       LEFT JOIN users u ON tt.user_id = u.id
       WHERE tt.order_id = ?
       ORDER BY tt.created_at DESC`,
      [orderId]
    );
  }

  // Listar transações com filtros
  static async findAll(filters: TefTransactionFilters = {}): Promise<TefTransaction[]> {
    let query = `
      SELECT tt.*, u.full_name as user_name, o.order_number
      FROM tef_transactions tt
      LEFT JOIN users u ON tt.user_id = u.id
      LEFT JOIN orders o ON tt.order_id = o.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.type) {
      query += ' AND tt.type = ?';
      params.push(filters.type);
    }

    if (filters.operation) {
      query += ' AND tt.operation = ?';
      params.push(filters.operation);
    }

    if (filters.status) {
      query += ' AND tt.status = ?';
      params.push(filters.status);
    }

    if (filters.card_type) {
      query += ' AND tt.card_type = ?';
      params.push(filters.card_type);
    }

    if (filters.order_id) {
      query += ' AND tt.order_id = ?';
      params.push(filters.order_id);
    }

    if (filters.cash_session_id) {
      query += ' AND tt.cash_session_id = ?';
      params.push(filters.cash_session_id);
    }

    if (filters.user_id) {
      query += ' AND tt.user_id = ?';
      params.push(filters.user_id);
    }

    if (filters.date_from) {
      query += ' AND DATE(tt.created_at) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(tt.created_at) <= ?';
      params.push(filters.date_to);
    }

    if (filters.search) {
      query += ' AND (tt.nsu LIKE ? OR tt.authorization_code LIKE ? OR tt.transaction_id LIKE ? OR o.order_number LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY tt.created_at DESC';

    return await executeQuery<TefTransaction>(query, params);
  }

  // Criar transação TEF
  static async create(transactionData: CreateTefTransactionRequest, userId: number): Promise<TefTransaction> {
    return await executeTransaction(async (db) => {
      // Verificar se pedido existe (se fornecido)
      if (transactionData.order_id) {
        const orderExists = await executeQuerySingle<{ count: number }>(
          'SELECT COUNT(*) as count FROM orders WHERE id = ?',
          [transactionData.order_id]
        );

        if (!orderExists || orderExists.count === 0) {
          throw new Error('Order not found');
        }
      }

      // Verificar se sessão de caixa existe (se fornecida)
      if (transactionData.cash_session_id) {
        const sessionExists = await executeQuerySingle<{ count: number }>(
          'SELECT COUNT(*) as count FROM cash_sessions WHERE id = ? AND status = "open"',
          [transactionData.cash_session_id]
        );

        if (!sessionExists || sessionExists.count === 0) {
          throw new Error('Cash session not found or closed');
        }
      }

      // Inserir transação TEF
      const result = await executeUpdate(
        `INSERT INTO tef_transactions (
          order_id, cash_session_id, type, operation, amount, installments, 
          card_type, status, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending', ?)`,
        [
          transactionData.order_id || null,
          transactionData.cash_session_id || null,
          transactionData.type,
          transactionData.operation,
          transactionData.amount,
          transactionData.installments || 1,
          transactionData.card_type || 'unknown',
          userId,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create TEF transaction');
      }

      // Retornar transação criada
      const newTransaction = await this.findById(result.lastID);
      if (!newTransaction) {
        throw new Error('Failed to retrieve created TEF transaction');
      }

      return newTransaction;
    });
  }

  // Atualizar transação TEF
  static async update(id: number, transactionData: UpdateTefTransactionRequest): Promise<TefTransaction> {
    const transaction = await this.findById(id);
    if (!transaction) {
      throw new Error('TEF transaction not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = [
      'status', 'card_brand', 'card_number_masked', 'authorization_code',
      'nsu', 'transaction_id', 'provider_response', 'error_message',
      'receipt_customer', 'receipt_merchant', 'processed_at', 'confirmed_at'
    ];

    updatableFields.forEach(field => {
      if (transactionData[field as keyof UpdateTefTransactionRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        params.push(transactionData[field as keyof UpdateTefTransactionRequest]);
      }
    });

    if (updateFields.length === 0) {
      return transaction; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE tef_transactions SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar transação atualizada
    const updatedTransaction = await this.findById(id);
    if (!updatedTransaction) {
      throw new Error('Failed to retrieve updated TEF transaction');
    }

    return updatedTransaction;
  }

  // Cancelar transação TEF
  static async cancel(id: number, cancelData: CancelTefTransactionRequest, userId: number): Promise<TefTransaction> {
    return await executeTransaction(async (db) => {
      const transaction = await this.findById(id);
      if (!transaction) {
        throw new Error('TEF transaction not found');
      }

      if (transaction.status !== 'approved') {
        throw new Error('Only approved transactions can be cancelled');
      }

      // Criar transação de cancelamento
      const cancelTransaction = await this.create({
        order_id: transaction.order_id,
        cash_session_id: transaction.cash_session_id,
        type: 'cancellation',
        operation: 'cancel',
        amount: transaction.amount,
        card_type: transaction.card_type,
      }, userId);

      // Atualizar transação original
      await executeUpdate(
        `UPDATE tef_transactions SET 
           status = 'cancelled',
           cancellation_reason = ?,
           cancelled_at = CURRENT_TIMESTAMP,
           updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [cancelData.reason, id]
      );

      // Retornar transação atualizada
      const updatedTransaction = await this.findById(id);
      if (!updatedTransaction) {
        throw new Error('Failed to retrieve updated TEF transaction');
      }

      return updatedTransaction;
    });
  }

  // Obter métricas TEF
  static async getMetrics(): Promise<TefMetrics> {
    // Transações do dia
    const todayTransactions = await executeQuerySingle<{
      count: number;
      amount: number;
      pending: number;
      approved: number;
      denied: number;
    }>(
      `SELECT 
         COUNT(*) as count,
         COALESCE(SUM(amount), 0) as amount,
         SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
         SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved,
         SUM(CASE WHEN status = 'denied' THEN 1 ELSE 0 END) as denied
       FROM tef_transactions 
       WHERE DATE(created_at) = DATE('now') AND type = 'sale'`
    );

    // Taxa de aprovação
    const totalTransactions = todayTransactions?.count || 0;
    const approvedTransactions = todayTransactions?.approved || 0;
    const approvalRate = totalTransactions > 0 ? (approvedTransactions / totalTransactions) * 100 : 0;

    // Última transação
    const lastTransaction = await executeQuerySingle<{ created_at: string }>(
      'SELECT created_at FROM tef_transactions ORDER BY created_at DESC LIMIT 1'
    );

    // Limite diário usado (simulado)
    const dailyLimitUsed = todayTransactions?.amount || 0;
    const dailyLimitTotal = 50000; // R$ 50.000 (configurável)

    return {
      transactions_today: totalTransactions,
      transactions_pending: todayTransactions?.pending || 0,
      transactions_approved: approvedTransactions,
      transactions_denied: todayTransactions?.denied || 0,
      total_amount_today: todayTransactions?.amount || 0,
      approval_rate: approvalRate,
      average_transaction_time: 3.2, // TODO: Calcular tempo real
      pinpad_status: 'ready', // TODO: Verificar status real
      last_transaction_at: lastTransaction?.created_at,
      provider_status: 'online', // TODO: Verificar status real
      daily_limit_used: dailyLimitUsed,
      daily_limit_total: dailyLimitTotal,
    };
  }

  // Buscar transações pendentes
  static async findPending(): Promise<TefTransaction[]> {
    return await this.findAll({ status: 'pending' });
  }

  // Buscar transações do dia
  static async findToday(): Promise<TefTransaction[]> {
    const today = new Date().toISOString().slice(0, 10);
    return await this.findAll({
      date_from: today,
      date_to: today,
    });
  }

  // Buscar transações aprovadas
  static async findApproved(): Promise<TefTransaction[]> {
    return await this.findAll({ status: 'approved' });
  }

  // Verificar se transação pode ser cancelada
  static async canCancel(id: number): Promise<{
    can_cancel: boolean;
    reason?: string;
  }> {
    const transaction = await this.findById(id);
    if (!transaction) {
      return {
        can_cancel: false,
        reason: 'Transaction not found',
      };
    }

    if (transaction.status !== 'approved') {
      return {
        can_cancel: false,
        reason: 'Only approved transactions can be cancelled',
      };
    }

    // Verificar prazo de cancelamento (24 horas)
    const createdAt = new Date(transaction.created_at);
    const now = new Date();
    const hoursDiff = (now.getTime() - createdAt.getTime()) / (1000 * 60 * 60);

    if (hoursDiff > 24) {
      return {
        can_cancel: false,
        reason: 'Cancellation period expired (24 hours)',
      };
    }

    return { can_cancel: true };
  }

  // Obter total de vendas por tipo de cartão
  static async getTotalsByCardType(dateFrom?: string, dateTo?: string): Promise<Array<{
    card_type: string;
    count: number;
    amount: number;
  }>> {
    let query = `
      SELECT 
        card_type,
        COUNT(*) as count,
        SUM(amount) as amount
      FROM tef_transactions 
      WHERE status = 'approved' AND type = 'sale'
    `;
    const params: any[] = [];

    if (dateFrom) {
      query += ' AND DATE(created_at) >= ?';
      params.push(dateFrom);
    }

    if (dateTo) {
      query += ' AND DATE(created_at) <= ?';
      params.push(dateTo);
    }

    query += ' GROUP BY card_type ORDER BY amount DESC';

    return await executeQuery<{
      card_type: string;
      count: number;
      amount: number;
    }>(query, params);
  }

  // Obter histórico de transações por hora
  static async getTransactionsByHour(date: string): Promise<Array<{
    hour: number;
    count: number;
    amount: number;
  }>> {
    return await executeQuery<{
      hour: number;
      count: number;
      amount: number;
    }>(
      `SELECT 
         CAST(strftime('%H', created_at) AS INTEGER) as hour,
         COUNT(*) as count,
         SUM(amount) as amount
       FROM tef_transactions 
       WHERE DATE(created_at) = ? AND status = 'approved' AND type = 'sale'
       GROUP BY hour
       ORDER BY hour`,
      [date]
    );
  }
}
