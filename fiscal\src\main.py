"""
Módulo Fiscal do Sistema PDV Adib
FastAPI + ACBr Suite para emissão de NFC-e e NF-e
"""

from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn
from loguru import logger
import os
from dotenv import load_dotenv

# Carregar variáveis de ambiente
load_dotenv()

# Configurar logging
logger.add(
    "../logs/fiscal.log",
    rotation="1 day",
    retention="30 days",
    level="INFO",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}"
)

# Criar aplicação FastAPI
app = FastAPI(
    title="Adib PDV - Módulo Fiscal",
    description="API para emissão de documentos fiscais (NFC-e/NF-e) via ACBr Suite",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configurar CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://localhost:3001"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Importar routers
from .nfce.router import router as nfce_router
from .nfe.router import router as nfe_router
from .utils.router import router as utils_router

# Registrar routers
app.include_router(nfce_router, prefix="/nfce", tags=["NFC-e"])
app.include_router(nfe_router, prefix="/nfe", tags=["NF-e"])
app.include_router(utils_router, prefix="/utils", tags=["Utilitários"])

@app.get("/")
async def root():
    """Endpoint raiz da API fiscal"""
    return {
        "message": "Adib PDV - Módulo Fiscal",
        "version": "1.0.0",
        "status": "online",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    """Verificação de saúde da API"""
    try:
        # Verificar se ACBr está disponível
        acbr_status = await check_acbr_status()
        
        # Verificar certificado
        cert_status = await check_certificate_status()
        
        return {
            "status": "healthy",
            "acbr": acbr_status,
            "certificate": cert_status,
            "timestamp": "2024-12-19T10:00:00Z"
        }
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(status_code=503, detail="Service unavailable")

async def check_acbr_status():
    """Verificar status do ACBr Suite"""
    try:
        # TODO: Implementar verificação real do ACBr
        return {"status": "available", "version": "1.0.0"}
    except Exception as e:
        logger.error(f"ACBr check failed: {str(e)}")
        return {"status": "unavailable", "error": str(e)}

async def check_certificate_status():
    """Verificar status do certificado digital"""
    try:
        cert_path = os.getenv("FISCAL_CERTIFICATE_PATH")
        if not cert_path or not os.path.exists(cert_path):
            return {"status": "not_found"}
        
        # TODO: Implementar verificação de validade do certificado
        return {"status": "valid", "expires_at": "2025-12-31"}
    except Exception as e:
        logger.error(f"Certificate check failed: {str(e)}")
        return {"status": "error", "error": str(e)}

@app.exception_handler(Exception)
async def global_exception_handler(request, exc):
    """Handler global para exceções"""
    logger.error(f"Unhandled exception: {str(exc)}")
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )

if __name__ == "__main__":
    port = int(os.getenv("FISCAL_PORT", 8001))
    host = os.getenv("FISCAL_HOST", "localhost")
    
    logger.info(f"Starting Fiscal API on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=True if os.getenv("NODE_ENV") == "development" else False,
        log_level="info"
    )
