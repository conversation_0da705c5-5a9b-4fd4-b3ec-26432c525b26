# Checklist de Desenvolvimento - Sistema PDV Adib

## 📋 Visão Geral
Sistema PDV offline-first com Electron+React, Node.js, SQLite, integração fiscal (ACBr), TEF e insights via GPT-4.

---

## 🏗️ **FASE 1: Estrutura Base e Configuração**

### 1.1 Configuração do Projeto
- [x] Criar estrutura de pastas do monorepo
- [x] Configurar package.json principal
- [x] Configurar Electron + React (frontend)
- [x] Configurar Node.js + Express (backend)
- [x] Configurar Python + FastAPI (fiscal)
- [x] Configurar SQLite e schemas iniciais
- [x] Configurar ESLint, Prettier e TypeScript
- [x] Configurar scripts de build e desenvolvimento

### 1.2 Banco de Dados
- [x] Schema de usuários e perfis
- [x] Schema de produtos e categorias
- [x] Schema de estoque e movimentações
- [x] Schema de vendas e itens
- [x] Schema de caixa e movimentações financeiras
- [x] Schema de clientes e endereços
- [x] Schema de mesas e comandas
- [x] Schema de configurações fiscais
- [x] Migrations e seeders iniciais

---

## 👥 **FASE 2: Autenticação e Gestão de Usuários**

### 2.1 Sistema de Login
- [x] Tela de login com seleção de perfil
- [x] Autenticação local (JWT ou sessão)
- [x] Middleware de autorização
- [x] Controle de permissões por perfil

### 2.2 Perfis de Usuário
- [x] **Gerente**: acesso total ao sistema
- [x] **Caixa/Atendente**: operações de venda
- [x] **Cozinha**: visualização apenas (KDS)

---

## 🛍️ **FASE 3: Gestão de Produtos e Estoque**

### 3.1 Cadastro de Produtos
- [x] CRUD de produtos
- [x] Categorização de produtos
- [x] Código de barras e busca
- [x] Preços e margens
- [x] Status ativo/inativo

### 3.2 Gestão de Insumos
- [x] CRUD de insumos/matérias-primas
- [x] Unidades de medida
- [x] Controle de validade
- [x] Fornecedores

### 3.3 Ficha Técnica
- [x] Receitas de produtos
- [x] Consumo automático de insumos
- [x] Cálculo de custo por produto
- [x] Validação de disponibilidade

### 3.4 Controle de Estoque
- [x] Movimentações de entrada/saída
- [x] Inventário e ajustes
- [x] Alertas de estoque baixo
- [ ] Bloqueio de venda sem estoque

---

## 💰 **FASE 4: PDV e Atendimento Multicanal**

### 4.1 Interface Principal do PDV
- [ ] Tela de seleção de canal (Balcão/Mesas/Delivery)
- [ ] Busca de produtos (nome, código, categoria)
- [ ] Carrinho de compras
- [ ] Aplicação de descontos
- [ ] Observações por item

### 4.2 Atendimento Balcão
- [ ] Pedido único por cliente
- [ ] Finalização rápida
- [ ] Impressão de comprovante

### 4.3 Gestão de Mesas
- [ ] Abertura de nova mesa
- [ ] Retomada de mesa existente
- [ ] Comando digital por mesa
- [ ] Fechamento parcial/total
- [ ] Transfer de itens entre mesas

### 4.4 Delivery
- [ ] Cadastro de clientes
- [ ] Gestão de endereços
- [ ] Cálculo de taxa de entrega
- [ ] Tempo estimado de entrega
- [ ] Status do pedido

---

## 💳 **FASE 5: Caixa e Movimentações Financeiras**

### 5.1 Operações de Caixa
- [ ] Abertura de caixa (saldo inicial)
- [ ] Sangrias (retiradas)
- [ ] Suprimentos (injeção de dinheiro)
- [ ] Controle de troco
- [ ] Fechamento de caixa

### 5.2 Formas de Pagamento
- [ ] Dinheiro
- [ ] Cartão de crédito/débito
- [ ] PIX
- [ ] Voucher/Vale
- [ ] Pagamento misto

### 5.3 Relatórios de Caixa
- [ ] Relatório de abertura
- [ ] Movimentações do turno
- [ ] Relatório de fechamento
- [ ] Conferência de valores

---

## 🧾 **FASE 6: Integração Fiscal**

### 6.1 Configuração Fiscal
- [ ] Configuração de certificado digital
- [ ] Dados da empresa
- [ ] Configuração de impostos
- [ ] Ambiente de homologação/produção

### 6.2 NFC-e (Nota Fiscal do Consumidor Eletrônica)
- [ ] Integração com ACBr via Python
- [ ] Geração automática após pagamento
- [ ] Envio para SEFAZ
- [ ] Tratamento de contingência
- [ ] Reimpressão de cupons

### 6.3 NF-e (Nota Fiscal Eletrônica)
- [ ] Emissão para pessoa jurídica
- [ ] Dados do destinatário
- [ ] Cálculo de impostos
- [ ] XML e DANFE

### 6.4 Modo Não-Fiscal
- [ ] Recibo simples para MEI
- [ ] Controle de numeração
- [ ] Relatórios não-fiscais

---

## 💳 **FASE 7: TEF (Transferência Eletrônica de Fundos)**

### 7.1 Integração com PinPad
- [ ] Comunicação USB/Serial
- [ ] SDK do fabricante (Cielo/Rede/Stone)
- [ ] Fluxo de autorização
- [ ] Tratamento de erros

### 7.2 Operações TEF
- [ ] Venda no crédito
- [ ] Venda no débito
- [ ] Cancelamento
- [ ] Reimpressão de comprovante
- [ ] Relatório gerencial

---

## 🍳 **FASE 8: KDS (Kitchen Display System)**

### 8.1 Interface da Cozinha
- [ ] Tela de pedidos pendentes
- [ ] Organização por prioridade
- [ ] Timer de preparo
- [ ] Marcação de itens prontos

### 8.2 Comunicação em Tempo Real
- [ ] WebSocket para envio instantâneo
- [ ] Notificações sonoras
- [ ] Status de preparo
- [ ] Reimpressão de comandas

### 8.3 Impressão Térmica
- [ ] Integração com QZ Tray
- [ ] Formatação de comandas
- [ ] Impressão por setor (cozinha/bar)

---

## 📊 **FASE 9: Relatórios e Analytics**

### 9.1 Relatórios Operacionais
- [ ] Vendas por período
- [ ] Vendas por produto/categoria
- [ ] Vendas por operador
- [ ] Ranking de produtos
- [ ] Margem de contribuição

### 9.2 Dashboards
- [ ] Visão geral de vendas
- [ ] Gráficos de performance
- [ ] Indicadores em tempo real
- [ ] Exportação PDF/CSV

### 9.3 Insights com GPT-4
- [ ] Integração com OpenAI API
- [ ] Chat de insights diários
- [ ] Análise de tendências
- [ ] Sugestões de promoções
- [ ] Relatório automático de fechamento

---

## 🔧 **FASE 10: Funcionalidades Avançadas**

### 10.1 Backup e Sincronização
- [ ] Backup automático em pendrive/NAS
- [ ] Agendador de backups
- [ ] Restauração de dados
- [ ] Sincronização entre terminais

### 10.2 Configurações do Sistema
- [ ] Parâmetros gerais
- [ ] Configuração de impressoras
- [ ] Personalização de interface
- [ ] Configuração de impostos

### 10.3 Licenciamento
- [ ] Controle de limites (200 pedidos/mês gratuito)
- [ ] Validação de licença premium
- [ ] Múltiplos terminais
- [ ] Relatório de uso

---

## 🚀 **FASE 11: Deploy e Distribuição**

### 11.1 Build e Empacotamento
- [ ] Build do Electron para Windows/Linux/macOS
- [ ] Instalador NSIS (Windows)
- [ ] Pacote .deb (Linux)
- [ ] Pacote .pkg (macOS)

### 11.2 Testes
- [ ] Testes unitários (backend)
- [ ] Testes de integração
- [ ] Testes E2E (frontend)
- [ ] Testes de performance
- [ ] Testes de fiscal/TEF

### 11.3 Documentação
- [ ] Manual do usuário
- [ ] Guia de instalação
- [ ] Documentação técnica
- [ ] API documentation

---

## ✅ **Critérios de Aceitação**

### Funcionalidades Obrigatórias
- [ ] Sistema funciona 100% offline
- [ ] Emissão fiscal conforme legislação
- [ ] TEF integrado e funcional
- [ ] Backup automático funcionando
- [ ] Interface responsiva e intuitiva
- [ ] Performance adequada (< 2s para operações)

### Testes de Validação
- [ ] Teste completo de venda (balcão/mesa/delivery)
- [ ] Teste de emissão fiscal
- [ ] Teste de pagamento com cartão
- [ ] Teste de backup/restauração
- [ ] Teste de múltiplos usuários simultâneos
- [ ] Teste de insights com GPT-4

---

## 📝 **Notas Importantes**

- **Prioridade**: Funcionalidades core primeiro (PDV básico)
- **Offline-First**: Toda lógica crítica deve funcionar sem internet
- **Performance**: SQLite otimizado para operações frequentes
- **Segurança**: Dados fiscais e financeiros protegidos
- **Usabilidade**: Interface intuitiva para usuários não-técnicos

---

**Status**: 🔄 Em Desenvolvimento - Iniciando Fase 3 (Produtos e Estoque)
**Última Atualização**: 2024-12-19
