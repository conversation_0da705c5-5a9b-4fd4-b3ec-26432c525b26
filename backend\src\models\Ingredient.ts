import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  Ingredient, 
  CreateIngredientRequest, 
  UpdateIngredientRequest, 
  IngredientFilters 
} from '../types/products';

export class IngredientModel {
  // Buscar ingrediente por ID
  static async findById(id: number): Promise<Ingredient | null> {
    const ingredient = await executeQuerySingle<Ingredient>(
      'SELECT * FROM ingredients WHERE id = ?',
      [id]
    );
    return ingredient || null;
  }

  // Buscar ingrediente por código
  static async findByCode(code: string): Promise<Ingredient | null> {
    const ingredient = await executeQuerySingle<Ingredient>(
      'SELECT * FROM ingredients WHERE code = ? AND is_active = 1',
      [code]
    );
    return ingredient || null;
  }

  // Buscar ingrediente por nome
  static async findByName(name: string): Promise<Ingredient | null> {
    const ingredient = await executeQuerySingle<Ingredient>(
      'SELECT * FROM ingredients WHERE name = ? AND is_active = 1',
      [name]
    );
    return ingredient || null;
  }

  // Listar ingredientes com filtros
  static async findAll(filters: IngredientFilters = {}): Promise<Ingredient[]> {
    let query = 'SELECT * FROM ingredients WHERE 1=1';
    const params: any[] = [];

    if (filters.is_active !== undefined) {
      query += ' AND is_active = ?';
      params.push(filters.is_active ? 1 : 0);
    }

    if (filters.low_stock) {
      query += ' AND current_stock <= min_stock';
    }

    if (filters.expired) {
      query += ' AND expiry_date IS NOT NULL AND expiry_date <= DATE("now")';
    }

    if (filters.search) {
      query += ' AND (name LIKE ? OR description LIKE ? OR code LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY name ASC';

    return await executeQuery<Ingredient>(query, params);
  }

  // Criar ingrediente
  static async create(ingredientData: CreateIngredientRequest): Promise<Ingredient> {
    // Verificar se código já existe (se fornecido)
    if (ingredientData.code) {
      const existingIngredient = await this.findByCode(ingredientData.code);
      if (existingIngredient) {
        throw new Error('Ingredient code already exists');
      }
    }

    // Verificar se nome já existe
    const existingIngredient = await this.findByName(ingredientData.name);
    if (existingIngredient) {
      throw new Error('Ingredient name already exists');
    }

    // Gerar código automático se não fornecido
    let code = ingredientData.code;
    if (!code) {
      code = await this.generateIngredientCode();
    }

    // Inserir ingrediente
    const result = await executeUpdate(
      `INSERT INTO ingredients (
        code, name, description, unit, cost_per_unit, current_stock, 
        min_stock, supplier, expiry_date, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [
        code,
        ingredientData.name,
        ingredientData.description || null,
        ingredientData.unit,
        ingredientData.cost_per_unit,
        ingredientData.current_stock || 0,
        ingredientData.min_stock || 0,
        ingredientData.supplier || null,
        ingredientData.expiry_date || null,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create ingredient');
    }

    // Retornar ingrediente criado
    const newIngredient = await this.findById(result.lastID);
    if (!newIngredient) {
      throw new Error('Failed to retrieve created ingredient');
    }

    return newIngredient;
  }

  // Atualizar ingrediente
  static async update(id: number, ingredientData: UpdateIngredientRequest): Promise<Ingredient> {
    const ingredient = await this.findById(id);
    if (!ingredient) {
      throw new Error('Ingredient not found');
    }

    // Verificar se novo código já existe (se estiver sendo alterado)
    if (ingredientData.code && ingredientData.code !== ingredient.code) {
      const existingIngredient = await this.findByCode(ingredientData.code);
      if (existingIngredient) {
        throw new Error('Ingredient code already exists');
      }
    }

    // Verificar se novo nome já existe (se estiver sendo alterado)
    if (ingredientData.name && ingredientData.name !== ingredient.name) {
      const existingIngredient = await this.findByName(ingredientData.name);
      if (existingIngredient) {
        throw new Error('Ingredient name already exists');
      }
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = [
      'code', 'name', 'description', 'unit', 'cost_per_unit', 
      'current_stock', 'min_stock', 'supplier', 'expiry_date', 'is_active'
    ];

    updatableFields.forEach(field => {
      if (ingredientData[field as keyof UpdateIngredientRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = ingredientData[field as keyof UpdateIngredientRequest];
        
        // Converter boolean para integer
        if (field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return ingredient; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE ingredients SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar ingrediente atualizado
    const updatedIngredient = await this.findById(id);
    if (!updatedIngredient) {
      throw new Error('Failed to retrieve updated ingredient');
    }

    return updatedIngredient;
  }

  // Deletar ingrediente (soft delete)
  static async delete(id: number): Promise<void> {
    const ingredient = await this.findById(id);
    if (!ingredient) {
      throw new Error('Ingredient not found');
    }

    // Verificar se há receitas usando este ingrediente
    const recipesCount = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM recipes WHERE ingredient_id = ?',
      [id]
    );

    if (recipesCount && recipesCount.count > 0) {
      throw new Error('Cannot delete ingredient used in recipes');
    }

    await executeUpdate(
      'UPDATE ingredients SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Gerar código automático para ingrediente
  static async generateIngredientCode(): Promise<string> {
    const result = await executeQuerySingle<{ max_id: number }>(
      'SELECT COALESCE(MAX(id), 0) + 1 as max_id FROM ingredients'
    );
    
    const nextId = result?.max_id || 1;
    return `ING${nextId.toString().padStart(4, '0')}`;
  }

  // Atualizar estoque
  static async updateStock(id: number, quantity: number, operation: 'add' | 'subtract' | 'set'): Promise<void> {
    const ingredient = await this.findById(id);
    if (!ingredient) {
      throw new Error('Ingredient not found');
    }

    let newStock: number;
    
    switch (operation) {
      case 'add':
        newStock = ingredient.current_stock + quantity;
        break;
      case 'subtract':
        newStock = ingredient.current_stock - quantity;
        break;
      case 'set':
        newStock = quantity;
        break;
      default:
        throw new Error('Invalid stock operation');
    }

    // Verificar se permite estoque negativo
    if (newStock < 0) {
      throw new Error('Insufficient ingredient stock');
    }

    await executeUpdate(
      'UPDATE ingredients SET current_stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newStock, id]
    );
  }

  // Buscar ingredientes com estoque baixo
  static async findLowStock(): Promise<Ingredient[]> {
    return await executeQuery<Ingredient>(
      `SELECT * FROM ingredients
       WHERE current_stock <= min_stock 
         AND is_active = 1
       ORDER BY (current_stock - min_stock) ASC`
    );
  }

  // Buscar ingredientes vencidos
  static async findExpired(): Promise<Ingredient[]> {
    return await executeQuery<Ingredient>(
      `SELECT * FROM ingredients
       WHERE expiry_date IS NOT NULL 
         AND expiry_date <= DATE('now')
         AND is_active = 1
       ORDER BY expiry_date ASC`
    );
  }

  // Buscar ingredientes próximos ao vencimento
  static async findExpiringSoon(days: number = 7): Promise<Ingredient[]> {
    return await executeQuery<Ingredient>(
      `SELECT * FROM ingredients
       WHERE expiry_date IS NOT NULL 
         AND expiry_date <= DATE('now', '+${days} days')
         AND expiry_date > DATE('now')
         AND is_active = 1
       ORDER BY expiry_date ASC`
    );
  }

  // Verificar disponibilidade para receita
  static async checkAvailability(id: number, quantity: number): Promise<boolean> {
    const ingredient = await this.findById(id);
    if (!ingredient) {
      return false;
    }

    if (!ingredient.is_active) {
      return false;
    }

    return ingredient.current_stock >= quantity;
  }

  // Obter valor total do estoque
  static async getTotalStockValue(): Promise<number> {
    const result = await executeQuerySingle<{ total_value: number }>(
      'SELECT COALESCE(SUM(current_stock * cost_per_unit), 0) as total_value FROM ingredients WHERE is_active = 1'
    );
    
    return result?.total_value || 0;
  }

  // Buscar ingredientes por fornecedor
  static async findBySupplier(supplier: string): Promise<Ingredient[]> {
    return await executeQuery<Ingredient>(
      'SELECT * FROM ingredients WHERE supplier = ? AND is_active = 1 ORDER BY name ASC',
      [supplier]
    );
  }

  // Obter lista de fornecedores
  static async getSuppliers(): Promise<string[]> {
    const suppliers = await executeQuery<{ supplier: string }>(
      'SELECT DISTINCT supplier FROM ingredients WHERE supplier IS NOT NULL AND is_active = 1 ORDER BY supplier ASC'
    );
    
    return suppliers.map(s => s.supplier);
  }
}
