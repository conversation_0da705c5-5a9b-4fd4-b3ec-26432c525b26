import { Request, Response } from 'express';
import { TefTransactionModel } from '../models/TefTransaction';
import { TefConfigModel } from '../models/TefConfig';
import { 
  CreateTefTransactionRequest, 
  UpdateTefTransactionRequest, 
  CancelTefTransactionRequest,
  TefTransactionFilters,
  CreateTefConfigRequest,
  UpdateTefConfigRequest,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class TefController {
  // ===== TRANSAÇÕES TEF =====

  // Listar transações TEF
  static async getTransactions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: TefTransactionFilters = {
        type: req.query.type as any,
        operation: req.query.operation as any,
        status: req.query.status as any,
        card_type: req.query.card_type as any,
        order_id: req.query.order_id ? parseInt(req.query.order_id as string) : undefined,
        cash_session_id: req.query.cash_session_id ? parseInt(req.query.cash_session_id as string) : undefined,
        user_id: req.query.user_id ? parseInt(req.query.user_id as string) : undefined,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        search: req.query.search as string,
      };

      const transactions = await TefTransactionModel.findAll(filters);

      res.status(200).json({
        success: true,
        data: transactions,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get TEF transactions error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get TEF transactions',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar transação TEF por ID
  static async getTransaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid TEF transaction ID');
      }

      const transaction = await TefTransactionModel.findById(id);
      if (!transaction) {
        throw new NotFoundError('TEF transaction not found');
      }

      res.status(200).json({
        success: true,
        data: transaction,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get TEF transaction error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get TEF transaction',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Criar transação TEF
  static async createTransaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const transactionData: CreateTefTransactionRequest = req.body;

      // Validações básicas
      if (!transactionData.type) {
        throw new ValidationError('Transaction type is required');
      }

      if (!transactionData.operation) {
        throw new ValidationError('Operation is required');
      }

      if (transactionData.amount <= 0) {
        throw new ValidationError('Amount must be greater than 0');
      }

      const transaction = await TefTransactionModel.create(transactionData, req.user!.id);

      console.log(`TEF transaction created: ${transaction.id} by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: transaction,
        message: 'TEF transaction created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create TEF transaction error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create TEF transaction',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar transação TEF
  static async updateTransaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const transactionData: UpdateTefTransactionRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid TEF transaction ID');
      }

      const transaction = await TefTransactionModel.update(id, transactionData);

      console.log(`TEF transaction ${id} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'TEF transaction updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update TEF transaction error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update TEF transaction',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Cancelar transação TEF
  static async cancelTransaction(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const cancelData: CancelTefTransactionRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid TEF transaction ID');
      }

      if (!cancelData.reason || cancelData.reason.trim().length === 0) {
        throw new ValidationError('Cancellation reason is required');
      }

      // Verificar se pode cancelar
      const canCancel = await TefTransactionModel.canCancel(id);
      if (!canCancel.can_cancel) {
        throw new ValidationError(canCancel.reason || 'Transaction cannot be cancelled');
      }

      const transaction = await TefTransactionModel.cancel(id, cancelData, req.user!.id);

      console.log(`TEF transaction ${id} cancelled by user ${req.user?.username}: ${cancelData.reason}`);

      res.status(200).json({
        success: true,
        data: transaction,
        message: 'TEF transaction cancelled successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Cancel TEF transaction error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to cancel TEF transaction',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter métricas TEF
  static async getMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = await TefTransactionModel.getMetrics();

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get TEF metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get TEF metrics',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== CONFIGURAÇÕES TEF =====

  // Listar configurações TEF
  static async getConfigs(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const activeOnly = req.query.active_only !== 'false';
      const configs = await TefConfigModel.findAll(activeOnly);

      res.status(200).json({
        success: true,
        data: configs,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get TEF configs error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get TEF configs',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar configuração ativa
  static async getActiveConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const config = await TefConfigModel.findActive();

      if (!config) {
        res.status(200).json({
          success: true,
          data: null,
          message: 'No active TEF configuration found',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
        return;
      }

      res.status(200).json({
        success: true,
        data: config,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get active TEF config error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get active TEF config',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Validar configuração TEF
  static async validateConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = req.params.id ? parseInt(req.params.id) : undefined;
      
      if (id && isNaN(id)) {
        throw new ValidationError('Invalid TEF config ID');
      }

      const validation = await TefConfigModel.validate(id);

      res.status(200).json({
        success: true,
        data: validation,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Validate TEF config error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to validate TEF config',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Testar conexão com provedor
  static async testProviderConnection(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = req.params.id ? parseInt(req.params.id) : undefined;
      
      if (id && isNaN(id)) {
        throw new ValidationError('Invalid TEF config ID');
      }

      const result = await TefConfigModel.testProviderConnection(id);

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Test provider connection error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to test provider connection',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Testar conexão com PinPad
  static async testPinPadConnection(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = req.params.id ? parseInt(req.params.id) : undefined;
      
      if (id && isNaN(id)) {
        throw new ValidationError('Invalid TEF config ID');
      }

      const result = await TefConfigModel.testPinPadConnection(id);

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Test PinPad connection error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to test PinPad connection',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
