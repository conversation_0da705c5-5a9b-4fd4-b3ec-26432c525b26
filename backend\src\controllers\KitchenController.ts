import { Request, Response } from 'express';
import { KitchenOrderModel } from '../models/KitchenOrder';
import { KitchenStationModel } from '../models/KitchenStation';
import { 
  CreateKitchenOrderRequest, 
  UpdateKitchenOrderRequest, 
  KitchenOrderFilters,
  UpdateKitchenItemRequest,
  CreateKitchenStationRequest,
  UpdateKitchenStationRequest,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class KitchenController {
  // ===== PEDIDOS DA COZINHA =====

  // Listar pedidos da cozinha
  static async getOrders(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: KitchenOrderFilters = {
        status: req.query.status as any,
        priority: req.query.priority as any,
        order_type: req.query.order_type as any,
        station_id: req.query.station_id ? parseInt(req.query.station_id as string) : undefined,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        search: req.query.search as string,
      };

      const orders = await KitchenOrderModel.findAll(filters);

      res.status(200).json({
        success: true,
        data: orders,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get kitchen orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get kitchen orders',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar pedido da cozinha por ID
  static async getOrder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid kitchen order ID');
      }

      const order = await KitchenOrderModel.findById(id);
      if (!order) {
        throw new NotFoundError('Kitchen order not found');
      }

      // Incluir itens se solicitado
      if (req.query.include_items === 'true') {
        (order as any).items = await KitchenOrderModel.getKitchenOrderItems(id);
      }

      res.status(200).json({
        success: true,
        data: order,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get kitchen order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get kitchen order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Criar pedido da cozinha
  static async createOrder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orderData: CreateKitchenOrderRequest = req.body;

      // Validações básicas
      if (!orderData.order_id) {
        throw new ValidationError('Order ID is required');
      }

      const order = await KitchenOrderModel.create(orderData);

      console.log(`Kitchen order created for order ${orderData.order_id} by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: order,
        message: 'Kitchen order created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create kitchen order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create kitchen order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar pedido da cozinha
  static async updateOrder(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const orderData: UpdateKitchenOrderRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid kitchen order ID');
      }

      const order = await KitchenOrderModel.update(id, orderData);

      console.log(`Kitchen order ${id} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: order,
        message: 'Kitchen order updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update kitchen order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update kitchen order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar pedidos ativos
  static async getActiveOrders(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orders = await KitchenOrderModel.findActive();

      res.status(200).json({
        success: true,
        data: orders,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get active kitchen orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get active kitchen orders',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar pedidos prontos
  static async getReadyOrders(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orders = await KitchenOrderModel.findReady();

      res.status(200).json({
        success: true,
        data: orders,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get ready kitchen orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get ready kitchen orders',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar itens do pedido da cozinha
  static async getOrderItems(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid kitchen order ID');
      }

      const items = await KitchenOrderModel.getKitchenOrderItems(id);

      res.status(200).json({
        success: true,
        data: items,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get kitchen order items error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get kitchen order items',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar item da cozinha
  static async updateOrderItem(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const itemId = parseInt(req.params.itemId);
      const itemData: UpdateKitchenItemRequest = req.body;

      if (isNaN(itemId)) {
        throw new ValidationError('Invalid kitchen order item ID');
      }

      const item = await KitchenOrderModel.updateItem(itemId, itemData);

      console.log(`Kitchen order item ${itemId} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: item,
        message: 'Kitchen order item updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update kitchen order item error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update kitchen order item',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter métricas da cozinha
  static async getMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = await KitchenOrderModel.getMetrics();

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get kitchen metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get kitchen metrics',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== ESTAÇÕES DA COZINHA =====

  // Listar estações
  static async getStations(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const activeOnly = req.query.active_only !== 'false';
      const withWorkload = req.query.with_workload === 'true';

      let stations;
      if (withWorkload) {
        stations = await KitchenStationModel.findWithWorkload();
      } else {
        stations = await KitchenStationModel.findAll(activeOnly);
      }

      res.status(200).json({
        success: true,
        data: stations,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get kitchen stations error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get kitchen stations',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar estação por ID
  static async getStation(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid station ID');
      }

      const station = await KitchenStationModel.findById(id);
      if (!station) {
        throw new NotFoundError('Kitchen station not found');
      }

      // Incluir estatísticas se solicitado
      if (req.query.with_stats === 'true') {
        const dateFrom = req.query.date_from as string;
        const dateTo = req.query.date_to as string;
        const stats = await KitchenStationModel.getStats(id, dateFrom, dateTo);
        
        res.status(200).json({
          success: true,
          data: { ...station, stats },
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(200).json({
          success: true,
          data: station,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }

    } catch (error) {
      console.error('Get kitchen station error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get kitchen station',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
