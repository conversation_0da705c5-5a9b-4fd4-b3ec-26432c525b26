#!/usr/bin/env node

/**
 * Script para testar o sistema PDV Adib
 * Verifica se todos os componentes estão funcionando
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🧪 Testando Sistema PDV Adib...\n');

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function checkFile(filePath, description) {
  if (fs.existsSync(filePath)) {
    log(`✅ ${description}`, 'green');
    return true;
  } else {
    log(`❌ ${description} - Arquivo não encontrado: ${filePath}`, 'red');
    return false;
  }
}

function checkDirectory(dirPath, description) {
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    log(`✅ ${description}`, 'green');
    return true;
  } else {
    log(`❌ ${description} - Diretório não encontrado: ${dirPath}`, 'red');
    return false;
  }
}

async function testDatabase() {
  log('\n📊 Testando Banco de Dados...', 'blue');
  
  const dbPath = path.join(__dirname, '..', 'database', 'adib.db');
  
  if (!checkFile(dbPath, 'Arquivo do banco de dados')) {
    log('Execute: node scripts/init-db.js', 'yellow');
    return false;
  }

  try {
    const sqlite3 = require('sqlite3').verbose();
    const db = new sqlite3.Database(dbPath);
    
    return new Promise((resolve) => {
      db.get("SELECT COUNT(*) as count FROM users", (err, result) => {
        if (err) {
          log(`❌ Erro ao consultar banco: ${err.message}`, 'red');
          resolve(false);
        } else {
          log(`✅ Banco funcionando - ${result.count} usuários encontrados`, 'green');
          resolve(true);
        }
        db.close();
      });
    });
  } catch (error) {
    log(`❌ Erro ao conectar com banco: ${error.message}`, 'red');
    return false;
  }
}

async function testBackend() {
  log('\n🔧 Testando Backend...', 'blue');
  
  // Verificar arquivos essenciais
  const backendFiles = [
    ['backend/package.json', 'Package.json do backend'],
    ['backend/src/server.ts', 'Servidor principal'],
    ['backend/src/types/index.ts', 'Tipos TypeScript'],
    ['backend/src/models/User.ts', 'Model de usuário'],
    ['backend/src/services/AuthService.ts', 'Serviço de autenticação'],
    ['backend/src/controllers/AuthController.ts', 'Controller de auth'],
    ['backend/src/routes/auth.ts', 'Rotas de autenticação'],
    ['backend/src/middleware/auth.ts', 'Middleware de auth'],
    ['backend/.env', 'Arquivo de configuração'],
  ];

  let allFilesExist = true;
  backendFiles.forEach(([file, desc]) => {
    if (!checkFile(file, desc)) {
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

async function testFrontend() {
  log('\n🎨 Testando Frontend...', 'blue');
  
  // Verificar arquivos essenciais
  const frontendFiles = [
    ['frontend/package.json', 'Package.json do frontend'],
    ['frontend/public/index.html', 'HTML principal'],
    ['frontend/public/electron.js', 'Electron main process'],
    ['frontend/public/preload.js', 'Electron preload'],
    ['frontend/src/App.tsx', 'Componente principal'],
    ['frontend/src/index.tsx', 'Entry point'],
    ['frontend/src/types/index.ts', 'Tipos TypeScript'],
    ['frontend/src/services/api.ts', 'Serviço de API'],
    ['frontend/src/services/auth.ts', 'Store de autenticação'],
    ['frontend/src/pages/Login.tsx', 'Página de login'],
    ['frontend/src/pages/Dashboard.tsx', 'Dashboard'],
    ['frontend/src/components/ProtectedRoute.tsx', 'Rota protegida'],
    ['frontend/src/components/ErrorBoundary.tsx', 'Error boundary'],
  ];

  let allFilesExist = true;
  frontendFiles.forEach(([file, desc]) => {
    if (!checkFile(file, desc)) {
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

async function testFiscal() {
  log('\n📄 Testando Módulo Fiscal...', 'blue');
  
  // Verificar arquivos essenciais
  const fiscalFiles = [
    ['fiscal/requirements.txt', 'Dependências Python'],
    ['fiscal/src/main.py', 'API FastAPI principal'],
  ];

  let allFilesExist = true;
  fiscalFiles.forEach(([file, desc]) => {
    if (!checkFile(file, desc)) {
      allFilesExist = false;
    }
  });

  return allFilesExist;
}

async function testStructure() {
  log('\n📁 Testando Estrutura de Diretórios...', 'blue');
  
  const directories = [
    ['frontend', 'Diretório do frontend'],
    ['backend', 'Diretório do backend'],
    ['fiscal', 'Diretório fiscal'],
    ['database', 'Diretório do banco'],
    ['scripts', 'Diretório de scripts'],
    ['docs', 'Diretório de documentação'],
  ];

  let allDirsExist = true;
  directories.forEach(([dir, desc]) => {
    if (!checkDirectory(dir, desc)) {
      allDirsExist = false;
    }
  });

  return allDirsExist;
}

async function testConfiguration() {
  log('\n⚙️ Testando Configurações...', 'blue');
  
  const configFiles = [
    ['package.json', 'Package.json principal'],
    ['.gitignore', 'Git ignore'],
    ['.prettierrc', 'Prettier config'],
    ['README.md', 'Documentação'],
    ['checklist.md', 'Checklist de desenvolvimento'],
  ];

  let allConfigsExist = true;
  configFiles.forEach(([file, desc]) => {
    if (!checkFile(file, desc)) {
      allConfigsExist = false;
    }
  });

  return allConfigsExist;
}

async function main() {
  try {
    log('🏪 Sistema PDV Adib - Teste de Integridade\n', 'cyan');

    const results = {
      structure: await testStructure(),
      configuration: await testConfiguration(),
      database: await testDatabase(),
      backend: await testBackend(),
      frontend: await testFrontend(),
      fiscal: await testFiscal(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! Sistema pronto para uso.', 'green');
      log('\nPróximos passos:', 'cyan');
      log('1. Instalar dependências: npm run setup', 'yellow');
      log('2. Inicializar banco: node scripts/init-db.js', 'yellow');
      log('3. Iniciar desenvolvimento: npm run dev', 'yellow');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
      log('Execute o setup novamente se necessário: npm run setup', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
