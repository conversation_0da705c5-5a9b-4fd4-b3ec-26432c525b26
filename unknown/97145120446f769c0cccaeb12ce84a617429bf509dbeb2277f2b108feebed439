#!/usr/bin/env node

/**
 * Script para testar as APIs de Relatórios e Analytics
 * Testa geração de relatórios e insights com IA
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar relatórios operacionais
async function testOperationalReports() {
  log('\n📊 Testando Relatórios Operacionais...', 'blue');

  try {
    const today = new Date().toISOString().slice(0, 10);
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().slice(0, 10);

    // Relatório de vendas
    log('Gerando relatório de vendas...', 'cyan');
    const salesResponse = await axios.get(`${API_BASE_URL}/reports/sales?date_from=${yesterday}&date_to=${today}`, {
      headers: getHeaders()
    });
    const salesReport = salesResponse.data.data;
    log(`✅ Relatório de vendas gerado:`, 'green');
    log(`   - Total de pedidos: ${salesReport.summary.total_orders}`, 'cyan');
    log(`   - Receita total: R$ ${salesReport.summary.total_amount.toFixed(2)}`, 'cyan');
    log(`   - Ticket médio: R$ ${salesReport.summary.average_ticket.toFixed(2)}`, 'cyan');

    // Relatório de produtos
    log('Gerando relatório de produtos...', 'cyan');
    const productsResponse = await axios.get(`${API_BASE_URL}/reports/products?date_from=${yesterday}&date_to=${today}`, {
      headers: getHeaders()
    });
    const productsReport = productsResponse.data.data;
    log(`✅ Relatório de produtos gerado:`, 'green');
    log(`   - Total de produtos: ${productsReport.summary.total_products}`, 'cyan');
    log(`   - Produtos vendidos: ${productsReport.summary.products_sold}`, 'cyan');
    log(`   - Quantidade total: ${productsReport.summary.total_quantity}`, 'cyan');

    // Relatório financeiro
    log('Gerando relatório financeiro...', 'cyan');
    const financialResponse = await axios.get(`${API_BASE_URL}/reports/financial?date_from=${yesterday}&date_to=${today}`, {
      headers: getHeaders()
    });
    const financialReport = financialResponse.data.data;
    log(`✅ Relatório financeiro gerado:`, 'green');
    log(`   - Receita bruta: R$ ${financialReport.summary.gross_revenue.toFixed(2)}`, 'cyan');
    log(`   - Receita líquida: R$ ${financialReport.summary.net_revenue.toFixed(2)}`, 'cyan');
    log(`   - Margem de lucro: ${financialReport.summary.margin.toFixed(1)}%`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de relatórios: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar dashboard executivo
async function testExecutiveDashboard() {
  log('\n📈 Testando Dashboard Executivo...', 'blue');

  try {
    const today = new Date().toISOString().slice(0, 10);
    
    log('Gerando dashboard executivo...', 'cyan');
    const dashboardResponse = await axios.get(`${API_BASE_URL}/reports/executive-dashboard?date_from=${today}&date_to=${today}`, {
      headers: getHeaders()
    });
    const dashboard = dashboardResponse.data.data;
    
    log(`✅ Dashboard executivo gerado:`, 'green');
    log(`   - Receita atual: R$ ${dashboard.kpis.revenue.current.toFixed(2)} (${dashboard.kpis.revenue.growth > 0 ? '+' : ''}${dashboard.kpis.revenue.growth.toFixed(1)}%)`, 'cyan');
    log(`   - Pedidos: ${dashboard.kpis.orders.current} (${dashboard.kpis.orders.growth > 0 ? '+' : ''}${dashboard.kpis.orders.growth.toFixed(1)}%)`, 'cyan');
    log(`   - Ticket médio: R$ ${dashboard.kpis.average_ticket.current.toFixed(2)} (${dashboard.kpis.average_ticket.growth > 0 ? '+' : ''}${dashboard.kpis.average_ticket.growth.toFixed(1)}%)`, 'cyan');
    log(`   - Alertas: ${dashboard.alerts.length}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de dashboard: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar analytics com IA
async function testAnalyticsAI() {
  log('\n🤖 Testando Analytics com IA...', 'blue');

  try {
    const today = new Date().toISOString().slice(0, 10);
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().slice(0, 10);

    // Gerar insights completos
    log('Gerando insights com IA...', 'cyan');
    const analyticsRequest = {
      date_from: yesterday,
      date_to: today,
      focus_areas: ['sales', 'products', 'finance', 'operations'],
      include_predictions: true,
      detail_level: 'detailed'
    };

    const analyticsResponse = await axios.post(`${API_BASE_URL}/reports/analytics/insights`, analyticsRequest, {
      headers: getHeaders()
    });
    const analytics = analyticsResponse.data.data;
    
    log(`✅ Analytics gerado com sucesso:`, 'green');
    log(`   - Total de insights: ${analytics.summary.total_insights}`, 'cyan');
    log(`   - Insights de alto impacto: ${analytics.summary.high_impact_count}`, 'cyan');
    log(`   - Confiança média: ${(analytics.summary.confidence_average * 100).toFixed(1)}%`, 'cyan');
    log(`   - Predições: ${analytics.predictions.length}`, 'cyan');
    log(`   - Recomendações: ${analytics.recommendations.length}`, 'cyan');

    // Mostrar alguns insights
    if (analytics.insights.length > 0) {
      log(`\n📝 Insights gerados:`, 'yellow');
      analytics.insights.slice(0, 3).forEach((insight, index) => {
        log(`   ${index + 1}. ${insight.title} (${insight.impact} impact)`, 'cyan');
        log(`      ${insight.description}`, 'cyan');
      });
    }

    // Quick insights
    log('\nBuscando insights rápidos...', 'cyan');
    const quickInsightsResponse = await axios.get(`${API_BASE_URL}/reports/analytics/quick-insights`, {
      headers: getHeaders()
    });
    const quickInsights = quickInsightsResponse.data.data;
    
    log(`✅ Quick insights obtidos: ${quickInsights.insights.length} insights`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de analytics: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar comparações e métricas
async function testComparisonsAndMetrics() {
  log('\n📊 Testando Comparações e Métricas...', 'blue');

  try {
    const today = new Date().toISOString().slice(0, 10);
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
    const twoDaysAgo = new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);
    const threeDaysAgo = new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString().slice(0, 10);

    // Comparação de períodos
    log('Comparando períodos...', 'cyan');
    const comparisonResponse = await axios.get(
      `${API_BASE_URL}/reports/compare-periods?current_start=${yesterday}&current_end=${today}&previous_start=${threeDaysAgo}&previous_end=${twoDaysAgo}`,
      { headers: getHeaders() }
    );
    const comparison = comparisonResponse.data.data;
    
    log(`✅ Comparação de períodos:`, 'green');
    log(`   - Crescimento de receita: ${comparison.comparison.revenue_growth.toFixed(1)}%`, 'cyan');
    log(`   - Crescimento de pedidos: ${comparison.comparison.orders_growth.toFixed(1)}%`, 'cyan');
    log(`   - Mudanças significativas: ${comparison.comparison.significant_changes.length}`, 'cyan');

    // Métricas de performance
    log('Obtendo métricas de performance...', 'cyan');
    const metricsResponse = await axios.get(`${API_BASE_URL}/reports/performance-metrics?date_from=${yesterday}&date_to=${today}`, {
      headers: getHeaders()
    });
    const metrics = metricsResponse.data.data;
    
    log(`✅ Métricas de performance:`, 'green');
    log(`   - Pedidos por hora: ${metrics.operational.orders_per_hour.toFixed(1)}`, 'cyan');
    log(`   - Receita por assento: R$ ${metrics.financial.revenue_per_seat.toFixed(2)}`, 'cyan');
    log(`   - Margem de lucro: ${metrics.financial.profit_margin.toFixed(1)}%`, 'cyan');
    log(`   - Score de satisfação: ${metrics.customer.satisfaction_score.toFixed(1)}/5`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de comparações: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar exportação
async function testExportation() {
  log('\n📤 Testando Exportação...', 'blue');

  try {
    // Exportar relatório de vendas
    log('Exportando relatório de vendas...', 'cyan');
    const exportResponse = await axios.get(`${API_BASE_URL}/reports/export/sales?format=pdf`, {
      headers: getHeaders()
    });
    const exportData = exportResponse.data.data;
    
    log(`✅ Relatório exportado:`, 'green');
    log(`   - Arquivo: ${exportData.file_name}`, 'cyan');
    log(`   - Tamanho: ${(exportData.file_size / 1024).toFixed(1)} KB`, 'cyan');
    log(`   - URL: ${exportData.download_url}`, 'cyan');
    log(`   - Expira em: ${new Date(exportData.expires_at).toLocaleString('pt-BR')}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de exportação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar comparar períodos sem datas
    log('Testando comparação sem datas...', 'cyan');
    try {
      await axios.get(`${API_BASE_URL}/reports/compare-periods`, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de datas funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar exportar tipo inválido
    log('Testando exportação com tipo inválido...', 'cyan');
    try {
      await axios.get(`${API_BASE_URL}/reports/export/invalid_type`, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de tipo de relatório funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar analytics com áreas inválidas
    log('Testando analytics com áreas inválidas...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/reports/analytics/insights`, {
        focus_areas: ['invalid_area']
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de áreas de foco funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs de Relatórios e Analytics - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      operational_reports: await testOperationalReports(),
      executive_dashboard: await testExecutiveDashboard(),
      analytics_ai: await testAnalyticsAI(),
      comparisons_metrics: await testComparisonsAndMetrics(),
      exportation: await testExportation(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase().replace(/_/g, ' ')}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs de Relatórios e Analytics funcionando corretamente.', 'green');
      log('\n🤖 Sistema pronto para gerar insights automáticos com IA!', 'cyan');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
