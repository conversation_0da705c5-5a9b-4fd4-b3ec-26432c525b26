import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Assessment,
  TrendingUp,
  TrendingDown,
  Download,
  Refresh,
  DateRange,
  BarChart,
  PieChart,
  ShowChart,
  Lightbulb,
  Warning,
  CheckCircle,
  Error,
  ExpandMore,
  AutoAwesome,
  Analytics,
} from '@mui/icons-material';
import { useAuthStore } from '../services/auth';

interface SalesReport {
  period: { start: string; end: string };
  summary: {
    total_orders: number;
    total_amount: number;
    average_ticket: number;
    total_items: number;
  };
  by_hour: Array<{ hour: number; orders: number; amount: number }>;
  top_products: Array<{ product_name: string; quantity: number; amount: number }>;
}

interface AnalyticsInsight {
  id: string;
  type: string;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: string;
  recommendations: string[];
  confidence: number;
}

interface ExecutiveDashboard {
  kpis: {
    revenue: { current: number; growth: number; trend: 'up' | 'down' | 'stable' };
    orders: { current: number; growth: number; trend: 'up' | 'down' | 'stable' };
    average_ticket: { current: number; growth: number; trend: 'up' | 'down' | 'stable' };
    profit_margin: { current: number; growth: number; trend: 'up' | 'down' | 'stable' };
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    priority: 'high' | 'medium' | 'low';
  }>;
}

const Reports: React.FC = () => {
  const [selectedTab, setSelectedTab] = useState(0);
  const [dateFrom, setDateFrom] = useState(new Date().toISOString().slice(0, 10));
  const [dateTo, setDateTo] = useState(new Date().toISOString().slice(0, 10));
  const [salesReport, setSalesReport] = useState<SalesReport | null>(null);
  const [insights, setInsights] = useState<AnalyticsInsight[]>([]);
  const [dashboard, setDashboard] = useState<ExecutiveDashboard | null>(null);
  const [loading, setLoading] = useState(false);
  const [generatingInsights, setGeneratingInsights] = useState(false);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular relatório de vendas
        const mockSalesReport: SalesReport = {
          period: { start: dateFrom, end: dateTo },
          summary: {
            total_orders: 156,
            total_amount: 8450.75,
            average_ticket: 54.17,
            total_items: 312,
          },
          by_hour: [
            { hour: 11, orders: 8, amount: 432.50 },
            { hour: 12, orders: 15, amount: 812.25 },
            { hour: 13, orders: 12, amount: 649.80 },
            { hour: 18, orders: 22, amount: 1189.60 },
            { hour: 19, orders: 28, amount: 1516.40 },
            { hour: 20, orders: 25, amount: 1352.75 },
            { hour: 21, orders: 18, amount: 974.70 },
          ],
          top_products: [
            { product_name: 'Hambúrguer Artesanal', quantity: 45, amount: 1575.00 },
            { product_name: 'Pizza Margherita', quantity: 32, amount: 1280.00 },
            { product_name: 'Batata Frita', quantity: 67, amount: 670.00 },
            { product_name: 'Refrigerante', quantity: 89, amount: 445.00 },
            { product_name: 'Salada Caesar', quantity: 28, amount: 840.00 },
          ],
        };

        // Simular dashboard executivo
        const mockDashboard: ExecutiveDashboard = {
          kpis: {
            revenue: { current: 8450.75, growth: 12.5, trend: 'up' },
            orders: { current: 156, growth: 8.3, trend: 'up' },
            average_ticket: { current: 54.17, growth: 3.8, trend: 'up' },
            profit_margin: { current: 28.5, growth: -2.1, trend: 'down' },
          },
          alerts: [
            { type: 'warning', message: 'Estoque baixo em 3 produtos', priority: 'medium' },
            { type: 'info', message: 'Pico de vendas às 19h', priority: 'low' },
            { type: 'error', message: 'Margem de lucro abaixo da meta', priority: 'high' },
          ],
        };

        setSalesReport(mockSalesReport);
        setDashboard(mockDashboard);
      } catch (error) {
        console.error('Error loading reports data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [dateFrom, dateTo]);

  // Gerar insights com IA
  const generateInsights = async () => {
    try {
      setGeneratingInsights(true);
      
      // Simular geração de insights
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const mockInsights: AnalyticsInsight[] = [
        {
          id: '1',
          type: 'revenue_opportunity',
          title: 'Oportunidade de Aumento do Ticket Médio',
          description: 'O ticket médio atual (R$ 54,17) pode ser aumentado através de estratégias de upselling.',
          impact: 'high',
          category: 'sales',
          recommendations: [
            'Implementar combos promocionais',
            'Treinar equipe em técnicas de venda',
            'Criar menu de sobremesas atrativo',
          ],
          confidence: 0.85,
        },
        {
          id: '2',
          type: 'performance_optimization',
          title: 'Pico de Vendas Identificado',
          description: 'O horário de 19h representa o maior volume de vendas do dia.',
          impact: 'medium',
          category: 'operations',
          recommendations: [
            'Garantir equipe completa no horário de pico',
            'Preparar ingredientes antecipadamente',
            'Considerar promoções em horários de menor movimento',
          ],
          confidence: 0.92,
        },
        {
          id: '3',
          type: 'cost_reduction',
          title: 'Margem de Lucro em Declínio',
          description: 'A margem de lucro diminuiu 2,1% em relação ao período anterior.',
          impact: 'high',
          category: 'finance',
          recommendations: [
            'Revisar preços dos produtos',
            'Negociar melhores condições com fornecedores',
            'Otimizar processos para reduzir desperdícios',
          ],
          confidence: 0.78,
        },
      ];

      setInsights(mockInsights);
    } catch (error) {
      console.error('Error generating insights:', error);
    } finally {
      setGeneratingInsights(false);
    }
  };

  // Formatar moeda
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  // Obter cor do impacto
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  // Obter ícone do trend
  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp color="success" />;
      case 'down': return <TrendingDown color="error" />;
      default: return <ShowChart color="info" />;
    }
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              Relatórios e Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <TextField
                type="date"
                label="De"
                value={dateFrom}
                onChange={(e) => setDateFrom(e.target.value)}
                size="small"
                InputLabelProps={{ shrink: true }}
              />
              <TextField
                type="date"
                label="Até"
                value={dateTo}
                onChange={(e) => setDateTo(e.target.value)}
                size="small"
                InputLabelProps={{ shrink: true }}
              />
              <IconButton>
                <Refresh />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Dashboard Executivo" />
          <Tab label="Relatório de Vendas" />
          <Tab label="Analytics com IA" />
          <Tab label="Exportar" />
        </Tabs>

        {/* Tab Dashboard Executivo */}
        {selectedTab === 0 && dashboard && (
          <Box sx={{ p: 2 }}>
            {/* KPIs */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                      {getTrendIcon(dashboard.kpis.revenue.trend)}
                      <Typography variant="h4" sx={{ ml: 1 }}>
                        {formatCurrency(dashboard.kpis.revenue.current)}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Receita
                    </Typography>
                    <Chip
                      label={`${dashboard.kpis.revenue.growth > 0 ? '+' : ''}${dashboard.kpis.revenue.growth.toFixed(1)}%`}
                      color={dashboard.kpis.revenue.growth > 0 ? 'success' : 'error'}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                      {getTrendIcon(dashboard.kpis.orders.trend)}
                      <Typography variant="h4" sx={{ ml: 1 }}>
                        {dashboard.kpis.orders.current}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Pedidos
                    </Typography>
                    <Chip
                      label={`${dashboard.kpis.orders.growth > 0 ? '+' : ''}${dashboard.kpis.orders.growth.toFixed(1)}%`}
                      color={dashboard.kpis.orders.growth > 0 ? 'success' : 'error'}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                      {getTrendIcon(dashboard.kpis.average_ticket.trend)}
                      <Typography variant="h4" sx={{ ml: 1 }}>
                        {formatCurrency(dashboard.kpis.average_ticket.current)}
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Ticket Médio
                    </Typography>
                    <Chip
                      label={`${dashboard.kpis.average_ticket.growth > 0 ? '+' : ''}${dashboard.kpis.average_ticket.growth.toFixed(1)}%`}
                      color={dashboard.kpis.average_ticket.growth > 0 ? 'success' : 'error'}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 1 }}>
                      {getTrendIcon(dashboard.kpis.profit_margin.trend)}
                      <Typography variant="h4" sx={{ ml: 1 }}>
                        {dashboard.kpis.profit_margin.current.toFixed(1)}%
                      </Typography>
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      Margem de Lucro
                    </Typography>
                    <Chip
                      label={`${dashboard.kpis.profit_margin.growth > 0 ? '+' : ''}${dashboard.kpis.profit_margin.growth.toFixed(1)}%`}
                      color={dashboard.kpis.profit_margin.growth > 0 ? 'success' : 'error'}
                      size="small"
                      sx={{ mt: 1 }}
                    />
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            {/* Alertas */}
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Alertas do Sistema
                </Typography>
                <List>
                  {dashboard.alerts.map((alert, index) => (
                    <ListItem key={index}>
                      <ListItemIcon>
                        {alert.type === 'error' && <Error color="error" />}
                        {alert.type === 'warning' && <Warning color="warning" />}
                        {alert.type === 'info' && <CheckCircle color="info" />}
                      </ListItemIcon>
                      <ListItemText
                        primary={alert.message}
                        secondary={`Prioridade: ${alert.priority}`}
                      />
                      <Chip
                        label={alert.priority}
                        color={alert.priority === 'high' ? 'error' : alert.priority === 'medium' ? 'warning' : 'default'}
                        size="small"
                      />
                    </ListItem>
                  ))}
                </List>
              </CardContent>
            </Card>
          </Box>
        )}

        {/* Tab Relatório de Vendas */}
        {selectedTab === 1 && salesReport && (
          <Box sx={{ p: 2 }}>
            {/* Resumo */}
            <Grid container spacing={3} sx={{ mb: 3 }}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <BarChart sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h4" color="primary">
                      {salesReport.summary.total_orders}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total de Pedidos
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Assessment sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {formatCurrency(salesReport.summary.total_amount)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Receita Total
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <ShowChart sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                    <Typography variant="h4" color="info.main">
                      {formatCurrency(salesReport.summary.average_ticket)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Ticket Médio
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <PieChart sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {salesReport.summary.total_items}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Itens Vendidos
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Grid container spacing={3}>
              {/* Vendas por Hora */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Vendas por Hora
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Hora</TableCell>
                            <TableCell align="right">Pedidos</TableCell>
                            <TableCell align="right">Receita</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {salesReport.by_hour.map((hour) => (
                            <TableRow key={hour.hour}>
                              <TableCell>{hour.hour}:00</TableCell>
                              <TableCell align="right">{hour.orders}</TableCell>
                              <TableCell align="right">{formatCurrency(hour.amount)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>

              {/* Top Produtos */}
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Produtos Mais Vendidos
                    </Typography>
                    <TableContainer>
                      <Table size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Produto</TableCell>
                            <TableCell align="right">Qtd</TableCell>
                            <TableCell align="right">Receita</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {salesReport.top_products.map((product, index) => (
                            <TableRow key={index}>
                              <TableCell>{product.product_name}</TableCell>
                              <TableCell align="right">{product.quantity}</TableCell>
                              <TableCell align="right">{formatCurrency(product.amount)}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Tab Analytics com IA */}
        {selectedTab === 2 && (
          <Box sx={{ p: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6">
                Insights Gerados por IA
              </Typography>
              <Button
                variant="contained"
                startIcon={<AutoAwesome />}
                onClick={generateInsights}
                disabled={generatingInsights}
              >
                {generatingInsights ? 'Gerando...' : 'Gerar Insights'}
              </Button>
            </Box>

            {generatingInsights && (
              <Box sx={{ mb: 3 }}>
                <Alert severity="info" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    🤖 Analisando dados com GPT-4OMINI...
                  </Typography>
                </Alert>
                <LinearProgress />
              </Box>
            )}

            {insights.length > 0 && (
              <Box>
                {insights.map((insight) => (
                  <Accordion key={insight.id} sx={{ mb: 2 }}>
                    <AccordionSummary expandIcon={<ExpandMore />}>
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Lightbulb sx={{ mr: 2, color: 'primary.main' }} />
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="h6">{insight.title}</Typography>
                          <Box sx={{ display: 'flex', gap: 1, mt: 1 }}>
                            <Chip
                              label={insight.impact}
                              color={getImpactColor(insight.impact) as any}
                              size="small"
                            />
                            <Chip
                              label={insight.category}
                              variant="outlined"
                              size="small"
                            />
                            <Chip
                              label={`${(insight.confidence * 100).toFixed(0)}% confiança`}
                              variant="outlined"
                              size="small"
                            />
                          </Box>
                        </Box>
                      </Box>
                    </AccordionSummary>
                    <AccordionDetails>
                      <Typography variant="body1" sx={{ mb: 2 }}>
                        {insight.description}
                      </Typography>
                      <Typography variant="h6" sx={{ mb: 1 }}>
                        Recomendações:
                      </Typography>
                      <List>
                        {insight.recommendations.map((rec, index) => (
                          <ListItem key={index}>
                            <ListItemIcon>
                              <CheckCircle color="success" />
                            </ListItemIcon>
                            <ListItemText primary={rec} />
                          </ListItem>
                        ))}
                      </List>
                    </AccordionDetails>
                  </Accordion>
                ))}
              </Box>
            )}

            {insights.length === 0 && !generatingInsights && (
              <Alert severity="info">
                Clique em "Gerar Insights" para obter análises automáticas dos seus dados com IA.
              </Alert>
            )}
          </Box>
        )}

        {/* Tab Exportar */}
        {selectedTab === 3 && (
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Exportar Relatórios
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Relatórios Disponíveis
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemText
                          primary="Relatório de Vendas"
                          secondary="Vendas por período, produtos e usuários"
                        />
                        <Button
                          variant="outlined"
                          startIcon={<Download />}
                          size="small"
                        >
                          PDF
                        </Button>
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemText
                          primary="Relatório Financeiro"
                          secondary="Receitas, custos e margem de lucro"
                        />
                        <Button
                          variant="outlined"
                          startIcon={<Download />}
                          size="small"
                        >
                          Excel
                        </Button>
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemText
                          primary="Relatório de Produtos"
                          secondary="Performance e estoque de produtos"
                        />
                        <Button
                          variant="outlined"
                          startIcon={<Download />}
                          size="small"
                        >
                          CSV
                        </Button>
                      </ListItem>
                      <Divider />
                      <ListItem>
                        <ListItemText
                          primary="Analytics com IA"
                          secondary="Insights e recomendações"
                        />
                        <Button
                          variant="outlined"
                          startIcon={<Download />}
                          size="small"
                        >
                          PDF
                        </Button>
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Configurações de Exportação
                    </Typography>
                    <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                      <FormControl fullWidth>
                        <InputLabel>Formato</InputLabel>
                        <Select defaultValue="pdf" label="Formato">
                          <MenuItem value="pdf">PDF</MenuItem>
                          <MenuItem value="excel">Excel</MenuItem>
                          <MenuItem value="csv">CSV</MenuItem>
                        </Select>
                      </FormControl>
                      
                      <FormControl fullWidth>
                        <InputLabel>Período</InputLabel>
                        <Select defaultValue="custom" label="Período">
                          <MenuItem value="today">Hoje</MenuItem>
                          <MenuItem value="week">Esta Semana</MenuItem>
                          <MenuItem value="month">Este Mês</MenuItem>
                          <MenuItem value="custom">Personalizado</MenuItem>
                        </Select>
                      </FormControl>
                      
                      <Button
                        variant="contained"
                        startIcon={<Download />}
                        fullWidth
                        size="large"
                      >
                        Exportar Relatório
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default Reports;
