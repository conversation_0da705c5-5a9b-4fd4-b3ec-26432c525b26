import { 
  BackupConfig, 
  BackupOperation, 
  RestoreOperation, 
  BackupType, 
  BackupStatus,
  RestoreType,
  RestoreStatus 
} from '../types/sync';
import { executeQuery, executeQuerySingle } from '../utils/database';
import * as fs from 'fs';
import * as path from 'path';
import * as zlib from 'zlib';

/**
 * Serviço de Backup e Recuperação
 * Gerencia backups automáticos e restauração de dados
 */
export class BackupService {
  private static instance: BackupService;
  private backupPath: string = './backups';
  private isBackupRunning: boolean = false;

  private constructor() {
    this.ensureBackupDirectory();
    this.startScheduledBackups();
  }

  static getInstance(): BackupService {
    if (!BackupService.instance) {
      BackupService.instance = new BackupService();
    }
    return BackupService.instance;
  }

  // ===== CONFIGURAÇÃO =====

  private ensureBackupDirectory(): void {
    if (!fs.existsSync(this.backupPath)) {
      fs.mkdirSync(this.backupPath, { recursive: true });
    }
  }

  private startScheduledBackups(): void {
    // Backup automático a cada 6 horas
    setInterval(async () => {
      try {
        await this.createBackup('incremental');
      } catch (error) {
        console.error('Scheduled backup failed:', error);
      }
    }, 6 * 60 * 60 * 1000);

    // Backup completo diário às 2h da manhã
    setInterval(async () => {
      const now = new Date();
      if (now.getHours() === 2 && now.getMinutes() === 0) {
        try {
          await this.createBackup('full');
        } catch (error) {
          console.error('Daily full backup failed:', error);
        }
      }
    }, 60 * 1000);
  }

  // ===== BACKUP =====

  async createBackup(type: BackupType = 'incremental'): Promise<BackupOperation> {
    if (this.isBackupRunning) {
      throw new Error('Backup already in progress');
    }

    this.isBackupRunning = true;
    const backupId = this.generateId();
    
    const operation: BackupOperation = {
      id: backupId,
      backup_type: type,
      status: 'running',
      entities_count: 0,
      started_at: new Date().toISOString(),
    };

    try {
      console.log(`Starting ${type} backup: ${backupId}`);

      // Coletar dados para backup
      const data = await this.collectBackupData(type);
      operation.entities_count = this.countEntities(data);

      // Gerar arquivo de backup
      const fileName = `backup_${type}_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
      const filePath = path.join(this.backupPath, fileName);

      // Comprimir e salvar
      const compressedData = await this.compressData(data);
      fs.writeFileSync(filePath, compressedData);

      const stats = fs.statSync(filePath);
      operation.file_path = filePath;
      operation.file_size = stats.size;
      operation.compression_ratio = data.length > 0 ? compressedData.length / data.length : 1;
      operation.status = 'completed';
      operation.completed_at = new Date().toISOString();

      console.log(`Backup completed: ${backupId} (${operation.entities_count} entities, ${(stats.size / 1024 / 1024).toFixed(2)} MB)`);

      // Limpar backups antigos
      await this.cleanupOldBackups();

      return operation;
    } catch (error) {
      operation.status = 'failed';
      operation.error_message = error instanceof Error ? error.message : 'Unknown error';
      operation.completed_at = new Date().toISOString();
      
      console.error(`Backup failed: ${backupId}`, error);
      throw error;
    } finally {
      this.isBackupRunning = false;
    }
  }

  private async collectBackupData(type: BackupType): Promise<string> {
    const data: any = {
      backup_info: {
        type,
        timestamp: new Date().toISOString(),
        version: '1.0.0',
      },
      tables: {},
    };

    // Lista de tabelas para backup
    const tables = [
      'users', 'products', 'categories', 'orders', 'order_items',
      'payments', 'cash_sessions', 'tef_transactions', 'fiscal_documents',
      'kitchen_orders', 'inventory_movements'
    ];

    for (const table of tables) {
      try {
        let query = `SELECT * FROM ${table}`;
        
        // Para backup incremental, pegar apenas dados modificados nas últimas 24h
        if (type === 'incremental') {
          query += ` WHERE updated_at >= datetime('now', '-1 day') OR created_at >= datetime('now', '-1 day')`;
        }

        const tableData = await executeQuery(query);
        data.tables[table] = tableData;
      } catch (error) {
        console.warn(`Failed to backup table ${table}:`, error);
        data.tables[table] = [];
      }
    }

    return JSON.stringify(data, null, 2);
  }

  private countEntities(data: string): number {
    try {
      const parsed = JSON.parse(data);
      return Object.values(parsed.tables).reduce((count: number, table: any) => {
        return count + (Array.isArray(table) ? table.length : 0);
      }, 0);
    } catch {
      return 0;
    }
  }

  private async compressData(data: string): Promise<Buffer> {
    return new Promise((resolve, reject) => {
      zlib.gzip(Buffer.from(data), (error, compressed) => {
        if (error) reject(error);
        else resolve(compressed);
      });
    });
  }

  private async decompressData(compressed: Buffer): Promise<string> {
    return new Promise((resolve, reject) => {
      zlib.gunzip(compressed, (error, decompressed) => {
        if (error) reject(error);
        else resolve(decompressed.toString());
      });
    });
  }

  // ===== RESTAURAÇÃO =====

  async restoreBackup(backupFilePath: string, restoreType: RestoreType = 'full'): Promise<RestoreOperation> {
    const restoreId = this.generateId();
    
    const operation: RestoreOperation = {
      id: restoreId,
      backup_id: path.basename(backupFilePath),
      restore_type: restoreType,
      status: 'running',
      entities_restored: 0,
      conflicts_found: 0,
      started_at: new Date().toISOString(),
    };

    try {
      console.log(`Starting restore: ${restoreId} from ${backupFilePath}`);

      // Verificar se arquivo existe
      if (!fs.existsSync(backupFilePath)) {
        throw new Error('Backup file not found');
      }

      // Ler e descomprimir dados
      const compressedData = fs.readFileSync(backupFilePath);
      const data = await this.decompressData(compressedData);
      const backupData = JSON.parse(data);

      // Validar estrutura do backup
      if (!backupData.backup_info || !backupData.tables) {
        throw new Error('Invalid backup file format');
      }

      // Restaurar dados
      const result = await this.restoreData(backupData, restoreType);
      operation.entities_restored = result.restored;
      operation.conflicts_found = result.conflicts;
      operation.status = 'completed';
      operation.completed_at = new Date().toISOString();

      console.log(`Restore completed: ${restoreId} (${result.restored} entities restored, ${result.conflicts} conflicts)`);

      return operation;
    } catch (error) {
      operation.status = 'failed';
      operation.error_message = error instanceof Error ? error.message : 'Unknown error';
      operation.completed_at = new Date().toISOString();
      
      console.error(`Restore failed: ${restoreId}`, error);
      throw error;
    }
  }

  private async restoreData(backupData: any, restoreType: RestoreType): Promise<{ restored: number; conflicts: number }> {
    let restored = 0;
    let conflicts = 0;

    for (const [tableName, tableData] of Object.entries(backupData.tables)) {
      if (!Array.isArray(tableData)) continue;

      for (const record of tableData as any[]) {
        try {
          if (restoreType === 'full') {
            // Restauração completa - substituir dados existentes
            await this.replaceRecord(tableName, record);
            restored++;
          } else if (restoreType === 'selective') {
            // Restauração seletiva - apenas se não existir
            const exists = await this.recordExists(tableName, record.id);
            if (!exists) {
              await this.insertRecord(tableName, record);
              restored++;
            }
          } else if (restoreType === 'merge') {
            // Merge - tentar mesclar com dados existentes
            const mergeResult = await this.mergeRecord(tableName, record);
            if (mergeResult.restored) restored++;
            if (mergeResult.conflict) conflicts++;
          }
        } catch (error) {
          console.warn(`Failed to restore record in ${tableName}:`, error);
          conflicts++;
        }
      }
    }

    return { restored, conflicts };
  }

  private async recordExists(tableName: string, id: number): Promise<boolean> {
    try {
      const result = await executeQuerySingle(
        `SELECT 1 FROM ${tableName} WHERE id = ? LIMIT 1`,
        [id]
      );
      return !!result;
    } catch {
      return false;
    }
  }

  private async replaceRecord(tableName: string, record: any): Promise<void> {
    // Simular substituição de registro
    console.log(`Replacing record in ${tableName}: ${record.id}`);
  }

  private async insertRecord(tableName: string, record: any): Promise<void> {
    // Simular inserção de registro
    console.log(`Inserting record in ${tableName}: ${record.id}`);
  }

  private async mergeRecord(tableName: string, record: any): Promise<{ restored: boolean; conflict: boolean }> {
    // Simular merge de registro
    const exists = await this.recordExists(tableName, record.id);
    
    if (!exists) {
      await this.insertRecord(tableName, record);
      return { restored: true, conflict: false };
    }

    // Verificar se há conflito (versões diferentes)
    const hasConflict = Math.random() > 0.8; // 20% de chance de conflito
    
    if (hasConflict) {
      console.log(`Conflict detected for record in ${tableName}: ${record.id}`);
      return { restored: false, conflict: true };
    } else {
      await this.replaceRecord(tableName, record);
      return { restored: true, conflict: false };
    }
  }

  // ===== LIMPEZA =====

  private async cleanupOldBackups(): Promise<void> {
    try {
      const files = fs.readdirSync(this.backupPath);
      const backupFiles = files
        .filter(file => file.startsWith('backup_') && file.endsWith('.json'))
        .map(file => ({
          name: file,
          path: path.join(this.backupPath, file),
          stats: fs.statSync(path.join(this.backupPath, file)),
        }))
        .sort((a, b) => b.stats.mtime.getTime() - a.stats.mtime.getTime());

      // Manter apenas os 30 backups mais recentes
      const filesToDelete = backupFiles.slice(30);
      
      for (const file of filesToDelete) {
        fs.unlinkSync(file.path);
        console.log(`Deleted old backup: ${file.name}`);
      }
    } catch (error) {
      console.error('Failed to cleanup old backups:', error);
    }
  }

  // ===== UTILITÁRIOS =====

  async listBackups(): Promise<Array<{ name: string; size: number; created: string; type: string }>> {
    try {
      const files = fs.readdirSync(this.backupPath);
      const backupFiles = files
        .filter(file => file.startsWith('backup_') && file.endsWith('.json'))
        .map(file => {
          const filePath = path.join(this.backupPath, file);
          const stats = fs.statSync(filePath);
          const type = file.includes('_full_') ? 'full' : 
                      file.includes('_incremental_') ? 'incremental' : 'differential';
          
          return {
            name: file,
            size: stats.size,
            created: stats.mtime.toISOString(),
            type,
          };
        })
        .sort((a, b) => new Date(b.created).getTime() - new Date(a.created).getTime());

      return backupFiles;
    } catch (error) {
      console.error('Failed to list backups:', error);
      return [];
    }
  }

  async getBackupInfo(fileName: string): Promise<any> {
    try {
      const filePath = path.join(this.backupPath, fileName);
      const compressedData = fs.readFileSync(filePath);
      const data = await this.decompressData(compressedData);
      const backupData = JSON.parse(data);
      
      return {
        ...backupData.backup_info,
        entities_count: this.countEntities(data),
        file_size: compressedData.length,
        tables: Object.keys(backupData.tables),
      };
    } catch (error) {
      console.error('Failed to get backup info:', error);
      return null;
    }
  }

  isBackupInProgress(): boolean {
    return this.isBackupRunning;
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }
}

// Instância singleton
export const backupService = BackupService.getInstance();
