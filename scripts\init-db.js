#!/usr/bin/env node

/**
 * Script para inicializar o banco de dados SQLite
 * Cria o banco, executa o schema e insere dados iniciais
 */

const fs = require('fs');
const path = require('path');
const sqlite3 = require('sqlite3').verbose();

const DB_PATH = path.join(__dirname, '..', 'database', 'adib.db');
const SCHEMA_PATH = path.join(__dirname, '..', 'database', 'schema.sql');
const SEEDS_PATH = path.join(__dirname, '..', 'database', 'seeds', 'initial_data.sql');

console.log('🗄️  Inicializando banco de dados...\n');

// Verificar se SQLite3 está disponível
try {
  require('sqlite3');
} catch (error) {
  console.error('❌ SQLite3 não encontrado!');
  console.log('Instale com: npm install sqlite3');
  process.exit(1);
}

// Criar diretório do banco se não existir
const dbDir = path.dirname(DB_PATH);
if (!fs.existsSync(dbDir)) {
  fs.mkdirSync(dbDir, { recursive: true });
}

// Função para executar SQL
function executeSqlFile(db, filePath, description) {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(filePath)) {
      reject(new Error(`Arquivo não encontrado: ${filePath}`));
      return;
    }

    const sql = fs.readFileSync(filePath, 'utf8');
    console.log(`📄 Executando ${description}...`);

    db.exec(sql, (err) => {
      if (err) {
        reject(err);
      } else {
        console.log(`✅ ${description} executado com sucesso!`);
        resolve();
      }
    });
  });
}

// Função principal
async function initDatabase() {
  let db;

  try {
    // Remover banco existente se houver
    if (fs.existsSync(DB_PATH)) {
      console.log('🗑️  Removendo banco existente...');
      fs.unlinkSync(DB_PATH);
    }

    // Criar novo banco
    console.log('🆕 Criando novo banco de dados...');
    db = new sqlite3.Database(DB_PATH);

    // Executar schema
    await executeSqlFile(db, SCHEMA_PATH, 'schema do banco');

    // Executar seeds
    await executeSqlFile(db, SEEDS_PATH, 'dados iniciais');

    // Verificar se tudo foi criado corretamente
    console.log('\n🔍 Verificando estrutura do banco...');
    
    db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
      if (err) {
        console.error('❌ Erro ao verificar tabelas:', err.message);
      } else {
        console.log(`✅ ${tables.length} tabelas criadas:`);
        tables.forEach(table => {
          console.log(`   - ${table.name}`);
        });
      }

      // Verificar dados iniciais
      db.get("SELECT COUNT(*) as count FROM users", (err, result) => {
        if (err) {
          console.error('❌ Erro ao verificar usuários:', err.message);
        } else {
          console.log(`✅ ${result.count} usuários criados`);
        }

        db.get("SELECT COUNT(*) as count FROM products", (err, result) => {
          if (err) {
            console.error('❌ Erro ao verificar produtos:', err.message);
          } else {
            console.log(`✅ ${result.count} produtos criados`);
          }

          console.log('\n🎉 Banco de dados inicializado com sucesso!');
          console.log('\nCredenciais padrão:');
          console.log('👤 Administrador: admin / password');
          console.log('💰 Caixa: caixa / password');
          console.log('🍳 Cozinha: cozinha / password');
          console.log('\n⚠️  IMPORTANTE: Altere as senhas padrão no primeiro login!');

          db.close();
        });
      });
    });

  } catch (error) {
    console.error('❌ Erro ao inicializar banco:', error.message);
    if (db) {
      db.close();
    }
    process.exit(1);
  }
}

// Executar inicialização
initDatabase();
