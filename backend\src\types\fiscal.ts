// Tipos para integração fiscal

export interface FiscalConfig {
  id: number;
  company_name: string;
  company_document: string;
  company_ie: string;
  company_address: string;
  company_city: string;
  company_state: string;
  company_zip: string;
  company_phone?: string;
  company_email?: string;
  certificate_path?: string;
  certificate_password?: string;
  environment: FiscalEnvironment;
  nfce_series: number;
  nfe_series: number;
  csc_id?: string;
  csc_token?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type FiscalEnvironment = 'production' | 'homologation';

export interface CreateFiscalConfigRequest {
  company_name: string;
  company_document: string;
  company_ie: string;
  company_address: string;
  company_city: string;
  company_state: string;
  company_zip: string;
  company_phone?: string;
  company_email?: string;
  environment?: FiscalEnvironment;
  nfce_series?: number;
  nfe_series?: number;
}

export interface UpdateFiscalConfigRequest {
  company_name?: string;
  company_document?: string;
  company_ie?: string;
  company_address?: string;
  company_city?: string;
  company_state?: string;
  company_zip?: string;
  company_phone?: string;
  company_email?: string;
  certificate_path?: string;
  certificate_password?: string;
  environment?: FiscalEnvironment;
  nfce_series?: number;
  nfe_series?: number;
  csc_id?: string;
  csc_token?: string;
  is_active?: boolean;
}

export interface FiscalDocument {
  id: number;
  type: FiscalDocumentType;
  order_id?: number;
  customer_id?: number;
  series: number;
  number: number;
  access_key: string;
  status: FiscalDocumentStatus;
  issue_date: string;
  total_amount: number;
  xml_content?: string;
  pdf_content?: string;
  qr_code?: string;
  protocol?: string;
  rejection_reason?: string;
  cancellation_reason?: string;
  cancelled_at?: string;
  created_at: string;
  updated_at: string;
  items?: FiscalDocumentItem[];
}

export type FiscalDocumentType = 'nfce' | 'nfe' | 'receipt';
export type FiscalDocumentStatus = 
  | 'pending' 
  | 'processing' 
  | 'authorized' 
  | 'rejected' 
  | 'cancelled' 
  | 'contingency';

export interface FiscalDocumentItem {
  id: number;
  fiscal_document_id: number;
  product_id: number;
  product_code: string;
  product_name: string;
  ncm_code?: string;
  cfop: string;
  unit: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  tax_rate: number;
  tax_amount: number;
  created_at: string;
  updated_at: string;
}

export interface CreateFiscalDocumentRequest {
  type: FiscalDocumentType;
  order_id?: number;
  customer_id?: number;
  items: Array<{
    product_id: number;
    quantity: number;
    unit_price: number;
    cfop?: string;
    tax_rate?: number;
  }>;
  notes?: string;
}

export interface UpdateFiscalDocumentRequest {
  status?: FiscalDocumentStatus;
  protocol?: string;
  rejection_reason?: string;
  xml_content?: string;
  pdf_content?: string;
  qr_code?: string;
}

// Filtros
export interface FiscalDocumentFilters {
  type?: FiscalDocumentType;
  status?: FiscalDocumentStatus;
  order_id?: number;
  customer_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// Contingência
export interface FiscalContingency {
  id: number;
  type: ContingencyType;
  reason: string;
  started_at: string;
  ended_at?: string;
  is_active: boolean;
  created_by: number;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

export type ContingencyType = 'offline' | 'epec' | 'fsda';

export interface CreateContingencyRequest {
  type: ContingencyType;
  reason: string;
}

export interface EndContingencyRequest {
  reason: string;
}

// Relatórios fiscais
export interface FiscalReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_documents: number;
    total_authorized: number;
    total_rejected: number;
    total_cancelled: number;
    total_amount: number;
    total_taxes: number;
  };
  by_type: Array<{
    type: FiscalDocumentType;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_status: Array<{
    status: FiscalDocumentStatus;
    count: number;
    amount: number;
  }>;
  by_day: Array<{
    date: string;
    documents: number;
    amount: number;
    taxes: number;
  }>;
  rejection_reasons: Array<{
    reason: string;
    count: number;
  }>;
}

// Configurações de impostos
export interface TaxConfig {
  id: number;
  product_id?: number;
  category_id?: number;
  tax_type: TaxType;
  rate: number;
  calculation_base: TaxCalculationBase;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type TaxType = 'icms' | 'ipi' | 'pis' | 'cofins' | 'iss';
export type TaxCalculationBase = 'product_value' | 'operation_value' | 'reduced_base';

export interface CreateTaxConfigRequest {
  product_id?: number;
  category_id?: number;
  tax_type: TaxType;
  rate: number;
  calculation_base?: TaxCalculationBase;
}

export interface UpdateTaxConfigRequest {
  rate?: number;
  calculation_base?: TaxCalculationBase;
  is_active?: boolean;
}

// Integração ACBr
export interface ACBrRequest {
  action: ACBrAction;
  data: any;
}

export interface ACBrResponse {
  success: boolean;
  data?: any;
  error?: string;
  access_key?: string;
  protocol?: string;
  xml?: string;
  pdf?: string;
  qr_code?: string;
}

export type ACBrAction = 
  | 'emit_nfce' 
  | 'emit_nfe' 
  | 'cancel_document' 
  | 'query_status' 
  | 'query_receipt'
  | 'print_document'
  | 'validate_certificate';

// Validações fiscais
export interface FiscalValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
  certificate_info?: {
    valid: boolean;
    expires_at: string;
    days_to_expire: number;
    subject: string;
  };
  sefaz_status?: {
    online: boolean;
    last_check: string;
    response_time: number;
  };
}

// Métricas fiscais
export interface FiscalMetrics {
  documents_today: number;
  documents_pending: number;
  documents_rejected: number;
  total_amount_today: number;
  total_taxes_today: number;
  authorization_rate: number;
  average_processing_time: number;
  contingency_active: boolean;
  certificate_expires_in: number;
  sefaz_status: 'online' | 'offline' | 'unstable';
  last_document_at?: string;
}

// Configurações de impressão
export interface PrintConfig {
  id: number;
  printer_name: string;
  printer_type: PrinterType;
  paper_size: PaperSize;
  margins: PrintMargins;
  logo_path?: string;
  footer_text?: string;
  is_default: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type PrinterType = 'thermal' | 'laser' | 'inkjet';
export type PaperSize = '80mm' | '58mm' | 'a4' | 'letter';

export interface PrintMargins {
  top: number;
  bottom: number;
  left: number;
  right: number;
}

export interface CreatePrintConfigRequest {
  printer_name: string;
  printer_type: PrinterType;
  paper_size: PaperSize;
  margins?: PrintMargins;
  logo_path?: string;
  footer_text?: string;
}

export interface UpdatePrintConfigRequest {
  printer_name?: string;
  printer_type?: PrinterType;
  paper_size?: PaperSize;
  margins?: PrintMargins;
  logo_path?: string;
  footer_text?: string;
  is_default?: boolean;
  is_active?: boolean;
}

// Auditoria fiscal
export interface FiscalAudit {
  id: number;
  document_id?: number;
  action: FiscalAuditAction;
  description: string;
  user_id: number;
  user_name?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export type FiscalAuditAction = 
  | 'document_created'
  | 'document_authorized'
  | 'document_rejected'
  | 'document_cancelled'
  | 'contingency_started'
  | 'contingency_ended'
  | 'config_updated'
  | 'certificate_updated';

// Backup fiscal
export interface FiscalBackup {
  id: number;
  type: BackupType;
  period_start: string;
  period_end: string;
  file_path: string;
  file_size: number;
  documents_count: number;
  status: BackupStatus;
  created_by: number;
  created_by_name?: string;
  created_at: string;
  updated_at: string;
}

export type BackupType = 'xml' | 'pdf' | 'database' | 'complete';
export type BackupStatus = 'pending' | 'processing' | 'completed' | 'failed';

export interface CreateBackupRequest {
  type: BackupType;
  period_start: string;
  period_end: string;
}

// Sincronização SEFAZ
export interface SefazSync {
  id: number;
  sync_type: SyncType;
  status: SyncStatus;
  started_at: string;
  finished_at?: string;
  documents_processed: number;
  documents_updated: number;
  errors_count: number;
  last_error?: string;
  created_at: string;
  updated_at: string;
}

export type SyncType = 'status_check' | 'receipt_query' | 'full_sync';
export type SyncStatus = 'pending' | 'running' | 'completed' | 'failed';
