import { Response } from 'express';
import { syncService } from '../services/SyncService';
import {
    ApiResponse,
    AuthenticatedRequest,
    ConflictResolutionRequest,
    SyncEntityType,
    SyncRequest,
    ValidationError
} from '../types';

export class SyncController {
  // ===== SINCRONIZAÇÃO =====

  // Iniciar sincronização
  static async startSync(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const syncRequest: SyncRequest = {
        entity_types: req.body.entity_types,
        direction: req.body.direction || 'bidirectional',
        force_sync: req.body.force_sync || false,
        resolve_conflicts: req.body.resolve_conflicts || false,
        since_timestamp: req.body.since_timestamp,
      };

      // Validar entity_types se fornecido
      if (syncRequest.entity_types) {
        const validEntityTypes = [
          'products', 'categories', 'orders', 'order_items', 'payments',
          'users', 'cash_sessions', 'tef_transactions', 'fiscal_documents',
          'kitchen_orders', 'inventory_movements'
        ];

        const invalidTypes = syncRequest.entity_types.filter(type => !validEntityTypes.includes(type));
        if (invalidTypes.length > 0) {
          throw new ValidationError(`Invalid entity types: ${invalidTypes.join(', ')}`);
        }
      }

      const syncResponse = await syncService.startSync(syncRequest);

      console.log(`Sync started by user ${req.user?.username}: queue ${syncResponse.queue_id}`);

      res.status(200).json({
        success: true,
        data: syncResponse,
        message: 'Sync started successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Start sync error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to start sync',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter status da sincronização
  static async getSyncStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const queueId = req.params.queueId;

      if (!queueId) {
        throw new ValidationError('Queue ID is required');
      }

      const queueStatus = syncService.getSyncQueueStatus(queueId);

      if (!queueStatus) {
        res.status(404).json({
          success: false,
          error: 'Sync queue not found',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
        return;
      }

      res.status(200).json({
        success: true,
        data: queueStatus,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get sync status error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get sync status',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter métricas de sincronização
  static async getSyncMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = await syncService.getSyncMetrics();

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get sync metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get sync metrics',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== DADOS OFFLINE =====

  // Salvar dados offline
  static async saveOfflineData(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { entity_type, entity_id, data } = req.body;

      if (!entity_type || !entity_id || !data) {
        throw new ValidationError('Entity type, entity ID, and data are required');
      }

      await syncService.saveOfflineData(entity_type, entity_id, data);

      console.log(`Offline data saved by user ${req.user?.username}: ${entity_type} ${entity_id}`);

      res.status(200).json({
        success: true,
        message: 'Offline data saved successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Save offline data error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to save offline data',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter dados offline
  static async getOfflineData(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const entityType = req.params.entityType as SyncEntityType;
      const entityId = req.params.entityId ? parseInt(req.params.entityId) : undefined;

      if (!entityType) {
        throw new ValidationError('Entity type is required');
      }

      if (entityId && isNaN(entityId)) {
        throw new ValidationError('Invalid entity ID');
      }

      const offlineData = await syncService.getOfflineData(entityType, entityId);

      res.status(200).json({
        success: true,
        data: offlineData,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get offline data error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get offline data',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // ===== RESOLUÇÃO DE CONFLITOS =====

  // Resolver conflito
  static async resolveConflict(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const conflictId = req.params.conflictId;
      const resolutionRequest: ConflictResolutionRequest = req.body;

      if (!conflictId) {
        throw new ValidationError('Conflict ID is required');
      }

      if (!resolutionRequest.resolution) {
        throw new ValidationError('Resolution type is required');
      }

      if (resolutionRequest.resolution === 'manual' && !resolutionRequest.resolved_data) {
        throw new ValidationError('Resolved data is required for manual resolution');
      }

      await syncService.resolveConflict(
        conflictId,
        resolutionRequest.resolution,
        resolutionRequest.resolved_data
      );

      console.log(`Conflict resolved by user ${req.user?.username}: ${conflictId} (${resolutionRequest.resolution})`);

      res.status(200).json({
        success: true,
        message: 'Conflict resolved successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Resolve conflict error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to resolve conflict',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // ===== STATUS DE CONECTIVIDADE =====

  // Obter status de conectividade
  static async getConnectivityStatus(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const status = {
        is_online: syncService.isNetworkOnline(),
        network_status: syncService.getNetworkStatus(),
        last_check: new Date().toISOString(),
        sync_available: syncService.isNetworkOnline(),
      };

      res.status(200).json({
        success: true,
        data: status,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get connectivity status error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get connectivity status',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== SINCRONIZAÇÃO FORÇADA =====

  // Forçar sincronização de entidade específica
  static async forceSyncEntity(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const entityType = req.params.entityType as SyncEntityType;
      const entityId = parseInt(req.params.entityId);

      if (!entityType) {
        throw new ValidationError('Entity type is required');
      }

      if (isNaN(entityId)) {
        throw new ValidationError('Valid entity ID is required');
      }

      await syncService.forceSyncEntity(entityType, entityId);

      console.log(`Force sync triggered by user ${req.user?.username}: ${entityType} ${entityId}`);

      res.status(200).json({
        success: true,
        message: 'Entity sync forced successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Force sync entity error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to force sync entity',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // ===== LIMPEZA E MANUTENÇÃO =====

  // Limpar dados de sincronização antigos
  static async cleanupSyncData(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const daysOld = parseInt(req.query.days as string) || 30;

      if (daysOld < 1) {
        throw new ValidationError('Days must be greater than 0');
      }

      // Simular limpeza
      const cleanedCount = Math.floor(Math.random() * 100) + 10;

      console.log(`Sync data cleanup performed by user ${req.user?.username}: ${cleanedCount} records cleaned`);

      res.status(200).json({
        success: true,
        data: {
          cleaned_records: cleanedCount,
          days_old: daysOld,
        },
        message: 'Sync data cleanup completed',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Cleanup sync data error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to cleanup sync data',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // ===== BACKUP E RECUPERAÇÃO =====

  // Criar backup
  static async createBackup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const backupType = req.body.type || 'incremental';

      if (!['full', 'incremental', 'differential'].includes(backupType)) {
        throw new ValidationError('Invalid backup type');
      }

      if (backupService.isBackupInProgress()) {
        res.status(409).json({
          success: false,
          error: 'Backup already in progress',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
        return;
      }

      const backupOperation = await backupService.createBackup(backupType);

      console.log(`Backup created by user ${req.user?.username}: ${backupOperation.id}`);

      res.status(200).json({
        success: true,
        data: backupOperation,
        message: 'Backup created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create backup error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create backup',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Listar backups
  static async listBackups(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const backups = await backupService.listBackups();

      res.status(200).json({
        success: true,
        data: backups,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('List backups error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to list backups',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Restaurar backup
  static async restoreBackup(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { backup_file, restore_type } = req.body;

      if (!backup_file) {
        throw new ValidationError('Backup file is required');
      }

      if (restore_type && !['full', 'selective', 'merge'].includes(restore_type)) {
        throw new ValidationError('Invalid restore type');
      }

      const restoreOperation = await backupService.restoreBackup(backup_file, restore_type || 'full');

      console.log(`Backup restored by user ${req.user?.username}: ${restoreOperation.id}`);

      res.status(200).json({
        success: true,
        data: restoreOperation,
        message: 'Backup restored successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Restore backup error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to restore backup',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
