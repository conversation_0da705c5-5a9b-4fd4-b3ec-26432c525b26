#!/usr/bin/env node

/**
 * Script simplificado para iniciar o Sistema PDV Adib
 * Inicia apenas o backend com as funcionalidades essenciais
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const sqlite3 = require('sqlite3').verbose();

// Configuração básica
const app = express();
const PORT = process.env.PORT || 3001;
const DB_PATH = path.join(__dirname, 'database', 'adib.db');

// Middleware básico
app.use(cors());
app.use(express.json());
app.use(express.static('frontend/dist'));

// Logs simples
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Conectar ao banco de dados
let db;
try {
  db = new sqlite3.Database(DB_PATH);
  console.log('✅ Conectado ao banco de dados SQLite');
} catch (error) {
  console.error('❌ Erro ao conectar ao banco:', error.message);
  process.exit(1);
}

// Rotas básicas
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Sistema PDV Adib funcionando!',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    status: 'online',
    database: 'connected'
  });
});

app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    data: {
      system: 'PDV Adib',
      version: '1.0.0',
      environment: 'development',
      database: 'SQLite',
      features: [
        'PDV (Ponto de Venda)',
        'Sistema de Cozinha (KDS)',
        'Gestão de Produtos',
        'Relatórios',
        'TEF',
        'Offline-First',
        'Sincronização'
      ],
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    }
  });
});

// Rota de login
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;

  if (!username || !password) {
    return res.status(400).json({
      success: false,
      error: 'Username e password são obrigatórios'
    });
  }

  // Verificar no banco de dados
  db.get(
    'SELECT * FROM users WHERE username = ? AND is_active = 1',
    [username],
    (err, user) => {
      if (err) {
        console.error('Erro ao buscar usuário:', err);
        return res.status(500).json({
          success: false,
          error: 'Erro interno do servidor'
        });
      }

      if (!user) {
        return res.status(401).json({
          success: false,
          error: 'Usuário não encontrado'
        });
      }

      // Para simplificar, vamos aceitar a senha "password" para todos os usuários
      if (password === 'password') {
        res.json({
          success: true,
          data: {
            token: 'demo-token-' + Date.now(),
            user: {
              id: user.id,
              username: user.username,
              name: user.full_name,
              role: user.role
            }
          },
          message: 'Login realizado com sucesso!'
        });
      } else {
        res.status(401).json({
          success: false,
          error: 'Senha incorreta'
        });
      }
    }
  );
});

// Rota para produtos
app.get('/api/products', (req, res) => {
  db.all(
    'SELECT p.*, c.name as category_name FROM products p LEFT JOIN categories c ON p.category_id = c.id WHERE p.is_active = 1',
    (err, products) => {
      if (err) {
        console.error('Erro ao buscar produtos:', err);
        return res.status(500).json({
          success: false,
          error: 'Erro ao buscar produtos'
        });
      }

      res.json({
        success: true,
        data: products,
        total: products.length
      });
    }
  );
});

// Rota para categorias
app.get('/api/categories', (req, res) => {
  db.all(
    'SELECT * FROM categories WHERE is_active = 1 ORDER BY name',
    (err, categories) => {
      if (err) {
        console.error('Erro ao buscar categorias:', err);
        return res.status(500).json({
          success: false,
          error: 'Erro ao buscar categorias'
        });
      }

      res.json({
        success: true,
        data: categories,
        total: categories.length
      });
    }
  );
});

// Rota para pedidos
app.get('/api/orders', (req, res) => {
  db.all(
    `SELECT o.*, u.full_name as user_name
     FROM orders o
     LEFT JOIN users u ON o.user_id = u.id
     ORDER BY o.created_at DESC
     LIMIT 50`,
    (err, orders) => {
      if (err) {
        console.error('Erro ao buscar pedidos:', err);
        return res.status(500).json({
          success: false,
          error: 'Erro ao buscar pedidos'
        });
      }

      res.json({
        success: true,
        data: orders,
        total: orders.length
      });
    }
  );
});

// Rota para criar pedido
app.post('/api/orders', (req, res) => {
  const { items, total, type = 'counter', customer_id, table_id } = req.body;

  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      error: 'Itens do pedido são obrigatórios'
    });
  }

  const orderNumber = 'PED' + Date.now().toString().slice(-6);

  // Inserir pedido
  db.run(
    `INSERT INTO orders (order_number, type, user_id, subtotal, total, status, created_at)
     VALUES (?, ?, 1, ?, ?, 'pending', datetime('now'))`,
    [orderNumber, type, total, total],
    function(err) {
      if (err) {
        console.error('Erro ao criar pedido:', err);
        return res.status(500).json({
          success: false,
          error: 'Erro ao criar pedido'
        });
      }

      const orderId = this.lastID;

      // Inserir itens do pedido
      const stmt = db.prepare(
        'INSERT INTO order_items (order_id, product_id, quantity, unit_price, total_price) VALUES (?, ?, ?, ?, ?)'
      );

      items.forEach(item => {
        stmt.run([orderId, item.id, item.quantity, item.price, item.quantity * item.price]);
      });

      stmt.finalize();

      res.json({
        success: true,
        data: {
          id: orderId,
          order_number: orderNumber,
          status: 'pending',
          items,
          total,
          created_at: new Date().toISOString()
        },
        message: 'Pedido criado com sucesso!'
      });
    }
  );
});

// Rota para métricas de sincronização
app.get('/api/sync/metrics', (req, res) => {
  res.json({
    success: true,
    data: {
      lastSync: new Date().toISOString(),
      pendingItems: 0,
      syncStatus: 'up_to_date',
      conflicts: 0,
      totalSynced: 150
    }
  });
});

// Rota para relatórios
app.get('/api/reports/sales-summary', (req, res) => {
  // Buscar dados reais do banco
  db.get(
    `SELECT
       COUNT(*) as total_orders,
       COALESCE(SUM(total), 0) as total_sales,
       COALESCE(AVG(total), 0) as average_ticket
     FROM orders
     WHERE DATE(created_at) = DATE('now')`,
    (err, summary) => {
      if (err) {
        console.error('Erro ao buscar resumo de vendas:', err);
        return res.status(500).json({
          success: false,
          error: 'Erro ao buscar relatório'
        });
      }

      res.json({
        success: true,
        data: {
          totalSales: summary.total_sales || 0,
          totalOrders: summary.total_orders || 0,
          averageTicket: summary.average_ticket || 0,
          topProducts: [],
          period: 'today'
        }
      });
    }
  );
});

// Rota raiz
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Sistema PDV Adib - API funcionando!',
    frontend: 'Frontend não encontrado. Execute "npm run build" para gerar.',
    api: {
      health: '/api/health',
      status: '/api/status',
      login: 'POST /api/auth/login',
      products: '/api/products',
      categories: '/api/categories',
      orders: '/api/orders'
    },
    credentials: {
      admin: 'admin / password',
      cashier: 'caixa / password',
      kitchen: 'cozinha / password'
    }
  });
});

// Tratamento de erros
app.use((err, req, res, next) => {
  console.error('Erro:', err);
  res.status(500).json({
    success: false,
    error: 'Erro interno do servidor',
    message: err.message
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log('\n🚀 Sistema PDV Adib iniciado com sucesso!');
  console.log(`📡 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`🔗 API Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Status do Sistema: http://localhost:${PORT}/api/status`);
  console.log('\n👤 Credenciais de teste:');
  console.log('   Admin: admin / password');
  console.log('   Caixa: caixa / password');
  console.log('   Cozinha: cozinha / password');
  console.log('\n📚 Endpoints disponíveis:');
  console.log('   GET  /api/health - Health check');
  console.log('   GET  /api/status - Status do sistema');
  console.log('   POST /api/auth/login - Login');
  console.log('   GET  /api/products - Lista de produtos');
  console.log('   GET  /api/categories - Lista de categorias');
  console.log('   GET  /api/orders - Lista de pedidos');
  console.log('   POST /api/orders - Criar pedido');
  console.log('   GET  /api/sync/metrics - Métricas de sync');
  console.log('   GET  /api/reports/sales-summary - Relatório de vendas');
  console.log('\n✨ Sistema pronto para uso!');
  console.log('\n🌐 Acesse: http://localhost:' + PORT);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🔄 Recebido SIGTERM. Encerrando servidor graciosamente...');
  if (db) {
    db.close();
  }
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\n🔄 Recebido SIGINT. Encerrando servidor graciosamente...');
  if (db) {
    db.close();
  }
  process.exit(0);
});
