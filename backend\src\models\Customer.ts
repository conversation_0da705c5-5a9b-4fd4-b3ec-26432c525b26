import { executeQuery, executeQ<PERSON>ySingle, executeUpdate } from '../utils/database';
import { 
  Customer, 
  CreateCustomerRequest, 
  UpdateCustomerRequest, 
  CustomerFilters,
  CustomerAddress,
  CreateCustomerAddressRequest
} from '../types/sales';

export class CustomerModel {
  // Buscar cliente por ID
  static async findById(id: number): Promise<Customer | null> {
    const customer = await executeQuerySingle<Customer>(
      'SELECT * FROM customers WHERE id = ?',
      [id]
    );
    return customer || null;
  }

  // Buscar cliente por CPF/CNPJ
  static async findByCpfCnpj(cpfCnpj: string): Promise<Customer | null> {
    const customer = await executeQuerySingle<Customer>(
      'SELECT * FROM customers WHERE cpf_cnpj = ? AND is_active = 1',
      [cpfCnpj]
    );
    return customer || null;
  }

  // Buscar cliente por telefone
  static async findByPhone(phone: string): Promise<Customer | null> {
    const customer = await executeQuerySingle<Customer>(
      'SELECT * FROM customers WHERE phone = ? AND is_active = 1',
      [phone]
    );
    return customer || null;
  }

  // Listar clientes com filtros
  static async findAll(filters: CustomerFilters = {}): Promise<Customer[]> {
    let query = 'SELECT * FROM customers WHERE 1=1';
    const params: any[] = [];

    if (filters.is_active !== undefined) {
      query += ' AND is_active = ?';
      params.push(filters.is_active ? 1 : 0);
    }

    if (filters.search) {
      query += ' AND (name LIKE ? OR email LIKE ? OR phone LIKE ? OR cpf_cnpj LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY name ASC';

    return await executeQuery<Customer>(query, params);
  }

  // Criar cliente
  static async create(customerData: CreateCustomerRequest): Promise<Customer> {
    // Verificar se CPF/CNPJ já existe (se fornecido)
    if (customerData.cpf_cnpj) {
      const existingCustomer = await this.findByCpfCnpj(customerData.cpf_cnpj);
      if (existingCustomer) {
        throw new Error('CPF/CNPJ already exists');
      }
    }

    // Inserir cliente
    const result = await executeUpdate(
      `INSERT INTO customers (name, email, phone, cpf_cnpj, birth_date, is_active)
       VALUES (?, ?, ?, ?, ?, 1)`,
      [
        customerData.name,
        customerData.email || null,
        customerData.phone || null,
        customerData.cpf_cnpj || null,
        customerData.birth_date || null,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create customer');
    }

    // Retornar cliente criado
    const newCustomer = await this.findById(result.lastID);
    if (!newCustomer) {
      throw new Error('Failed to retrieve created customer');
    }

    return newCustomer;
  }

  // Atualizar cliente
  static async update(id: number, customerData: UpdateCustomerRequest): Promise<Customer> {
    const customer = await this.findById(id);
    if (!customer) {
      throw new Error('Customer not found');
    }

    // Verificar se novo CPF/CNPJ já existe (se estiver sendo alterado)
    if (customerData.cpf_cnpj && customerData.cpf_cnpj !== customer.cpf_cnpj) {
      const existingCustomer = await this.findByCpfCnpj(customerData.cpf_cnpj);
      if (existingCustomer) {
        throw new Error('CPF/CNPJ already exists');
      }
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['name', 'email', 'phone', 'cpf_cnpj', 'birth_date', 'is_active'];

    updatableFields.forEach(field => {
      if (customerData[field as keyof UpdateCustomerRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = customerData[field as keyof UpdateCustomerRequest];
        
        // Converter boolean para integer
        if (field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return customer; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE customers SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar cliente atualizado
    const updatedCustomer = await this.findById(id);
    if (!updatedCustomer) {
      throw new Error('Failed to retrieve updated customer');
    }

    return updatedCustomer;
  }

  // Deletar cliente (soft delete)
  static async delete(id: number): Promise<void> {
    const customer = await this.findById(id);
    if (!customer) {
      throw new Error('Customer not found');
    }

    await executeUpdate(
      'UPDATE customers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Buscar endereços do cliente
  static async getAddresses(customerId: number): Promise<CustomerAddress[]> {
    return await executeQuery<CustomerAddress>(
      'SELECT * FROM customer_addresses WHERE customer_id = ? ORDER BY is_default DESC, created_at ASC',
      [customerId]
    );
  }

  // Adicionar endereço ao cliente
  static async addAddress(addressData: CreateCustomerAddressRequest): Promise<CustomerAddress> {
    // Verificar se cliente existe
    const customer = await this.findById(addressData.customer_id);
    if (!customer) {
      throw new Error('Customer not found');
    }

    // Se é endereço padrão, remover padrão dos outros
    if (addressData.is_default) {
      await executeUpdate(
        'UPDATE customer_addresses SET is_default = 0 WHERE customer_id = ?',
        [addressData.customer_id]
      );
    }

    // Inserir endereço
    const result = await executeUpdate(
      `INSERT INTO customer_addresses (
        customer_id, street, number, complement, neighborhood, 
        city, state, zip_code, is_default, delivery_fee
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        addressData.customer_id,
        addressData.street,
        addressData.number || null,
        addressData.complement || null,
        addressData.neighborhood,
        addressData.city,
        addressData.state,
        addressData.zip_code,
        addressData.is_default ? 1 : 0,
        addressData.delivery_fee || 0,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create customer address');
    }

    // Retornar endereço criado
    const newAddress = await executeQuerySingle<CustomerAddress>(
      'SELECT * FROM customer_addresses WHERE id = ?',
      [result.lastID]
    );

    if (!newAddress) {
      throw new Error('Failed to retrieve created address');
    }

    return newAddress;
  }

  // Buscar clientes mais frequentes
  static async findMostFrequent(limit: number = 10): Promise<Array<Customer & { order_count: number; total_spent: number }>> {
    return await executeQuery<Customer & { order_count: number; total_spent: number }>(
      `SELECT c.*, 
              COUNT(o.id) as order_count,
              COALESCE(SUM(o.total), 0) as total_spent
       FROM customers c
       LEFT JOIN orders o ON c.id = o.customer_id AND o.status != 'cancelled'
       WHERE c.is_active = 1
       GROUP BY c.id
       HAVING order_count > 0
       ORDER BY order_count DESC, total_spent DESC
       LIMIT ?`,
      [limit]
    );
  }

  // Buscar clientes por aniversário
  static async findBirthdays(month?: number, day?: number): Promise<Customer[]> {
    let query = `SELECT * FROM customers 
                 WHERE is_active = 1 AND birth_date IS NOT NULL`;
    const params: any[] = [];

    if (month !== undefined) {
      query += ` AND strftime('%m', birth_date) = ?`;
      params.push(month.toString().padStart(2, '0'));
    }

    if (day !== undefined) {
      query += ` AND strftime('%d', birth_date) = ?`;
      params.push(day.toString().padStart(2, '0'));
    }

    query += ` ORDER BY strftime('%m-%d', birth_date) ASC`;

    return await executeQuery<Customer>(query, params);
  }

  // Obter estatísticas do cliente
  static async getStats(id: number): Promise<{
    total_orders: number;
    total_spent: number;
    average_ticket: number;
    last_order_date?: string;
    favorite_products: Array<{
      product_id: number;
      product_name: string;
      quantity: number;
    }>;
  }> {
    const customer = await this.findById(id);
    if (!customer) {
      throw new Error('Customer not found');
    }

    // Estatísticas básicas
    const basicStats = await executeQuerySingle<{
      total_orders: number;
      total_spent: number;
      last_order_date?: string;
    }>(
      `SELECT 
         COUNT(o.id) as total_orders,
         COALESCE(SUM(o.total), 0) as total_spent,
         MAX(o.created_at) as last_order_date
       FROM orders o
       WHERE o.customer_id = ? AND o.status != 'cancelled'`,
      [id]
    );

    // Produtos favoritos
    const favoriteProducts = await executeQuery<{
      product_id: number;
      product_name: string;
      quantity: number;
    }>(
      `SELECT 
         oi.product_id,
         p.name as product_name,
         SUM(oi.quantity) as quantity
       FROM order_items oi
       JOIN orders o ON oi.order_id = o.id
       JOIN products p ON oi.product_id = p.id
       WHERE o.customer_id = ? AND o.status != 'cancelled'
       GROUP BY oi.product_id
       ORDER BY quantity DESC
       LIMIT 5`,
      [id]
    );

    const totalOrders = basicStats?.total_orders || 0;
    const totalSpent = basicStats?.total_spent || 0;

    return {
      total_orders: totalOrders,
      total_spent: totalSpent,
      average_ticket: totalOrders > 0 ? totalSpent / totalOrders : 0,
      last_order_date: basicStats?.last_order_date,
      favorite_products: favoriteProducts,
    };
  }
}
