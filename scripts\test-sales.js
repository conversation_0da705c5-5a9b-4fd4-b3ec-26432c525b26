#!/usr/bin/env node

/**
 * Script para testar as APIs de vendas
 * Testa CRUD de pedidos e pagamentos
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar pedidos
async function testOrders() {
  log('\n🛒 Testando Pedidos...', 'blue');

  try {
    // Listar pedidos
    log('Listando pedidos...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/sales/orders`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} pedidos encontrados`, 'green');

    // Validar pedido
    log('Validando pedido...', 'cyan');
    const orderData = {
      type: 'counter',
      items: [
        {
          product_id: 1,
          quantity: 2,
          unit_price: 15.90
        },
        {
          product_id: 2,
          quantity: 1,
          unit_price: 4.50
        }
      ],
      notes: 'Pedido de teste'
    };

    const validateResponse = await axios.post(`${API_BASE_URL}/sales/orders/validate`, orderData, {
      headers: getHeaders()
    });
    log(`✅ Validação: ${validateResponse.data.data.valid ? 'Válido' : 'Inválido'}`, 'green');

    // Criar pedido
    log('Criando novo pedido...', 'cyan');
    const createResponse = await axios.post(`${API_BASE_URL}/sales/orders`, orderData, {
      headers: getHeaders()
    });
    
    const orderId = createResponse.data.data.id;
    const orderNumber = createResponse.data.data.order_number;
    log(`✅ Pedido criado com ID: ${orderId}, Número: ${orderNumber}`, 'green');

    // Buscar pedido por ID
    log('Buscando pedido por ID...', 'cyan');
    const getResponse = await axios.get(`${API_BASE_URL}/sales/orders/${orderId}?include=items,payments`, {
      headers: getHeaders()
    });
    log(`✅ Pedido encontrado: ${getResponse.data.data.order_number}`, 'green');

    // Buscar itens do pedido
    log('Buscando itens do pedido...', 'cyan');
    const itemsResponse = await axios.get(`${API_BASE_URL}/sales/orders/${orderId}/items`, {
      headers: getHeaders()
    });
    log(`✅ ${itemsResponse.data.data.length} itens encontrados`, 'green');

    // Adicionar item ao pedido
    log('Adicionando item ao pedido...', 'cyan');
    await axios.post(`${API_BASE_URL}/sales/orders/${orderId}/items`, {
      product_id: 3,
      quantity: 1,
      unit_price: 8.90,
      notes: 'Item adicional'
    }, {
      headers: getHeaders()
    });
    log('✅ Item adicionado ao pedido', 'green');

    // Atualizar pedido
    log('Atualizando status do pedido...', 'cyan');
    await axios.put(`${API_BASE_URL}/sales/orders/${orderId}`, {
      status: 'preparing',
      notes: 'Pedido em preparo'
    }, {
      headers: getHeaders()
    });
    log('✅ Status do pedido atualizado', 'green');

    // Buscar pedidos em aberto
    log('Buscando pedidos em aberto...', 'cyan');
    const openOrdersResponse = await axios.get(`${API_BASE_URL}/sales/orders/open`, {
      headers: getHeaders()
    });
    log(`✅ ${openOrdersResponse.data.data.length} pedidos em aberto`, 'green');

    // Buscar pedidos do dia
    log('Buscando pedidos do dia...', 'cyan');
    const todayOrdersResponse = await axios.get(`${API_BASE_URL}/sales/orders/today`, {
      headers: getHeaders()
    });
    log(`✅ ${todayOrdersResponse.data.data.length} pedidos hoje`, 'green');

    // Cancelar pedido
    log('Cancelando pedido...', 'cyan');
    await axios.delete(`${API_BASE_URL}/sales/orders/${orderId}`, {
      headers: getHeaders()
    });
    log('✅ Pedido cancelado', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de pedidos: ${error.response?.data?.error || error.message}`, 'red');
    if (error.response?.data?.details) {
      console.log('Detalhes:', error.response.data.details);
    }
    return false;
  }
}

// Testar filtros de pedidos
async function testOrderFilters() {
  log('\n🔍 Testando Filtros de Pedidos...', 'blue');

  try {
    // Filtrar por tipo
    log('Filtrando pedidos por tipo...', 'cyan');
    const typeFilterResponse = await axios.get(`${API_BASE_URL}/sales/orders?type=counter`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por tipo funcionando`, 'green');

    // Filtrar por status
    log('Filtrando pedidos por status...', 'cyan');
    const statusFilterResponse = await axios.get(`${API_BASE_URL}/sales/orders?status=pending`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por status funcionando`, 'green');

    // Filtrar por data
    log('Filtrando pedidos por data...', 'cyan');
    const today = new Date().toISOString().slice(0, 10);
    const dateFilterResponse = await axios.get(`${API_BASE_URL}/sales/orders?date_from=${today}&date_to=${today}`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por data funcionando`, 'green');

    // Filtrar por busca
    log('Filtrando pedidos por busca...', 'cyan');
    const searchFilterResponse = await axios.get(`${API_BASE_URL}/sales/orders?search=teste`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por busca funcionando`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de filtros: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar criar pedido sem itens
    log('Testando pedido sem itens...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/sales/orders`, {
        type: 'counter',
        items: []
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de itens funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar criar pedido com produto inexistente
    log('Testando produto inexistente...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/sales/orders`, {
        type: 'counter',
        items: [
          {
            product_id: 99999,
            quantity: 1
          }
        ]
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 500) {
        log('✅ Validação de produto funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar criar pedido com quantidade inválida
    log('Testando quantidade inválida...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/sales/orders`, {
        type: 'counter',
        items: [
          {
            product_id: 1,
            quantity: 0
          }
        ]
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de quantidade funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs de Vendas - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      orders: await testOrders(),
      filters: await testOrderFilters(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs de vendas funcionando corretamente.', 'green');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
