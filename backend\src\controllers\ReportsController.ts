import { Request, Response } from 'express';
import { ReportsModel } from '../models/ReportsModel';
import { analyticsService } from '../services/AnalyticsService';
import { 
  ReportFilter,
  AnalyticsRequest,
  AuthenticatedRequest,
  ValidationError,
  ApiResponse 
} from '../types';

export class ReportsController {
  // ===== RELATÓRIOS OPERACIONAIS =====

  // Relatório de vendas
  static async getSalesReport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: ReportFilter = {
        date_from: req.query.date_from as string || new Date().toISOString().slice(0, 10),
        date_to: req.query.date_to as string || new Date().toISOString().slice(0, 10),
        user_id: req.query.user_id ? parseInt(req.query.user_id as string) : undefined,
        product_id: req.query.product_id ? parseInt(req.query.product_id as string) : undefined,
        category_id: req.query.category_id ? parseInt(req.query.category_id as string) : undefined,
        payment_method: req.query.payment_method as string,
      };

      const report = await ReportsModel.generateSalesReport(filters);

      res.status(200).json({
        success: true,
        data: report,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Sales report error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate sales report',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Relatório de produtos
  static async getProductReport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: ReportFilter = {
        date_from: req.query.date_from as string || new Date().toISOString().slice(0, 10),
        date_to: req.query.date_to as string || new Date().toISOString().slice(0, 10),
        category_id: req.query.category_id ? parseInt(req.query.category_id as string) : undefined,
      };

      const report = await ReportsModel.generateProductReport(filters);

      res.status(200).json({
        success: true,
        data: report,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Product report error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate product report',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Relatório financeiro
  static async getFinancialReport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: ReportFilter = {
        date_from: req.query.date_from as string || new Date().toISOString().slice(0, 10),
        date_to: req.query.date_to as string || new Date().toISOString().slice(0, 10),
        payment_method: req.query.payment_method as string,
      };

      const report = await ReportsModel.generateFinancialReport(filters);

      res.status(200).json({
        success: true,
        data: report,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Financial report error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate financial report',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Dashboard executivo
  static async getExecutiveDashboard(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: ReportFilter = {
        date_from: req.query.date_from as string || new Date().toISOString().slice(0, 10),
        date_to: req.query.date_to as string || new Date().toISOString().slice(0, 10),
      };

      const dashboard = await ReportsModel.generateExecutiveDashboard(filters);

      res.status(200).json({
        success: true,
        data: dashboard,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Executive dashboard error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to generate executive dashboard',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== ANALYTICS COM IA =====

  // Gerar insights com IA
  static async generateInsights(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const analyticsRequest: AnalyticsRequest = {
        period: {
          start: req.body.date_from || new Date().toISOString().slice(0, 10),
          end: req.body.date_to || new Date().toISOString().slice(0, 10),
        },
        focus_areas: req.body.focus_areas || ['sales', 'products', 'finance', 'operations'],
        include_predictions: req.body.include_predictions || false,
        detail_level: req.body.detail_level || 'detailed',
      };

      const analytics = await analyticsService.generateAnalytics(analyticsRequest);

      console.log(`Analytics generated for user ${req.user?.username}: ${analytics.insights.length} insights`);

      res.status(200).json({
        success: true,
        data: analytics,
        message: 'Analytics generated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Generate insights error:', error);
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate insights',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Insights rápidos para dashboard
  static async getQuickInsights(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const today = new Date().toISOString().slice(0, 10);
      const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().slice(0, 10);

      const analyticsRequest: AnalyticsRequest = {
        period: {
          start: yesterday,
          end: today,
        },
        focus_areas: ['sales', 'operations'],
        include_predictions: false,
        detail_level: 'summary',
      };

      const analytics = await analyticsService.generateAnalytics(analyticsRequest);

      // Filtrar apenas insights de alto impacto para dashboard
      const quickInsights = analytics.insights
        .filter(insight => insight.impact === 'high')
        .slice(0, 3);

      res.status(200).json({
        success: true,
        data: {
          insights: quickInsights,
          summary: {
            total_insights: quickInsights.length,
            confidence_average: quickInsights.reduce((sum, i) => sum + i.confidence, 0) / quickInsights.length,
          },
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Quick insights error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get quick insights',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== COMPARAÇÕES E ANÁLISES =====

  // Comparar períodos
  static async comparePeriods(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const currentStart = req.query.current_start as string;
      const currentEnd = req.query.current_end as string;
      const previousStart = req.query.previous_start as string;
      const previousEnd = req.query.previous_end as string;

      if (!currentStart || !currentEnd || !previousStart || !previousEnd) {
        throw new ValidationError('All period dates are required');
      }

      const [currentReport, previousReport] = await Promise.all([
        ReportsModel.generateSalesReport({
          date_from: currentStart,
          date_to: currentEnd,
        }),
        ReportsModel.generateSalesReport({
          date_from: previousStart,
          date_to: previousEnd,
        }),
      ]);

      const comparison = {
        current_period: {
          start: currentStart,
          end: currentEnd,
          data: currentReport.summary,
        },
        previous_period: {
          start: previousStart,
          end: previousEnd,
          data: previousReport.summary,
        },
        comparison: {
          revenue_growth: previousReport.summary.total_amount > 0 
            ? ((currentReport.summary.total_amount - previousReport.summary.total_amount) / previousReport.summary.total_amount) * 100
            : 0,
          orders_growth: previousReport.summary.total_orders > 0
            ? ((currentReport.summary.total_orders - previousReport.summary.total_orders) / previousReport.summary.total_orders) * 100
            : 0,
          items_growth: previousReport.summary.total_items > 0
            ? ((currentReport.summary.total_items - previousReport.summary.total_items) / previousReport.summary.total_items) * 100
            : 0,
          customers_growth: 0, // Seria calculado se tivéssemos dados de clientes
          significant_changes: [
            {
              metric: 'Receita',
              change: currentReport.summary.total_amount - previousReport.summary.total_amount,
              impact: currentReport.summary.total_amount > previousReport.summary.total_amount ? 'positive' : 'negative',
              description: `Variação de R$ ${(currentReport.summary.total_amount - previousReport.summary.total_amount).toFixed(2)}`,
            },
            {
              metric: 'Pedidos',
              change: currentReport.summary.total_orders - previousReport.summary.total_orders,
              impact: currentReport.summary.total_orders > previousReport.summary.total_orders ? 'positive' : 'negative',
              description: `Variação de ${currentReport.summary.total_orders - previousReport.summary.total_orders} pedidos`,
            },
          ],
        },
      };

      res.status(200).json({
        success: true,
        data: comparison,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Period comparison error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to compare periods',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Métricas de performance
  static async getPerformanceMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: ReportFilter = {
        date_from: req.query.date_from as string || new Date().toISOString().slice(0, 10),
        date_to: req.query.date_to as string || new Date().toISOString().slice(0, 10),
      };

      const [salesReport, financialReport] = await Promise.all([
        ReportsModel.generateSalesReport(filters),
        ReportsModel.generateFinancialReport(filters),
      ]);

      // Calcular métricas de performance
      const totalHours = 12; // Horário de funcionamento simulado
      const totalSeats = 40; // Número de assentos simulado

      const metrics = {
        period: {
          start: filters.date_from,
          end: filters.date_to,
        },
        operational: {
          orders_per_hour: salesReport.summary.total_orders / totalHours,
          average_prep_time: 8.5, // Simulado em minutos
          table_turnover: salesReport.summary.total_orders / totalSeats,
          staff_efficiency: 85.2, // Simulado em percentual
        },
        financial: {
          revenue_per_seat: salesReport.summary.total_amount / totalSeats,
          cost_percentage: (financialReport.summary.total_costs / financialReport.summary.gross_revenue) * 100,
          profit_margin: financialReport.summary.margin,
          cash_flow_ratio: 1.25, // Simulado
        },
        customer: {
          satisfaction_score: 4.2, // Simulado (1-5)
          retention_rate: 68.5, // Simulado em percentual
          acquisition_cost: 12.50, // Simulado em reais
          lifetime_value: 450.00, // Simulado em reais
        },
        benchmarks: {
          industry_average: {
            orders_per_hour: 3.2,
            profit_margin: 25.0,
            table_turnover: 2.1,
          },
          best_practices: {
            orders_per_hour: 4.5,
            profit_margin: 35.0,
            table_turnover: 3.0,
          },
          improvement_areas: [
            'Reduzir tempo de preparo',
            'Aumentar giro de mesas',
            'Melhorar margem de lucro',
          ],
        },
      };

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Performance metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get performance metrics',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== EXPORTAÇÃO =====

  // Exportar relatório
  static async exportReport(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const reportType = req.params.type;
      const format = req.query.format as string || 'pdf';

      if (!['sales', 'products', 'financial'].includes(reportType)) {
        throw new ValidationError('Invalid report type');
      }

      if (!['pdf', 'excel', 'csv'].includes(format)) {
        throw new ValidationError('Invalid export format');
      }

      // Simular exportação
      const fileName = `${reportType}_report_${new Date().toISOString().slice(0, 10)}.${format}`;
      const filePath = `/exports/${fileName}`;
      const downloadUrl = `${req.protocol}://${req.get('host')}/api/reports/download/${fileName}`;

      console.log(`Report exported: ${fileName} by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: {
          file_path: filePath,
          file_name: fileName,
          file_size: 1024 * 50, // 50KB simulado
          download_url: downloadUrl,
          expires_at: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // 24 horas
        },
        message: 'Report exported successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Export report error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to export report',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
