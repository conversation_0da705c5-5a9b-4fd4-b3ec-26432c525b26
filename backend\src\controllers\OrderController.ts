import { Request, Response } from 'express';
import { OrderModel } from '../models/Order';
import { PaymentModel } from '../models/Payment';
import { 
  CreateOrderRequest, 
  UpdateOrderRequest, 
  OrderFilters,
  CreateOrderItemRequest,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class OrderController {
  // Listar pedidos
  static async index(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: OrderFilters = {
        type: req.query.type as any,
        status: req.query.status as any,
        payment_status: req.query.payment_status as any,
        customer_id: req.query.customer_id ? parseInt(req.query.customer_id as string) : undefined,
        table_id: req.query.table_id ? parseInt(req.query.table_id as string) : undefined,
        user_id: req.query.user_id ? parseInt(req.query.user_id as string) : undefined,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        search: req.query.search as string,
      };

      const orders = await OrderModel.findAll(filters);

      res.status(200).json({
        success: true,
        data: orders,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get orders',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar pedido por ID
  static async show(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid order ID');
      }

      const order = await OrderModel.findById(id);
      if (!order) {
        throw new NotFoundError('Order not found');
      }

      // Incluir itens e pagamentos se solicitado
      if (req.query.include) {
        const includes = (req.query.include as string).split(',');
        
        if (includes.includes('items')) {
          (order as any).items = await OrderModel.getOrderItems(id);
        }
        
        if (includes.includes('payments')) {
          (order as any).payments = await PaymentModel.findByOrder(id);
        }
      }

      res.status(200).json({
        success: true,
        data: order,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Criar pedido
  static async create(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orderData: CreateOrderRequest = req.body;

      // Validações básicas
      if (!orderData.type) {
        throw new ValidationError('Order type is required');
      }

      if (!orderData.items || orderData.items.length === 0) {
        throw new ValidationError('Order must have at least one item');
      }

      const order = await OrderModel.create(orderData, req.user!.id);

      console.log(`Order ${order.order_number} created by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: order,
        message: 'Order created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar pedido
  static async update(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const orderData: UpdateOrderRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid order ID');
      }

      const order = await OrderModel.update(id, orderData);

      console.log(`Order ${order.order_number} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: order,
        message: 'Order updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Cancelar pedido
  static async cancel(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid order ID');
      }

      const order = await OrderModel.cancel(id, req.user!.id);

      console.log(`Order ${order.order_number} cancelled by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: order,
        message: 'Order cancelled successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Cancel order error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to cancel order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar itens do pedido
  static async getItems(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid order ID');
      }

      const items = await OrderModel.getOrderItems(id);

      res.status(200).json({
        success: true,
        data: items,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get order items error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get order items',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Adicionar item ao pedido
  static async addItem(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const itemData: CreateOrderItemRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid order ID');
      }

      if (!itemData.product_id || !itemData.quantity) {
        throw new ValidationError('Product ID and quantity are required');
      }

      const item = await OrderModel.addItem(id, itemData, req.user!.id);

      console.log(`Item added to order ${id} by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: item,
        message: 'Item added to order successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Add order item error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to add item to order',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar pedidos em aberto
  static async getOpen(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orders = await OrderModel.findOpen();

      res.status(200).json({
        success: true,
        data: orders,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get open orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get open orders',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar pedidos do dia
  static async getToday(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orders = await OrderModel.findToday();

      res.status(200).json({
        success: true,
        data: orders,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get today orders error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get today orders',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Validar pedido
  static async validate(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const orderData: CreateOrderRequest = req.body;

      const validation = await OrderModel.validateOrder(orderData);

      res.status(200).json({
        success: true,
        data: validation,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Validate order error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to validate order',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }
}
