import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  KitchenOrder, 
  CreateKitchenOrderRequest, 
  UpdateKitchenOrderRequest, 
  KitchenOrderFilters,
  KitchenOrderItem,
  UpdateKitchenItemRequest,
  KitchenMetrics
} from '../types/kitchen';
import { OrderModel } from './Order';

export class KitchenOrderModel {
  // Buscar pedido da cozinha por ID
  static async findById(id: number): Promise<KitchenOrder | null> {
    const kitchenOrder = await executeQuerySingle<KitchenOrder>(
      `SELECT ko.*, o.order_number, o.type as order_type, 
              t.number as table_number, c.name as customer_name
       FROM kitchen_orders ko
       LEFT JOIN orders o ON ko.order_id = o.id
       LEFT JOIN tables t ON o.table_id = t.id
       LEFT JOIN customers c ON o.customer_id = c.id
       WHERE ko.id = ?`,
      [id]
    );
    return kitchenOrder || null;
  }

  // Buscar pedido da cozinha por order_id
  static async findByOrderId(orderId: number): Promise<KitchenOrder | null> {
    const kitchenOrder = await executeQuerySingle<KitchenOrder>(
      `SELECT ko.*, o.order_number, o.type as order_type, 
              t.number as table_number, c.name as customer_name
       FROM kitchen_orders ko
       LEFT JOIN orders o ON ko.order_id = o.id
       LEFT JOIN tables t ON o.table_id = t.id
       LEFT JOIN customers c ON o.customer_id = c.id
       WHERE ko.order_id = ?`,
      [orderId]
    );
    return kitchenOrder || null;
  }

  // Listar pedidos da cozinha com filtros
  static async findAll(filters: KitchenOrderFilters = {}): Promise<KitchenOrder[]> {
    let query = `
      SELECT ko.*, o.order_number, o.type as order_type, 
             t.number as table_number, c.name as customer_name
      FROM kitchen_orders ko
      LEFT JOIN orders o ON ko.order_id = o.id
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN customers c ON o.customer_id = c.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.status) {
      query += ' AND ko.status = ?';
      params.push(filters.status);
    }

    if (filters.priority) {
      query += ' AND ko.priority = ?';
      params.push(filters.priority);
    }

    if (filters.order_type) {
      query += ' AND o.type = ?';
      params.push(filters.order_type);
    }

    if (filters.date_from) {
      query += ' AND DATE(ko.created_at) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(ko.created_at) <= ?';
      params.push(filters.date_to);
    }

    if (filters.search) {
      query += ' AND (o.order_number LIKE ? OR c.name LIKE ? OR ko.notes LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY ko.priority DESC, ko.created_at ASC';

    return await executeQuery<KitchenOrder>(query, params);
  }

  // Criar pedido da cozinha
  static async create(kitchenOrderData: CreateKitchenOrderRequest): Promise<KitchenOrder> {
    return await executeTransaction(async (db) => {
      // Verificar se pedido existe
      const order = await OrderModel.findById(kitchenOrderData.order_id);
      if (!order) {
        throw new Error('Order not found');
      }

      // Verificar se já existe pedido da cozinha para este pedido
      const existingKitchenOrder = await this.findByOrderId(kitchenOrderData.order_id);
      if (existingKitchenOrder) {
        throw new Error('Kitchen order already exists for this order');
      }

      // Calcular tempo estimado baseado nos itens
      let estimatedTime = kitchenOrderData.estimated_time;
      if (!estimatedTime) {
        const orderItems = await OrderModel.getOrderItems(kitchenOrderData.order_id);
        estimatedTime = orderItems.reduce((total, item) => {
          // Buscar tempo de preparo do produto (simulado)
          const productPreparationTime = 10; // TODO: Buscar da tabela preparation_times
          return Math.max(total, productPreparationTime * item.quantity);
        }, 0);
      }

      // Inserir pedido da cozinha
      const result = await executeUpdate(
        `INSERT INTO kitchen_orders (
          order_id, status, priority, estimated_time, notes
        ) VALUES (?, 'pending', ?, ?, ?)`,
        [
          kitchenOrderData.order_id,
          kitchenOrderData.priority || 'normal',
          estimatedTime,
          kitchenOrderData.notes || null,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create kitchen order');
      }

      const kitchenOrderId = result.lastID;

      // Criar itens da cozinha
      const orderItems = await OrderModel.getOrderItems(kitchenOrderData.order_id);
      for (const item of orderItems) {
        // Buscar tempo de preparo do produto
        const preparationTime = 10; // TODO: Buscar da tabela preparation_times
        
        await executeUpdate(
          `INSERT INTO kitchen_order_items (
            kitchen_order_id, order_item_id, product_id, product_name, 
            quantity, status, preparation_time
          ) VALUES (?, ?, ?, ?, ?, 'pending', ?)`,
          [
            kitchenOrderId,
            item.id,
            item.product_id,
            item.product?.name || 'Unknown Product',
            item.quantity,
            preparationTime,
          ]
        );
      }

      // Retornar pedido criado
      const newKitchenOrder = await this.findById(kitchenOrderId);
      if (!newKitchenOrder) {
        throw new Error('Failed to retrieve created kitchen order');
      }

      return newKitchenOrder;
    });
  }

  // Atualizar pedido da cozinha
  static async update(id: number, kitchenOrderData: UpdateKitchenOrderRequest): Promise<KitchenOrder> {
    const kitchenOrder = await this.findById(id);
    if (!kitchenOrder) {
      throw new Error('Kitchen order not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['status', 'priority', 'estimated_time', 'actual_time', 'notes'];

    updatableFields.forEach(field => {
      if (kitchenOrderData[field as keyof UpdateKitchenOrderRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        params.push(kitchenOrderData[field as keyof UpdateKitchenOrderRequest]);
      }
    });

    // Atualizar timestamps baseado no status
    if (kitchenOrderData.status === 'preparing' && kitchenOrder.status === 'pending') {
      updateFields.push('started_at = CURRENT_TIMESTAMP');
    } else if (kitchenOrderData.status === 'ready' && kitchenOrder.status === 'preparing') {
      updateFields.push('finished_at = CURRENT_TIMESTAMP');
    }

    if (updateFields.length === 0) {
      return kitchenOrder; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE kitchen_orders SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Atualizar status do pedido original se necessário
    if (kitchenOrderData.status === 'ready') {
      await executeUpdate(
        'UPDATE orders SET status = "ready", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [kitchenOrder.order_id]
      );
    } else if (kitchenOrderData.status === 'delivered') {
      await executeUpdate(
        'UPDATE orders SET status = "delivered", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [kitchenOrder.order_id]
      );
    }

    // Retornar pedido atualizado
    const updatedKitchenOrder = await this.findById(id);
    if (!updatedKitchenOrder) {
      throw new Error('Failed to retrieve updated kitchen order');
    }

    return updatedKitchenOrder;
  }

  // Buscar itens do pedido da cozinha
  static async getKitchenOrderItems(kitchenOrderId: number): Promise<KitchenOrderItem[]> {
    return await executeQuery<KitchenOrderItem>(
      `SELECT koi.*, ks.name as station_name
       FROM kitchen_order_items koi
       LEFT JOIN kitchen_stations ks ON koi.station_id = ks.id
       WHERE koi.kitchen_order_id = ?
       ORDER BY koi.created_at ASC`,
      [kitchenOrderId]
    );
  }

  // Atualizar item da cozinha
  static async updateItem(itemId: number, itemData: UpdateKitchenItemRequest): Promise<KitchenOrderItem> {
    const item = await executeQuerySingle<KitchenOrderItem>(
      'SELECT * FROM kitchen_order_items WHERE id = ?',
      [itemId]
    );

    if (!item) {
      throw new Error('Kitchen order item not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['status', 'station_id', 'notes'];

    updatableFields.forEach(field => {
      if (itemData[field as keyof UpdateKitchenItemRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        params.push(itemData[field as keyof UpdateKitchenItemRequest]);
      }
    });

    // Atualizar timestamps baseado no status
    if (itemData.status === 'preparing' && item.status === 'pending') {
      updateFields.push('started_at = CURRENT_TIMESTAMP');
    } else if (itemData.status === 'ready' && item.status === 'preparing') {
      updateFields.push('finished_at = CURRENT_TIMESTAMP');
    }

    if (updateFields.length === 0) {
      return item; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(itemId);

    await executeUpdate(
      `UPDATE kitchen_order_items SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Verificar se todos os itens estão prontos para atualizar o pedido
    const allItems = await this.getKitchenOrderItems(item.kitchen_order_id);
    const allReady = allItems.every(i => i.status === 'ready' || i.status === 'delivered');
    
    if (allReady) {
      await this.update(item.kitchen_order_id, { status: 'ready' });
    }

    // Retornar item atualizado
    const updatedItem = await executeQuerySingle<KitchenOrderItem>(
      `SELECT koi.*, ks.name as station_name
       FROM kitchen_order_items koi
       LEFT JOIN kitchen_stations ks ON koi.station_id = ks.id
       WHERE koi.id = ?`,
      [itemId]
    );

    if (!updatedItem) {
      throw new Error('Failed to retrieve updated kitchen order item');
    }

    return updatedItem;
  }

  // Buscar pedidos ativos (pendentes e em preparo)
  static async findActive(): Promise<KitchenOrder[]> {
    return await this.findAll({
      status: undefined, // Buscar todos, filtrar depois
    }).then(orders => orders.filter(order => 
      order.status === 'pending' || order.status === 'preparing'
    ));
  }

  // Buscar pedidos prontos
  static async findReady(): Promise<KitchenOrder[]> {
    return await this.findAll({
      status: 'ready',
    });
  }

  // Buscar pedidos por estação
  static async findByStation(stationId: number): Promise<KitchenOrderItem[]> {
    return await executeQuery<KitchenOrderItem>(
      `SELECT koi.*, ko.order_number, ko.priority, ko.order_type,
              ko.table_number, ko.customer_name
       FROM kitchen_order_items koi
       JOIN kitchen_orders ko ON koi.kitchen_order_id = ko.id
       WHERE koi.station_id = ? AND koi.status IN ('pending', 'preparing')
       ORDER BY ko.priority DESC, koi.created_at ASC`,
      [stationId]
    );
  }

  // Obter métricas da cozinha
  static async getMetrics(): Promise<KitchenMetrics> {
    // Pedidos ativos
    const activeOrders = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM kitchen_orders WHERE status IN ("pending", "preparing")'
    );

    // Itens por status
    const itemStats = await executeQuery<{ status: string; count: number }>(
      'SELECT status, COUNT(*) as count FROM kitchen_order_items WHERE status IN ("pending", "preparing", "ready") GROUP BY status'
    );

    // Tempo médio de espera
    const avgWaitTime = await executeQuerySingle<{ avg_time: number }>(
      `SELECT AVG(
         CASE 
           WHEN finished_at IS NOT NULL THEN 
             (julianday(finished_at) - julianday(started_at)) * 24 * 60
           ELSE 
             (julianday('now') - julianday(created_at)) * 24 * 60
         END
       ) as avg_time
       FROM kitchen_orders 
       WHERE status IN ('preparing', 'ready') 
         AND DATE(created_at) = DATE('now')`
    );

    // Maior tempo de espera
    const longestWait = await executeQuerySingle<{ max_time: number }>(
      `SELECT MAX(
         (julianday('now') - julianday(created_at)) * 24 * 60
       ) as max_time
       FROM kitchen_orders 
       WHERE status IN ('pending', 'preparing')`
    );

    // Pedidos por hora
    const ordersPerHour = await executeQuerySingle<{ count: number }>(
      `SELECT COUNT(*) as count 
       FROM kitchen_orders 
       WHERE created_at >= datetime('now', '-1 hour')`
    );

    const pendingItems = itemStats.find(s => s.status === 'pending')?.count || 0;
    const preparingItems = itemStats.find(s => s.status === 'preparing')?.count || 0;
    const readyItems = itemStats.find(s => s.status === 'ready')?.count || 0;

    return {
      active_orders: activeOrders?.count || 0,
      pending_items: pendingItems,
      preparing_items: preparingItems,
      ready_items: readyItems,
      average_wait_time: avgWaitTime?.avg_time || 0,
      longest_wait_time: longestWait?.max_time || 0,
      orders_per_hour: ordersPerHour?.count || 0,
      efficiency_score: 85, // TODO: Calcular baseado em métricas reais
      station_utilization: [], // TODO: Implementar
    };
  }

  // Buscar pedidos atrasados
  static async findDelayed(): Promise<KitchenOrder[]> {
    return await executeQuery<KitchenOrder>(
      `SELECT ko.*, o.order_number, o.type as order_type, 
              t.number as table_number, c.name as customer_name
       FROM kitchen_orders ko
       LEFT JOIN orders o ON ko.order_id = o.id
       LEFT JOIN tables t ON o.table_id = t.id
       LEFT JOIN customers c ON o.customer_id = c.id
       WHERE ko.status IN ('pending', 'preparing')
         AND (julianday('now') - julianday(ko.created_at)) * 24 * 60 > ko.estimated_time
       ORDER BY ko.created_at ASC`
    );
  }
}
