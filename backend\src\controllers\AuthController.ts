import { Request, Response } from 'express';
import { AuthService } from '../services/AuthService';
import { UserModel } from '../models/User';
import { 
  LoginRequest, 
  AuthenticatedRequest,
  ValidationError,
  AuthenticationError,
  ApiResponse 
} from '../types';

export class AuthController {
  // Login
  static async login(req: Request, res: Response): Promise<void> {
    try {
      const { username, password }: LoginRequest = req.body;

      // Validar entrada
      if (!username || !password) {
        throw new ValidationError('Username and password are required');
      }

      // Fazer login
      const result = await AuthService.login({ username, password });

      // Log da ação
      console.log(`User ${username} logged in successfully`);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Login successful',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Login error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Login failed',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Logout
  static async logout(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const authHeader = req.headers.authorization;
      const token = authHeader?.startsWith('Bearer ') 
        ? authHeader.slice(7) 
        : authHeader;

      if (token) {
        await AuthService.logout(token);
      }

      // Log da ação
      if (req.user) {
        console.log(`User ${req.user.username} logged out`);
      }

      res.status(200).json({
        success: true,
        message: 'Logout successful',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Logout error:', error);
      
      res.status(500).json({
        success: false,
        error: 'Logout failed',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Renovar token
  static async refreshToken(req: Request, res: Response): Promise<void> {
    try {
      const { token } = req.body;

      if (!token) {
        throw new ValidationError('Token is required');
      }

      const result = await AuthService.refreshToken(token);

      res.status(200).json({
        success: true,
        data: result,
        message: 'Token refreshed successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Token refresh error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Token refresh failed',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter perfil do usuário atual
  static async getProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      // Buscar dados atualizados do usuário
      const user = await UserModel.findById(req.user.id);
      if (!user) {
        throw new AuthenticationError('User not found');
      }

      res.status(200).json({
        success: true,
        data: user,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get profile error:', error);

      if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get profile',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar perfil do usuário atual
  static async updateProfile(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      const { full_name, email, phone } = req.body;

      // Atualizar dados do usuário
      const updatedUser = await UserModel.update(req.user.id, {
        full_name,
        email,
        phone,
      });

      console.log(`User ${req.user.username} updated profile`);

      res.status(200).json({
        success: true,
        data: updatedUser,
        message: 'Profile updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update profile error:', error);

      if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to update profile',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Alterar senha
  static async changePassword(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      const { currentPassword, newPassword } = req.body;

      // Validar entrada
      if (!currentPassword || !newPassword) {
        throw new ValidationError('Current password and new password are required');
      }

      // Verificar senha atual
      const isValidPassword = await UserModel.verifyPassword(req.user.username, currentPassword);
      if (!isValidPassword) {
        throw new AuthenticationError('Current password is incorrect');
      }

      // Validar força da nova senha
      const passwordValidation = AuthService.validatePasswordStrength(newPassword);
      if (!passwordValidation.isValid) {
        throw new ValidationError(`Password requirements not met: ${passwordValidation.errors.join(', ')}`);
      }

      // Atualizar senha
      await UserModel.updatePassword(req.user.id, newPassword);

      console.log(`User ${req.user.username} changed password`);

      res.status(200).json({
        success: true,
        message: 'Password changed successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Change password error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to change password',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter permissões do usuário
  static async getPermissions(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      const permissions = AuthService.getUserPermissions(req.user.role);

      res.status(200).json({
        success: true,
        data: {
          role: req.user.role,
          permissions,
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get permissions error:', error);

      if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get permissions',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
