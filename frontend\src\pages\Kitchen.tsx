import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  <PERSON>ton,
  Chip,
  IconButton,
  Badge,
  LinearProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  Divider,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Paper,
} from '@mui/material';
import {
  PlayArrow,
  CheckCircle,
  Schedule,
  Warning,
  Restaurant,
  Timer,
  Person,
  TableRestaurant,
  LocalShipping,
  Refresh,
  Settings,
} from '@mui/icons-material';
import { useAuthStore } from '../services/auth';

interface KitchenOrder {
  id: number;
  order_id: number;
  order_number: string;
  order_type: 'counter' | 'table' | 'delivery';
  table_number?: number;
  customer_name?: string;
  status: 'pending' | 'preparing' | 'ready' | 'delivered';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimated_time: number;
  actual_time?: number;
  started_at?: string;
  finished_at?: string;
  notes?: string;
  created_at: string;
  items?: KitchenOrderItem[];
}

interface KitchenOrderItem {
  id: number;
  product_name: string;
  quantity: number;
  status: 'pending' | 'preparing' | 'ready' | 'delivered';
  station_name?: string;
  preparation_time: number;
  notes?: string;
}

interface KitchenMetrics {
  active_orders: number;
  pending_items: number;
  preparing_items: number;
  ready_items: number;
  average_wait_time: number;
  longest_wait_time: number;
  orders_per_hour: number;
  efficiency_score: number;
}

const Kitchen: React.FC = () => {
  const [orders, setOrders] = useState<KitchenOrder[]>([]);
  const [metrics, setMetrics] = useState<KitchenMetrics | null>(null);
  const [selectedOrder, setSelectedOrder] = useState<KitchenOrder | null>(null);
  const [detailsDialog, setDetailsDialog] = useState(false);
  const [statusFilter, setStatusFilter] = useState<string>('active');
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular métricas
        const mockMetrics: KitchenMetrics = {
          active_orders: 8,
          pending_items: 12,
          preparing_items: 6,
          ready_items: 3,
          average_wait_time: 18.5,
          longest_wait_time: 32,
          orders_per_hour: 24,
          efficiency_score: 87,
        };

        // Simular pedidos da cozinha
        const mockOrders: KitchenOrder[] = [
          {
            id: 1,
            order_id: 1001,
            order_number: '20241219001',
            order_type: 'counter',
            status: 'preparing',
            priority: 'high',
            estimated_time: 15,
            actual_time: 18,
            started_at: '2024-12-19T10:30:00Z',
            created_at: '2024-12-19T10:25:00Z',
            notes: 'Sem cebola',
            items: [
              {
                id: 1,
                product_name: 'Hambúrguer Clássico',
                quantity: 2,
                status: 'preparing',
                station_name: 'Grill',
                preparation_time: 15,
                notes: 'Sem cebola'
              },
              {
                id: 2,
                product_name: 'Batata Frita',
                quantity: 1,
                status: 'ready',
                station_name: 'Fritura',
                preparation_time: 8,
              }
            ]
          },
          {
            id: 2,
            order_id: 1002,
            order_number: '20241219002',
            order_type: 'table',
            table_number: 5,
            customer_name: 'João Silva',
            status: 'pending',
            priority: 'normal',
            estimated_time: 20,
            created_at: '2024-12-19T10:35:00Z',
            items: [
              {
                id: 3,
                product_name: 'Hambúrguer Especial',
                quantity: 1,
                status: 'pending',
                station_name: 'Grill',
                preparation_time: 20,
              },
              {
                id: 4,
                product_name: 'Coca-Cola 350ml',
                quantity: 2,
                status: 'ready',
                preparation_time: 0,
              }
            ]
          },
          {
            id: 3,
            order_id: 1003,
            order_number: '20241219003',
            order_type: 'delivery',
            customer_name: 'Maria Santos',
            status: 'ready',
            priority: 'urgent',
            estimated_time: 25,
            actual_time: 22,
            started_at: '2024-12-19T10:20:00Z',
            finished_at: '2024-12-19T10:42:00Z',
            created_at: '2024-12-19T10:15:00Z',
            items: [
              {
                id: 5,
                product_name: 'Pudim de Leite',
                quantity: 2,
                status: 'ready',
                station_name: 'Sobremesas',
                preparation_time: 5,
              }
            ]
          },
        ];

        setMetrics(mockMetrics);
        setOrders(mockOrders);
      } catch (error) {
        console.error('Error loading kitchen data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, [statusFilter]);

  // Auto-refresh a cada 30 segundos
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(() => {
      // Recarregar dados
      console.log('Auto-refreshing kitchen data...');
    }, 30000);

    return () => clearInterval(interval);
  }, [autoRefresh]);

  // Filtrar pedidos
  const filteredOrders = orders.filter(order => {
    switch (statusFilter) {
      case 'pending':
        return order.status === 'pending';
      case 'preparing':
        return order.status === 'preparing';
      case 'ready':
        return order.status === 'ready';
      case 'active':
        return order.status === 'pending' || order.status === 'preparing';
      default:
        return true;
    }
  });

  // Calcular tempo decorrido
  const getElapsedTime = (createdAt: string, startedAt?: string): number => {
    const start = startedAt ? new Date(startedAt) : new Date(createdAt);
    const now = new Date();
    return Math.floor((now.getTime() - start.getTime()) / 1000 / 60);
  };

  // Obter cor da prioridade
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'error';
      case 'high': return 'warning';
      case 'normal': return 'primary';
      case 'low': return 'default';
      default: return 'default';
    }
  };

  // Obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return '#f57c00';
      case 'preparing': return '#1976d2';
      case 'ready': return '#388e3c';
      case 'delivered': return '#757575';
      default: return '#757575';
    }
  };

  // Obter ícone do tipo de pedido
  const getOrderTypeIcon = (type: string) => {
    switch (type) {
      case 'table': return <TableRestaurant />;
      case 'delivery': return <LocalShipping />;
      default: return <Restaurant />;
    }
  };

  // Iniciar preparo
  const startOrder = (orderId: number) => {
    setOrders(orders.map(order =>
      order.id === orderId
        ? { ...order, status: 'preparing' as const, started_at: new Date().toISOString() }
        : order
    ));
  };

  // Marcar como pronto
  const markReady = (orderId: number) => {
    setOrders(orders.map(order =>
      order.id === orderId
        ? { ...order, status: 'ready' as const, finished_at: new Date().toISOString() }
        : order
    ));
  };

  // Abrir detalhes do pedido
  const openDetails = (order: KitchenOrder) => {
    setSelectedOrder(order);
    setDetailsDialog(true);
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              Kitchen Display System
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Filtro</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Filtro"
                >
                  <MenuItem value="active">Ativos</MenuItem>
                  <MenuItem value="pending">Pendentes</MenuItem>
                  <MenuItem value="preparing">Preparando</MenuItem>
                  <MenuItem value="ready">Prontos</MenuItem>
                  <MenuItem value="all">Todos</MenuItem>
                </Select>
              </FormControl>
              
              <IconButton
                onClick={() => setAutoRefresh(!autoRefresh)}
                color={autoRefresh ? 'primary' : 'default'}
              >
                <Refresh />
              </IconButton>
              
              <IconButton>
                <Settings />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Métricas */}
      {metrics && (
        <Paper sx={{ m: 2, p: 2 }}>
          <Grid container spacing={3}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="primary">
                  {metrics.active_orders}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Pedidos Ativos
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="warning.main">
                  {metrics.pending_items}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Itens Pendentes
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4" color="success.main">
                  {metrics.ready_items}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Itens Prontos
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h4">
                  {metrics.average_wait_time.toFixed(1)}min
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Tempo Médio
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Lista de Pedidos */}
      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
        <Grid container spacing={2}>
          {filteredOrders.map((order) => {
            const elapsedTime = getElapsedTime(order.created_at, order.started_at);
            const isDelayed = elapsedTime > order.estimated_time;
            
            return (
              <Grid item xs={12} sm={6} md={4} lg={3} key={order.id}>
                <Card 
                  sx={{ 
                    height: '100%',
                    border: 2,
                    borderColor: getStatusColor(order.status),
                    cursor: 'pointer',
                    '&:hover': {
                      boxShadow: 4,
                    },
                  }}
                  onClick={() => openDetails(order)}
                >
                  <CardContent>
                    {/* Header do pedido */}
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 1 }}>
                      <Typography variant="h6" component="h3">
                        #{order.order_number.slice(-3)}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getOrderTypeIcon(order.order_type)}
                        <Chip
                          label={order.priority}
                          size="small"
                          color={getPriorityColor(order.priority) as any}
                        />
                      </Box>
                    </Box>

                    {/* Informações do cliente/mesa */}
                    {order.table_number && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Mesa {order.table_number}
                      </Typography>
                    )}
                    {order.customer_name && (
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        {order.customer_name}
                      </Typography>
                    )}

                    {/* Status e tempo */}
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                      <Chip
                        label={order.status}
                        size="small"
                        sx={{ backgroundColor: getStatusColor(order.status), color: 'white' }}
                      />
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Timer fontSize="small" />
                        <Typography variant="body2" color={isDelayed ? 'error' : 'text.secondary'}>
                          {elapsedTime}min
                        </Typography>
                        {isDelayed && <Warning fontSize="small" color="error" />}
                      </Box>
                    </Box>

                    {/* Progresso */}
                    <Box sx={{ mb: 2 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Progresso: {order.items?.filter(i => i.status === 'ready').length || 0}/{order.items?.length || 0} itens
                      </Typography>
                      <LinearProgress
                        variant="determinate"
                        value={((order.items?.filter(i => i.status === 'ready').length || 0) / (order.items?.length || 1)) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>

                    {/* Itens principais */}
                    <Box sx={{ mb: 2 }}>
                      {order.items?.slice(0, 2).map((item, index) => (
                        <Typography key={index} variant="body2" gutterBottom>
                          {item.quantity}x {item.product_name}
                        </Typography>
                      ))}
                      {(order.items?.length || 0) > 2 && (
                        <Typography variant="body2" color="text.secondary">
                          +{(order.items?.length || 0) - 2} mais...
                        </Typography>
                      )}
                    </Box>

                    {/* Ações */}
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      {order.status === 'pending' && (
                        <Button
                          fullWidth
                          variant="contained"
                          startIcon={<PlayArrow />}
                          onClick={(e) => {
                            e.stopPropagation();
                            startOrder(order.id);
                          }}
                        >
                          Iniciar
                        </Button>
                      )}
                      {order.status === 'preparing' && (
                        <Button
                          fullWidth
                          variant="contained"
                          color="success"
                          startIcon={<CheckCircle />}
                          onClick={(e) => {
                            e.stopPropagation();
                            markReady(order.id);
                          }}
                        >
                          Pronto
                        </Button>
                      )}
                      {order.status === 'ready' && (
                        <Button
                          fullWidth
                          variant="outlined"
                          color="success"
                          disabled
                        >
                          Aguardando Retirada
                        </Button>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
        </Grid>

        {filteredOrders.length === 0 && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Restaurant sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary">
              Nenhum pedido encontrado
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {statusFilter === 'active' ? 'Não há pedidos ativos no momento' : 'Ajuste os filtros para ver mais pedidos'}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Dialog de Detalhes */}
      <Dialog open={detailsDialog} onClose={() => setDetailsDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Detalhes do Pedido #{selectedOrder?.order_number.slice(-3)}
        </DialogTitle>
        <DialogContent>
          {selectedOrder && (
            <Box>
              <Grid container spacing={2} sx={{ mb: 3 }}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Tipo: {selectedOrder.order_type}
                  </Typography>
                  {selectedOrder.table_number && (
                    <Typography variant="body2" color="text.secondary">
                      Mesa: {selectedOrder.table_number}
                    </Typography>
                  )}
                  {selectedOrder.customer_name && (
                    <Typography variant="body2" color="text.secondary">
                      Cliente: {selectedOrder.customer_name}
                    </Typography>
                  )}
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status: {selectedOrder.status}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Prioridade: {selectedOrder.priority}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Tempo estimado: {selectedOrder.estimated_time}min
                  </Typography>
                </Grid>
              </Grid>

              {selectedOrder.notes && (
                <Alert severity="info" sx={{ mb: 2 }}>
                  <strong>Observações:</strong> {selectedOrder.notes}
                </Alert>
              )}

              <Typography variant="h6" gutterBottom>
                Itens do Pedido
              </Typography>
              <List>
                {selectedOrder.items?.map((item, index) => (
                  <React.Fragment key={index}>
                    <ListItem>
                      <ListItemText
                        primary={`${item.quantity}x ${item.product_name}`}
                        secondary={
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Status: {item.status} | Tempo: {item.preparation_time}min
                            </Typography>
                            {item.station_name && (
                              <Typography variant="body2" color="text.secondary">
                                Estação: {item.station_name}
                              </Typography>
                            )}
                            {item.notes && (
                              <Typography variant="body2" color="text.secondary">
                                Obs: {item.notes}
                              </Typography>
                            )}
                          </Box>
                        }
                      />
                      <Chip
                        label={item.status}
                        size="small"
                        color={item.status === 'ready' ? 'success' : item.status === 'preparing' ? 'primary' : 'default'}
                      />
                    </ListItem>
                    {index < (selectedOrder.items?.length || 0) - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialog(false)}>
            Fechar
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Kitchen;
