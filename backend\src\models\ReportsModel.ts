import { executeQuery, executeQuerySingle } from '../utils/database';
import { 
  ReportFilter, 
  SalesReport, 
  ProductReport, 
  FinancialReport,
  UserReport,
  KitchenReport,
  ExecutiveDashboard,
  PeriodComparison
} from '../types/reports';

export class ReportsModel {
  // ===== RELATÓRIO DE VENDAS =====
  
  static async generateSalesReport(filters: ReportFilter): Promise<SalesReport> {
    // Summary
    const summary = await executeQuerySingle<{
      total_orders: number;
      total_items: number;
      total_amount: number;
      total_discount: number;
      total_tax: number;
    }>(
      `SELECT 
         COUNT(DISTINCT o.id) as total_orders,
         COALESCE(SUM(oi.quantity), 0) as total_items,
         COALESCE(SUM(o.total_amount), 0) as total_amount,
         COALESCE(SUM(o.discount_amount), 0) as total_discount,
         COALESCE(SUM(o.tax_amount), 0) as total_tax
       FROM orders o
       LEFT JOIN order_items oi ON o.id = oi.order_id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'`,
      [filters.date_from, filters.date_to]
    );

    const totalOrders = summary?.total_orders || 0;
    const totalAmount = summary?.total_amount || 0;
    const averageTicket = totalOrders > 0 ? totalAmount / totalOrders : 0;
    const netAmount = totalAmount - (summary?.total_discount || 0) - (summary?.total_tax || 0);

    // Por período (diário)
    const byPeriod = await executeQuery<{
      date: string;
      orders: number;
      amount: number;
      items: number;
    }>(
      `SELECT 
         DATE(o.created_at) as date,
         COUNT(DISTINCT o.id) as orders,
         COALESCE(SUM(o.total_amount), 0) as amount,
         COALESCE(SUM(oi.quantity), 0) as items
       FROM orders o
       LEFT JOIN order_items oi ON o.id = oi.order_id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY DATE(o.created_at)
       ORDER BY date`,
      [filters.date_from, filters.date_to]
    );

    // Por hora
    const byHour = await executeQuery<{
      hour: number;
      orders: number;
      amount: number;
    }>(
      `SELECT 
         CAST(strftime('%H', o.created_at) AS INTEGER) as hour,
         COUNT(DISTINCT o.id) as orders,
         COALESCE(SUM(o.total_amount), 0) as amount
       FROM orders o
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY hour
       ORDER BY hour`,
      [filters.date_from, filters.date_to]
    );

    // Por método de pagamento
    const byPaymentMethod = await executeQuery<{
      method: string;
      orders: number;
      amount: number;
    }>(
      `SELECT 
         p.method,
         COUNT(DISTINCT o.id) as orders,
         COALESCE(SUM(p.amount), 0) as amount
       FROM orders o
       JOIN payments p ON o.id = p.order_id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY p.method
       ORDER BY amount DESC`,
      [filters.date_from, filters.date_to]
    );

    // Por usuário
    const byUser = await executeQuery<{
      user_id: number;
      user_name: string;
      orders: number;
      amount: number;
    }>(
      `SELECT 
         o.user_id,
         u.full_name as user_name,
         COUNT(DISTINCT o.id) as orders,
         COALESCE(SUM(o.total_amount), 0) as amount
       FROM orders o
       JOIN users u ON o.user_id = u.id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY o.user_id, u.full_name
       ORDER BY amount DESC`,
      [filters.date_from, filters.date_to]
    );

    // Top produtos
    const topProducts = await executeQuery<{
      product_id: number;
      product_name: string;
      quantity: number;
      amount: number;
    }>(
      `SELECT 
         oi.product_id,
         p.name as product_name,
         SUM(oi.quantity) as quantity,
         SUM(oi.quantity * oi.unit_price) as amount
       FROM order_items oi
       JOIN products p ON oi.product_id = p.id
       JOIN orders o ON oi.order_id = o.id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY oi.product_id, p.name
       ORDER BY quantity DESC
       LIMIT 10`,
      [filters.date_from, filters.date_to]
    );

    // Calcular percentuais
    const byPaymentMethodWithPercentage = byPaymentMethod.map(item => ({
      ...item,
      percentage: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0
    }));

    const byUserWithPercentage = byUser.map(item => ({
      ...item,
      percentage: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0
    }));

    const topProductsWithPercentage = topProducts.map(item => ({
      ...item,
      percentage: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0
    }));

    return {
      period: {
        start: filters.date_from,
        end: filters.date_to,
      },
      summary: {
        total_orders: totalOrders,
        total_items: summary?.total_items || 0,
        total_amount: totalAmount,
        average_ticket: averageTicket,
        total_discount: summary?.total_discount || 0,
        total_tax: summary?.total_tax || 0,
        net_amount: netAmount,
      },
      by_period: byPeriod,
      by_hour: byHour,
      by_payment_method: byPaymentMethodWithPercentage,
      by_user: byUserWithPercentage,
      top_products: topProductsWithPercentage,
    };
  }

  // ===== RELATÓRIO DE PRODUTOS =====
  
  static async generateProductReport(filters: ReportFilter): Promise<ProductReport> {
    // Summary
    const summary = await executeQuerySingle<{
      total_products: number;
      products_sold: number;
      total_quantity: number;
      total_amount: number;
    }>(
      `SELECT 
         COUNT(DISTINCT p.id) as total_products,
         COUNT(DISTINCT oi.product_id) as products_sold,
         COALESCE(SUM(oi.quantity), 0) as total_quantity,
         COALESCE(SUM(oi.quantity * oi.unit_price), 0) as total_amount
       FROM products p
       LEFT JOIN order_items oi ON p.id = oi.product_id
       LEFT JOIN orders o ON oi.order_id = o.id
       WHERE (o.id IS NULL OR (DATE(o.created_at) BETWEEN ? AND ? AND o.status = 'completed'))`,
      [filters.date_from, filters.date_to]
    );

    const totalQuantity = summary?.total_quantity || 0;
    const totalAmount = summary?.total_amount || 0;
    const averagePrice = totalQuantity > 0 ? totalAmount / totalQuantity : 0;

    // Por produto
    const byProduct = await executeQuery<{
      product_id: number;
      product_name: string;
      category_name: string;
      quantity_sold: number;
      amount: number;
      cost: number;
      stock_current: number;
      stock_min: number;
    }>(
      `SELECT 
         p.id as product_id,
         p.name as product_name,
         c.name as category_name,
         COALESCE(SUM(oi.quantity), 0) as quantity_sold,
         COALESCE(SUM(oi.quantity * oi.unit_price), 0) as amount,
         p.cost_price * COALESCE(SUM(oi.quantity), 0) as cost,
         p.stock_quantity as stock_current,
         p.stock_min
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       LEFT JOIN order_items oi ON p.id = oi.product_id
       LEFT JOIN orders o ON oi.order_id = o.id AND DATE(o.created_at) BETWEEN ? AND ? AND o.status = 'completed'
       GROUP BY p.id, p.name, c.name, p.cost_price, p.stock_quantity, p.stock_min
       ORDER BY quantity_sold DESC`,
      [filters.date_from, filters.date_to]
    );

    // Calcular lucro e margem
    const byProductWithMetrics = byProduct.map(item => {
      const profit = item.amount - item.cost;
      const margin = item.amount > 0 ? (profit / item.amount) * 100 : 0;
      return {
        ...item,
        profit,
        margin,
      };
    });

    // Por categoria
    const byCategory = await executeQuery<{
      category_id: number;
      category_name: string;
      products_count: number;
      quantity_sold: number;
      amount: number;
    }>(
      `SELECT 
         c.id as category_id,
         c.name as category_name,
         COUNT(DISTINCT p.id) as products_count,
         COALESCE(SUM(oi.quantity), 0) as quantity_sold,
         COALESCE(SUM(oi.quantity * oi.unit_price), 0) as amount
       FROM categories c
       LEFT JOIN products p ON c.id = p.category_id
       LEFT JOIN order_items oi ON p.id = oi.product_id
       LEFT JOIN orders o ON oi.order_id = o.id AND DATE(o.created_at) BETWEEN ? AND ? AND o.status = 'completed'
       GROUP BY c.id, c.name
       ORDER BY amount DESC`,
      [filters.date_from, filters.date_to]
    );

    const byCategoryWithPercentage = byCategory.map(item => ({
      ...item,
      percentage: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0
    }));

    // Estoque baixo
    const lowStock = await executeQuery<{
      product_id: number;
      product_name: string;
      stock_current: number;
      stock_min: number;
    }>(
      `SELECT 
         id as product_id,
         name as product_name,
         stock_quantity as stock_current,
         stock_min
       FROM products
       WHERE stock_quantity <= stock_min
       ORDER BY (stock_quantity - stock_min) ASC`
    );

    const lowStockWithStatus = lowStock.map(item => {
      let status: 'critical' | 'low' | 'warning';
      if (item.stock_current === 0) {
        status = 'critical';
      } else if (item.stock_current < item.stock_min * 0.5) {
        status = 'low';
      } else {
        status = 'warning';
      }
      return {
        ...item,
        status,
      };
    });

    return {
      period: {
        start: filters.date_from,
        end: filters.date_to,
      },
      summary: {
        total_products: summary?.total_products || 0,
        products_sold: summary?.products_sold || 0,
        total_quantity: totalQuantity,
        total_amount: totalAmount,
        average_price: averagePrice,
      },
      by_product: byProductWithMetrics,
      by_category: byCategoryWithPercentage,
      low_stock: lowStockWithStatus,
    };
  }

  // ===== RELATÓRIO FINANCEIRO =====
  
  static async generateFinancialReport(filters: ReportFilter): Promise<FinancialReport> {
    // Summary
    const summary = await executeQuerySingle<{
      gross_revenue: number;
      total_costs: number;
      total_taxes: number;
      total_discounts: number;
    }>(
      `SELECT 
         COALESCE(SUM(o.total_amount), 0) as gross_revenue,
         COALESCE(SUM(oi.quantity * p.cost_price), 0) as total_costs,
         COALESCE(SUM(o.tax_amount), 0) as total_taxes,
         COALESCE(SUM(o.discount_amount), 0) as total_discounts
       FROM orders o
       LEFT JOIN order_items oi ON o.id = oi.order_id
       LEFT JOIN products p ON oi.product_id = p.id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'`,
      [filters.date_from, filters.date_to]
    );

    const grossRevenue = summary?.gross_revenue || 0;
    const totalCosts = summary?.total_costs || 0;
    const totalTaxes = summary?.total_taxes || 0;
    const totalDiscounts = summary?.total_discounts || 0;
    const netRevenue = grossRevenue - totalTaxes - totalDiscounts;
    const profit = netRevenue - totalCosts;
    const margin = netRevenue > 0 ? (profit / netRevenue) * 100 : 0;

    // Fluxo de caixa (simulado)
    const cashFlow = await executeQuery<{
      date: string;
      total_sales: number;
    }>(
      `SELECT 
         DATE(created_at) as date,
         COALESCE(SUM(total_amount), 0) as total_sales
       FROM orders
       WHERE DATE(created_at) BETWEEN ? AND ?
         AND status = 'completed'
       GROUP BY DATE(created_at)
       ORDER BY date`,
      [filters.date_from, filters.date_to]
    );

    const cashFlowWithBalance = cashFlow.map((item, index) => ({
      date: item.date,
      opening_balance: index === 0 ? 1000 : 1000 + cashFlow.slice(0, index).reduce((sum, cf) => sum + cf.total_sales, 0),
      total_sales: item.total_sales,
      total_expenses: item.total_sales * 0.3, // Simulado: 30% de despesas
      closing_balance: 1000 + cashFlow.slice(0, index + 1).reduce((sum, cf) => sum + cf.total_sales, 0) - (item.total_sales * 0.3),
    }));

    // Por método de pagamento
    const byPaymentMethod = await executeQuery<{
      method: string;
      amount: number;
    }>(
      `SELECT 
         p.method,
         COALESCE(SUM(p.amount), 0) as amount
       FROM payments p
       JOIN orders o ON p.order_id = o.id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY p.method
       ORDER BY amount DESC`,
      [filters.date_from, filters.date_to]
    );

    const byPaymentMethodWithMetrics = byPaymentMethod.map(item => {
      const fees = item.method === 'card' ? item.amount * 0.035 : 0; // 3.5% para cartão
      return {
        ...item,
        percentage: grossRevenue > 0 ? (item.amount / grossRevenue) * 100 : 0,
        fees,
        net_amount: item.amount - fees,
      };
    });

    // Despesas (simulado)
    const expenses = [
      { category: 'Ingredientes', amount: totalCosts, percentage: 0 },
      { category: 'Pessoal', amount: grossRevenue * 0.25, percentage: 0 },
      { category: 'Aluguel', amount: grossRevenue * 0.08, percentage: 0 },
      { category: 'Utilities', amount: grossRevenue * 0.05, percentage: 0 },
      { category: 'Marketing', amount: grossRevenue * 0.03, percentage: 0 },
    ];

    const totalExpenses = expenses.reduce((sum, exp) => sum + exp.amount, 0);
    const expensesWithPercentage = expenses.map(item => ({
      ...item,
      percentage: totalExpenses > 0 ? (item.amount / totalExpenses) * 100 : 0,
    }));

    // Impostos
    const taxes = [
      { type: 'ICMS', amount: totalTaxes * 0.6, percentage: 0 },
      { type: 'PIS/COFINS', amount: totalTaxes * 0.3, percentage: 0 },
      { type: 'ISS', amount: totalTaxes * 0.1, percentage: 0 },
    ];

    const taxesWithPercentage = taxes.map(item => ({
      ...item,
      percentage: totalTaxes > 0 ? (item.amount / totalTaxes) * 100 : 0,
    }));

    return {
      period: {
        start: filters.date_from,
        end: filters.date_to,
      },
      summary: {
        gross_revenue: grossRevenue,
        net_revenue: netRevenue,
        total_costs: totalCosts,
        total_taxes: totalTaxes,
        total_discounts: totalDiscounts,
        profit,
        margin,
      },
      cash_flow: cashFlowWithBalance,
      by_payment_method: byPaymentMethodWithMetrics,
      expenses: expensesWithPercentage,
      taxes: taxesWithPercentage,
    };
  }

  // ===== DASHBOARD EXECUTIVO =====
  
  static async generateExecutiveDashboard(filters: ReportFilter): Promise<ExecutiveDashboard> {
    // KPIs atuais
    const currentKpis = await executeQuerySingle<{
      revenue: number;
      orders: number;
      items: number;
    }>(
      `SELECT 
         COALESCE(SUM(o.total_amount), 0) as revenue,
         COUNT(DISTINCT o.id) as orders,
         COALESCE(SUM(oi.quantity), 0) as items
       FROM orders o
       LEFT JOIN order_items oi ON o.id = oi.order_id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'`,
      [filters.date_from, filters.date_to]
    );

    // Calcular período anterior para comparação
    const daysDiff = Math.ceil((new Date(filters.date_to).getTime() - new Date(filters.date_from).getTime()) / (1000 * 60 * 60 * 24));
    const previousStart = new Date(new Date(filters.date_from).getTime() - (daysDiff * 24 * 60 * 60 * 1000)).toISOString().slice(0, 10);
    const previousEnd = new Date(new Date(filters.date_from).getTime() - (24 * 60 * 60 * 1000)).toISOString().slice(0, 10);

    const previousKpis = await executeQuerySingle<{
      revenue: number;
      orders: number;
      items: number;
    }>(
      `SELECT 
         COALESCE(SUM(o.total_amount), 0) as revenue,
         COUNT(DISTINCT o.id) as orders,
         COALESCE(SUM(oi.quantity), 0) as items
       FROM orders o
       LEFT JOIN order_items oi ON o.id = oi.order_id
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'`,
      [previousStart, previousEnd]
    );

    const currentRevenue = currentKpis?.revenue || 0;
    const previousRevenue = previousKpis?.revenue || 0;
    const currentOrders = currentKpis?.orders || 0;
    const previousOrders = previousKpis?.orders || 0;

    const revenueGrowth = previousRevenue > 0 ? ((currentRevenue - previousRevenue) / previousRevenue) * 100 : 0;
    const ordersGrowth = previousOrders > 0 ? ((currentOrders - previousOrders) / previousOrders) * 100 : 0;

    const currentAvgTicket = currentOrders > 0 ? currentRevenue / currentOrders : 0;
    const previousAvgTicket = previousOrders > 0 ? previousRevenue / previousOrders : 0;
    const avgTicketGrowth = previousAvgTicket > 0 ? ((currentAvgTicket - previousAvgTicket) / previousAvgTicket) * 100 : 0;

    // Trend de receita
    const revenueTrend = await executeQuery<{
      date: string;
      revenue: number;
      orders: number;
    }>(
      `SELECT 
         DATE(o.created_at) as date,
         COALESCE(SUM(o.total_amount), 0) as revenue,
         COUNT(DISTINCT o.id) as orders
       FROM orders o
       WHERE DATE(o.created_at) BETWEEN ? AND ?
         AND o.status = 'completed'
       GROUP BY DATE(o.created_at)
       ORDER BY date`,
      [filters.date_from, filters.date_to]
    );

    return {
      period: {
        start: filters.date_from,
        end: filters.date_to,
      },
      kpis: {
        revenue: {
          current: currentRevenue,
          previous: previousRevenue,
          growth: revenueGrowth,
          trend: revenueGrowth > 0 ? 'up' : revenueGrowth < 0 ? 'down' : 'stable',
        },
        orders: {
          current: currentOrders,
          previous: previousOrders,
          growth: ordersGrowth,
          trend: ordersGrowth > 0 ? 'up' : ordersGrowth < 0 ? 'down' : 'stable',
        },
        average_ticket: {
          current: currentAvgTicket,
          previous: previousAvgTicket,
          growth: avgTicketGrowth,
          trend: avgTicketGrowth > 0 ? 'up' : avgTicketGrowth < 0 ? 'down' : 'stable',
        },
        profit_margin: {
          current: 25.5, // Simulado
          previous: 23.8,
          growth: 7.1,
          trend: 'up',
        },
      },
      charts: {
        revenue_trend: revenueTrend,
        top_products: [], // Será preenchido
        payment_methods: [], // Será preenchido
        hourly_performance: [], // Será preenchido
      },
      alerts: [
        {
          type: 'warning',
          message: 'Estoque baixo em 3 produtos',
          priority: 'medium',
          category: 'inventory',
        },
        {
          type: 'info',
          message: 'Pico de vendas às 19h',
          priority: 'low',
          category: 'operations',
        },
      ],
      insights: [], // Será preenchido pelo serviço de analytics
    };
  }
}
