import {
    Account<PERSON><PERSON><PERSON>,
    AttachMoney,
    ExitToApp,
    Inventory,
    Settings,
    ShoppingCart,
    Store,
    TableRestaurant,
    TrendingUp
} from '@mui/icons-material';
import {
    AppBar,
    Avatar,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Grid,
    IconButton,
    Menu,
    MenuItem,
    Paper,
    Toolbar,
    Typography,
} from '@mui/material';
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../services/auth';

const Dashboard: React.FC = () => {
  const { user, logout } = useAuthStore();
  const navigate = useNavigate();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const handleLogout = async () => {
    handleMenuClose();
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager':
        return 'primary';
      case 'cashier':
        return 'secondary';
      case 'kitchen':
        return 'success';
      default:
        return 'default';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'manager':
        return 'Gerente';
      case 'cashier':
        return 'Caixa';
      case 'kitchen':
        return 'Cozinha';
      default:
        return role;
    }
  };

  // Dados de exemplo para o dashboard
  const stats = [
    {
      title: 'Vendas Hoje',
      value: 'R$ 1.250,00',
      icon: <AttachMoney />,
      color: '#4caf50',
    },
    {
      title: 'Pedidos',
      value: '23',
      icon: <ShoppingCart />,
      color: '#2196f3',
    },
    {
      title: 'Mesas Ocupadas',
      value: '5/8',
      icon: <TableRestaurant />,
      color: '#ff9800',
    },
    {
      title: 'Crescimento',
      value: '+12%',
      icon: <TrendingUp />,
      color: '#9c27b0',
    },
  ];

  return (
    <Box sx={{ flexGrow: 1 }}>
      {/* AppBar */}
      <AppBar position="static" elevation={1}>
        <Toolbar>
          <Store sx={{ mr: 2 }} />
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            Adib PDV - Dashboard
          </Typography>

          {/* Informações do usuário */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
            <Chip
              label={getRoleLabel(user?.role || '')}
              color={getRoleColor(user?.role || '') as any}
              size="small"
            />

            <Typography variant="body2">
              {user?.full_name}
            </Typography>

            <IconButton
              size="large"
              edge="end"
              aria-label="account of current user"
              aria-controls="menu-appbar"
              aria-haspopup="true"
              onClick={handleMenuOpen}
              color="inherit"
            >
              <AccountCircle />
            </IconButton>
          </Box>

          {/* Menu do usuário */}
          <Menu
            id="menu-appbar"
            anchorEl={anchorEl}
            anchorOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            keepMounted
            transformOrigin={{
              vertical: 'top',
              horizontal: 'right',
            }}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
          >
            <MenuItem onClick={handleMenuClose}>
              <Settings sx={{ mr: 1 }} />
              Configurações
            </MenuItem>
            <MenuItem onClick={handleLogout}>
              <ExitToApp sx={{ mr: 1 }} />
              Sair
            </MenuItem>
          </Menu>
        </Toolbar>
      </AppBar>

      {/* Conteúdo principal */}
      <Box sx={{ p: 3 }}>
        {/* Boas-vindas */}
        <Paper sx={{ p: 3, mb: 3, background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', color: 'white' }}>
          <Typography variant="h4" gutterBottom>
            Bem-vindo, {user?.full_name}! 👋
          </Typography>
          <Typography variant="body1">
            Sistema PDV funcionando perfeitamente. Aqui está um resumo das atividades de hoje.
          </Typography>
        </Paper>

        {/* Estatísticas */}
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar sx={{ bgcolor: stat.color, mr: 2 }}>
                      {stat.icon}
                    </Avatar>
                    <Box>
                      <Typography variant="h4" component="div">
                        {stat.value}
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        {stat.title}
                      </Typography>
                    </Box>
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Ações rápidas baseadas no role */}
        <Grid container spacing={3}>
          <Grid item xs={12} md={8}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Ações Rápidas
                </Typography>
                <Grid container spacing={2}>
                  {user?.role === 'manager' && (
                    <>
                      <Grid item xs={12} sm={6} md={4}>
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<Inventory />}
                          sx={{ height: 60 }}
                          onClick={() => navigate('/products')}
                        >
                          Gerenciar Produtos
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm={6} md={4}>
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<TrendingUp />}
                          sx={{ height: 60 }}
                        >
                          Relatórios
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm={6} md={4}>
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<Settings />}
                          sx={{ height: 60 }}
                        >
                          Configurações
                        </Button>
                      </Grid>
                    </>
                  )}

                  {(user?.role === 'cashier' || user?.role === 'manager') && (
                    <>
                      <Grid item xs={12} sm={6} md={4}>
                        <Button
                          fullWidth
                          variant="contained"
                          startIcon={<ShoppingCart />}
                          sx={{ height: 60 }}
                          onClick={() => navigate('/pos')}
                        >
                          Novo Pedido
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm={6} md={4}>
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<AttachMoney />}
                          sx={{ height: 60 }}
                        >
                          Abrir Caixa
                        </Button>
                      </Grid>
                      <Grid item xs={12} sm={6} md={4}>
                        <Button
                          fullWidth
                          variant="outlined"
                          startIcon={<TableRestaurant />}
                          sx={{ height: 60 }}
                          onClick={() => navigate('/kitchen')}
                        >
                          Cozinha (KDS)
                        </Button>
                      </Grid>
                    </>
                  )}

                  {user?.role === 'kitchen' && (
                    <Grid item xs={12}>
                      <Button
                        fullWidth
                        variant="contained"
                        startIcon={<Store />}
                        sx={{ height: 60 }}
                      >
                        Visualizar Pedidos da Cozinha
                      </Button>
                    </Grid>
                  )}
                </Grid>
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={4}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Status do Sistema
                </Typography>
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Banco de Dados:</Typography>
                    <Chip label="Online" color="success" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Fiscal:</Typography>
                    <Chip label="Homologação" color="warning" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">TEF:</Typography>
                    <Chip label="Desabilitado" color="default" size="small" />
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Typography variant="body2">Backup:</Typography>
                    <Chip label="Ativo" color="success" size="small" />
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      </Box>
    </Box>
  );
};

export default Dashboard;
