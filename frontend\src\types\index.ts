// Tipos do Frontend

export interface User {
  id: number;
  username: string;
  full_name: string;
  email?: string;
  phone?: string;
  role: UserR<PERSON>;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'manager' | 'cashier' | 'kitchen';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: User;
  token: string;
  expires_in: string;
}

export interface AuthState {
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

export interface Permission {
  resource: string;
  actions: string[];
}

export interface UserPermissions {
  role: UserRole;
  permissions: Permission[];
}

// Configurações da aplicação
export interface AppConfig {
  apiUrl: string;
  fiscalApiUrl: string;
  version: string;
  environment: string;
}

// Temas da aplicação
export interface Theme {
  primary: string;
  secondary: string;
  background: string;
  surface: string;
  text: string;
  error: string;
  warning: string;
  success: string;
  info: string;
}

// Estados de loading
export interface LoadingState {
  login: boolean;
  logout: boolean;
  profile: boolean;
  general: boolean;
}

// Notificações
export interface Notification {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: string;
}

// Configurações do usuário
export interface UserSettings {
  theme: 'light' | 'dark' | 'auto';
  language: 'pt-BR' | 'en-US';
  notifications: boolean;
  autoLogout: number; // minutos
  defaultView: 'counter' | 'table' | 'delivery';
}

// Dados da empresa
export interface CompanyInfo {
  name: string;
  cnpj: string;
  ie: string;
  address: string;
  city: string;
  state: string;
  zip: string;
  phone: string;
  email: string;
}

// Rotas da aplicação
export type AppRoute =
  | '/login'
  | '/dashboard'
  | '/pos'
  | '/products'
  | '/orders'
  | '/cash'
  | '/reports'
  | '/settings'
  | '/kitchen';

// Props comuns de componentes
export interface BaseComponentProps {
  className?: string;
  children?: React.ReactNode;
}

// Props de formulários
export interface FormProps extends BaseComponentProps {
  onSubmit: (data: any) => void;
  loading?: boolean;
  disabled?: boolean;
}

// Props de modais
export interface ModalProps extends BaseComponentProps {
  open: boolean;
  onClose: () => void;
  title?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

// Props de tabelas
export interface TableColumn {
  id: string;
  label: string;
  minWidth?: number;
  align?: 'left' | 'center' | 'right';
  format?: (value: any) => string;
  sortable?: boolean;
}

export interface TableProps extends BaseComponentProps {
  columns: TableColumn[];
  data: any[];
  loading?: boolean;
  onRowClick?: (row: any) => void;
  pagination?: boolean;
  pageSize?: number;
}

// Estados de formulários
export interface FormState<T = any> {
  data: T;
  errors: Record<string, string>;
  touched: Record<string, boolean>;
  isValid: boolean;
  isSubmitting: boolean;
}

// Validação de formulários
export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [field: string]: ValidationRule;
}

// Configurações de menu
export interface MenuItem {
  id: string;
  label: string;
  icon: string;
  route: AppRoute;
  roles: UserRole[];
  children?: MenuItem[];
}

// Configurações de atalhos de teclado
export interface KeyboardShortcut {
  key: string;
  ctrl?: boolean;
  alt?: boolean;
  shift?: boolean;
  action: string;
  description: string;
}

// Configurações de impressão
export interface PrintConfig {
  printerName: string;
  paperSize: 'A4' | '80mm' | '58mm';
  copies: number;
  autoprint: boolean;
}

// Configurações de backup
export interface BackupConfig {
  enabled: boolean;
  interval: number; // horas
  retention: number; // dias
  location: string;
}

// Estatísticas do dashboard
export interface DashboardStats {
  todaySales: number;
  todayOrders: number;
  openTables: number;
  cashBalance: number;
  topProducts: Array<{
    name: string;
    quantity: number;
    revenue: number;
  }>;
}

// Configurações de notificação do sistema
export interface SystemNotification {
  id: string;
  type: 'order' | 'payment' | 'kitchen' | 'system';
  title: string;
  message: string;
  data?: any;
  timestamp: string;
  read: boolean;
}

// ===== PRODUTOS E ESTOQUE =====

export interface Category {
  id: number;
  name: string;
  description?: string;
  color: string;
  icon: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  product_count?: number;
}

export interface Product {
  id: number;
  code?: string;
  barcode?: string;
  name: string;
  description?: string;
  category_id?: number;
  category?: Category;
  category_name?: string;
  category_color?: string;
  price: number;
  cost: number;
  unit: string;
  stock_control: boolean;
  current_stock: number;
  min_stock: number;
  max_stock: number;
  is_active: boolean;
  image_url?: string;
  preparation_time: number;
  kitchen_printer?: string;
  ncm?: string;
  cfop: string;
  tax_rate: number;
  created_at: string;
  updated_at: string;
}

export interface Ingredient {
  id: number;
  code?: string;
  name: string;
  description?: string;
  unit: string;
  cost_per_unit: number;
  current_stock: number;
  min_stock: number;
  supplier?: string;
  expiry_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Recipe {
  id: number;
  product_id: number;
  ingredient_id: number;
  ingredient?: Ingredient;
  ingredient_name?: string;
  ingredient_unit?: string;
  quantity: number;
  unit: string;
  cost: number;
  created_at: string;
}

export interface ProductWithRecipe extends Product {
  recipe: Recipe[];
  total_recipe_cost: number;
  profit_margin: number;
}

export interface StockMovement {
  id: number;
  type: 'product' | 'ingredient';
  item_id: number;
  item_name?: string;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reason?: string;
  order_id?: number;
  user_id: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
  username?: string;
  user_name?: string;
  created_at: string;
}

// Requests para criação/atualização
export interface CreateCategoryRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
}

export interface CreateProductRequest {
  code?: string;
  barcode?: string;
  name: string;
  description?: string;
  category_id?: number;
  price: number;
  cost?: number;
  unit?: string;
  stock_control?: boolean;
  current_stock?: number;
  min_stock?: number;
  max_stock?: number;
  image_url?: string;
  preparation_time?: number;
  kitchen_printer?: string;
  ncm?: string;
  cfop?: string;
  tax_rate?: number;
}

export interface UpdateProductRequest {
  code?: string;
  barcode?: string;
  name?: string;
  description?: string;
  category_id?: number;
  price?: number;
  cost?: number;
  unit?: string;
  stock_control?: boolean;
  current_stock?: number;
  min_stock?: number;
  max_stock?: number;
  is_active?: boolean;
  image_url?: string;
  preparation_time?: number;
  kitchen_printer?: string;
  ncm?: string;
  cfop?: string;
  tax_rate?: number;
}

export interface CreateIngredientRequest {
  code?: string;
  name: string;
  description?: string;
  unit: string;
  cost_per_unit: number;
  current_stock?: number;
  min_stock?: number;
  supplier?: string;
  expiry_date?: string;
}

export interface UpdateIngredientRequest {
  code?: string;
  name?: string;
  description?: string;
  unit?: string;
  cost_per_unit?: number;
  current_stock?: number;
  min_stock?: number;
  supplier?: string;
  expiry_date?: string;
  is_active?: boolean;
}

export interface CreateRecipeRequest {
  product_id: number;
  ingredient_id: number;
  quantity: number;
  unit: string;
  cost?: number;
}

export interface UpdateRecipeRequest {
  quantity?: number;
  unit?: string;
  cost?: number;
}

export interface CreateStockMovementRequest {
  type: 'product' | 'ingredient';
  item_id: number;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  unit_cost?: number;
  reason?: string;
  order_id?: number;
}

// Filtros
export interface ProductFilters {
  category_id?: number;
  is_active?: boolean;
  stock_control?: boolean;
  low_stock?: boolean;
  search?: string;
}

export interface CategoryFilters {
  is_active?: boolean;
  search?: string;
}

export interface IngredientFilters {
  is_active?: boolean;
  low_stock?: boolean;
  expired?: boolean;
  search?: string;
}

export interface StockMovementFilters {
  type?: 'product' | 'ingredient';
  item_id?: number;
  movement_type?: 'in' | 'out' | 'adjustment';
  user_id?: number;
  date_from?: string;
  date_to?: string;
}

// Unidades de medida
export type ProductUnit = 'UN' | 'KG' | 'G' | 'L' | 'ML' | 'M' | 'CM';

// Status de estoque
export type StockStatus = 'ok' | 'low' | 'out';

// Relatórios
export interface StockReport {
  products: Array<{
    id: number;
    name: string;
    current_stock: number;
    min_stock: number;
    status: StockStatus;
    value: number;
  }>;
  ingredients: Array<{
    id: number;
    name: string;
    current_stock: number;
    min_stock: number;
    status: StockStatus;
    value: number;
  }>;
  total_value: number;
  low_stock_items: number;
  out_of_stock_items: number;
}
