import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { AuthState, LoginRequest, User, UserRole } from '../types';
import { apiService } from './api';

interface AuthStore extends AuthState {
  // Actions
  login: (credentials: LoginRequest) => Promise<void>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  checkAuth: () => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;

  // Getters
  hasPermission: (resource: string, action: string) => boolean;
  isRole: (role: UserRole) => boolean;
  canAccess: (roles: UserRole[]) => boolean;
}

// Permissões por role (espelhando o backend)
const ROLE_PERMISSIONS: Record<UserRole, Array<{ resource: string; actions: string[] }>> = {
  manager: [
    { resource: 'users', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'products', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'categories', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'orders', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'cash', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'reports', actions: ['read'] },
    { resource: 'settings', actions: ['read', 'update'] },
    { resource: 'fiscal', actions: ['create', 'read', 'update'] },
  ],
  cashier: [
    { resource: 'products', actions: ['read'] },
    { resource: 'categories', actions: ['read'] },
    { resource: 'orders', actions: ['create', 'read', 'update'] },
    { resource: 'cash', actions: ['create', 'read', 'update'] },
    { resource: 'customers', actions: ['create', 'read', 'update'] },
    { resource: 'tables', actions: ['read', 'update'] },
    { resource: 'fiscal', actions: ['create', 'read'] },
  ],
  kitchen: [
    { resource: 'orders', actions: ['read', 'update'] },
    { resource: 'products', actions: ['read'] },
  ],
};

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Estado inicial
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        try {
          set({ isLoading: true, error: null });

          const response = await apiService.login(credentials);

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          console.log(`User ${response.user.username} logged in successfully`);
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Login failed';
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      logout: async () => {
        try {
          set({ isLoading: true });

          await apiService.logout();

          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });

          console.log('User logged out successfully');
        } catch (error) {
          console.error('Logout error:', error);
          // Mesmo com erro, limpar estado local
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
            error: null,
          });
        }
      },

      refreshToken: async () => {
        try {
          const response = await apiService.refreshToken();

          set({
            user: response.user,
            token: response.token,
            isAuthenticated: true,
            error: null,
          });
        } catch (error) {
          console.error('Token refresh failed:', error);
          // Se refresh falhar, fazer logout
          get().logout();
        }
      },

      updateProfile: async (data: Partial<User>) => {
        try {
          set({ isLoading: true, error: null });

          const updatedUser = await apiService.updateProfile(data);

          set({
            user: updatedUser,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Profile update failed';
          set({
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      changePassword: async (currentPassword: string, newPassword: string) => {
        try {
          set({ isLoading: true, error: null });

          await apiService.changePassword(currentPassword, newPassword);

          set({
            isLoading: false,
            error: null,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Password change failed';
          set({
            isLoading: false,
            error: errorMessage,
          });
          throw error;
        }
      },

      checkAuth: async () => {
        try {
          if (!apiService.hasValidToken()) {
            set({
              user: null,
              token: null,
              isAuthenticated: false,
            });
            return;
          }

          const response = await apiService.checkAuth();

          if (response.authenticated) {
            set({
              user: response.user,
              isAuthenticated: true,
              error: null,
            });
          } else {
            set({
              user: null,
              token: null,
              isAuthenticated: false,
            });
          }
        } catch (error) {
          console.error('Auth check failed:', error);
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
        }
      },

      clearError: () => {
        set({ error: null });
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },

      // Getters
      hasPermission: (resource: string, action: string) => {
        const { user } = get();
        if (!user) return false;

        const permissions = ROLE_PERMISSIONS[user.role];
        if (!permissions) return false;

        const resourcePermission = permissions.find(p => p.resource === resource);
        if (!resourcePermission) return false;

        return resourcePermission.actions.includes(action);
      },

      isRole: (role: UserRole) => {
        const { user } = get();
        return user?.role === role;
      },

      canAccess: (roles: UserRole[]) => {
        const { user } = get();
        if (!user) return false;
        return roles.includes(user.role);
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Hook para verificar autenticação automaticamente
export const useAuthCheck = () => {
  const checkAuth = useAuthStore(state => state.checkAuth);
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  // React será importado no componente que usar este hook
  // React.useEffect(() => {
  //   if (!isAuthenticated) {
  //     checkAuth();
  //   }
  // }, [checkAuth, isAuthenticated]);
};

// Hook para auto-refresh do token
export const useTokenRefresh = () => {
  const refreshToken = useAuthStore(state => state.refreshToken);
  const isAuthenticated = useAuthStore(state => state.isAuthenticated);

  // React será importado no componente que usar este hook
  // React.useEffect(() => {
  //   if (!isAuthenticated) return;

  //   // Refresh token a cada 23 horas (token expira em 24h)
  //   const interval = setInterval(() => {
  //     refreshToken();
  //   }, 23 * 60 * 60 * 1000);

  //   return () => clearInterval(interval);
  // }, [refreshToken, isAuthenticated]);
};

// Seletores úteis
export const authSelectors = {
  user: () => useAuthStore(state => state.user),
  isAuthenticated: () => useAuthStore(state => state.isAuthenticated),
  isLoading: () => useAuthStore(state => state.isLoading),
  error: () => useAuthStore(state => state.error),
  hasPermission: () => useAuthStore(state => state.hasPermission),
  isRole: () => useAuthStore(state => state.isRole),
  canAccess: () => useAuthStore(state => state.canAccess),
};
