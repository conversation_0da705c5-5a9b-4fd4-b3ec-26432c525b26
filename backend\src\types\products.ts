// Tipos para produtos e estoque

export interface Category {
  id: number;
  name: string;
  description?: string;
  color: string;
  icon: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateCategoryRequest {
  name: string;
  description?: string;
  color?: string;
  icon?: string;
}

export interface UpdateCategoryRequest {
  name?: string;
  description?: string;
  color?: string;
  icon?: string;
  is_active?: boolean;
}

export interface Product {
  id: number;
  code?: string;
  barcode?: string;
  name: string;
  description?: string;
  category_id?: number;
  category?: Category;
  price: number;
  cost: number;
  unit: string;
  stock_control: boolean;
  current_stock: number;
  min_stock: number;
  max_stock: number;
  is_active: boolean;
  image_url?: string;
  preparation_time: number;
  kitchen_printer?: string;
  ncm?: string;
  cfop: string;
  tax_rate: number;
  created_at: string;
  updated_at: string;
}

export interface CreateProductRequest {
  code?: string;
  barcode?: string;
  name: string;
  description?: string;
  category_id?: number;
  price: number;
  cost?: number;
  unit?: string;
  stock_control?: boolean;
  current_stock?: number;
  min_stock?: number;
  max_stock?: number;
  image_url?: string;
  preparation_time?: number;
  kitchen_printer?: string;
  ncm?: string;
  cfop?: string;
  tax_rate?: number;
}

export interface UpdateProductRequest {
  code?: string;
  barcode?: string;
  name?: string;
  description?: string;
  category_id?: number;
  price?: number;
  cost?: number;
  unit?: string;
  stock_control?: boolean;
  current_stock?: number;
  min_stock?: number;
  max_stock?: number;
  is_active?: boolean;
  image_url?: string;
  preparation_time?: number;
  kitchen_printer?: string;
  ncm?: string;
  cfop?: string;
  tax_rate?: number;
}

export interface Ingredient {
  id: number;
  code?: string;
  name: string;
  description?: string;
  unit: string;
  cost_per_unit: number;
  current_stock: number;
  min_stock: number;
  supplier?: string;
  expiry_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateIngredientRequest {
  code?: string;
  name: string;
  description?: string;
  unit: string;
  cost_per_unit: number;
  current_stock?: number;
  min_stock?: number;
  supplier?: string;
  expiry_date?: string;
}

export interface UpdateIngredientRequest {
  code?: string;
  name?: string;
  description?: string;
  unit?: string;
  cost_per_unit?: number;
  current_stock?: number;
  min_stock?: number;
  supplier?: string;
  expiry_date?: string;
  is_active?: boolean;
}

export interface Recipe {
  id: number;
  product_id: number;
  ingredient_id: number;
  ingredient?: Ingredient;
  quantity: number;
  unit: string;
  cost: number;
  created_at: string;
}

export interface CreateRecipeRequest {
  product_id: number;
  ingredient_id: number;
  quantity: number;
  unit: string;
  cost?: number;
}

export interface UpdateRecipeRequest {
  quantity?: number;
  unit?: string;
  cost?: number;
}

export interface StockMovement {
  id: number;
  type: 'product' | 'ingredient';
  item_id: number;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  unit_cost?: number;
  total_cost?: number;
  reason?: string;
  order_id?: number;
  user_id: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
  created_at: string;
}

export interface CreateStockMovementRequest {
  type: 'product' | 'ingredient';
  item_id: number;
  movement_type: 'in' | 'out' | 'adjustment';
  quantity: number;
  unit_cost?: number;
  reason?: string;
  order_id?: number;
}

// Filtros
export interface ProductFilters {
  category_id?: number;
  is_active?: boolean;
  stock_control?: boolean;
  low_stock?: boolean;
  search?: string;
}

export interface CategoryFilters {
  is_active?: boolean;
  search?: string;
}

export interface IngredientFilters {
  is_active?: boolean;
  low_stock?: boolean;
  expired?: boolean;
  search?: string;
}

export interface StockMovementFilters {
  type?: 'product' | 'ingredient';
  item_id?: number;
  movement_type?: 'in' | 'out' | 'adjustment';
  user_id?: number;
  date_from?: string;
  date_to?: string;
}

// Relatórios
export interface StockReport {
  products: Array<{
    id: number;
    name: string;
    current_stock: number;
    min_stock: number;
    status: 'ok' | 'low' | 'out';
    value: number;
  }>;
  ingredients: Array<{
    id: number;
    name: string;
    current_stock: number;
    min_stock: number;
    status: 'ok' | 'low' | 'out';
    value: number;
  }>;
  total_value: number;
  low_stock_items: number;
  out_of_stock_items: number;
}

export interface ProductWithRecipe extends Product {
  recipe: Recipe[];
  total_recipe_cost: number;
  profit_margin: number;
}

// Validações
export interface StockValidation {
  available: boolean;
  current_stock: number;
  required_stock: number;
  missing_ingredients?: Array<{
    ingredient_id: number;
    ingredient_name: string;
    required: number;
    available: number;
    missing: number;
  }>;
}

// Configurações de estoque
export interface StockSettings {
  auto_deduct: boolean;
  low_stock_alert: boolean;
  negative_stock_allowed: boolean;
  cost_method: 'fifo' | 'lifo' | 'average';
}
