// Tipos para Sincronização e Offline-First

export interface SyncConfig {
  id: number;
  entity_type: SyncEntityType;
  sync_direction: SyncDirection;
  sync_frequency: SyncFrequency;
  auto_sync: boolean;
  conflict_resolution: ConflictResolution;
  last_sync_at?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type SyncEntityType = 
  | 'products'
  | 'categories'
  | 'orders'
  | 'order_items'
  | 'payments'
  | 'users'
  | 'cash_sessions'
  | 'tef_transactions'
  | 'fiscal_documents'
  | 'kitchen_orders'
  | 'inventory_movements';

export type SyncDirection = 'up' | 'down' | 'bidirectional';
export type SyncFrequency = 'realtime' | 'minute' | 'hourly' | 'daily' | 'manual';
export type ConflictResolution = 'server_wins' | 'client_wins' | 'manual' | 'merge';

export interface SyncOperation {
  id: string;
  entity_type: SyncEntityType;
  entity_id: number;
  operation: SyncOperationType;
  direction: SyncDirection;
  status: SyncStatus;
  data: any;
  conflict_data?: any;
  error_message?: string;
  retry_count: number;
  max_retries: number;
  created_at: string;
  processed_at?: string;
  synced_at?: string;
}

export type SyncOperationType = 'create' | 'update' | 'delete' | 'restore';
export type SyncStatus = 
  | 'pending'
  | 'processing'
  | 'completed'
  | 'failed'
  | 'conflict'
  | 'cancelled';

export interface SyncQueue {
  id: string;
  operations: SyncOperation[];
  status: SyncQueueStatus;
  total_operations: number;
  completed_operations: number;
  failed_operations: number;
  started_at: string;
  completed_at?: string;
  estimated_completion?: string;
}

export type SyncQueueStatus = 'pending' | 'running' | 'completed' | 'failed' | 'paused';

export interface SyncConflict {
  id: string;
  entity_type: SyncEntityType;
  entity_id: number;
  operation_id: string;
  conflict_type: ConflictType;
  server_data: any;
  client_data: any;
  resolution: ConflictResolution;
  resolved_data?: any;
  resolved_by?: number;
  resolved_at?: string;
  created_at: string;
}

export type ConflictType = 
  | 'concurrent_update'
  | 'delete_update'
  | 'create_duplicate'
  | 'version_mismatch'
  | 'data_integrity';

export interface OfflineStorage {
  id: string;
  entity_type: SyncEntityType;
  entity_id: number;
  data: any;
  version: number;
  last_modified: string;
  is_dirty: boolean;
  is_deleted: boolean;
  sync_status: OfflineSyncStatus;
  created_offline: boolean;
}

export type OfflineSyncStatus = 'synced' | 'pending' | 'conflict' | 'error';

export interface SyncMetrics {
  total_entities: number;
  synced_entities: number;
  pending_entities: number;
  conflict_entities: number;
  error_entities: number;
  last_sync_duration: number;
  average_sync_time: number;
  sync_success_rate: number;
  network_status: NetworkStatus;
  storage_usage: {
    total_size: number;
    offline_size: number;
    cache_size: number;
  };
}

export type NetworkStatus = 'online' | 'offline' | 'slow' | 'unstable';

export interface SyncRequest {
  entity_types?: SyncEntityType[];
  direction?: SyncDirection;
  force_sync?: boolean;
  resolve_conflicts?: boolean;
  since_timestamp?: string;
}

export interface SyncResponse {
  queue_id: string;
  total_operations: number;
  estimated_duration: number;
  started_at: string;
  status: SyncQueueStatus;
}

export interface ConflictResolutionRequest {
  conflict_id: string;
  resolution: ConflictResolution;
  resolved_data?: any;
}

export interface ConflictResolutionResponse {
  conflict_id: string;
  resolution: ConflictResolution;
  applied_data: any;
  resolved_at: string;
}

// Cache e Performance
export interface CacheConfig {
  id: number;
  entity_type: SyncEntityType;
  cache_strategy: CacheStrategy;
  ttl_seconds: number;
  max_size_mb: number;
  compression_enabled: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type CacheStrategy = 'lru' | 'lfu' | 'ttl' | 'manual';

export interface CacheEntry {
  key: string;
  entity_type: SyncEntityType;
  data: any;
  size_bytes: number;
  access_count: number;
  created_at: string;
  last_accessed: string;
  expires_at?: string;
}

export interface CacheMetrics {
  total_entries: number;
  total_size_mb: number;
  hit_rate: number;
  miss_rate: number;
  eviction_count: number;
  memory_usage: number;
  by_entity_type: Array<{
    entity_type: SyncEntityType;
    entries: number;
    size_mb: number;
    hit_rate: number;
  }>;
}

// Backup e Recuperação
export interface BackupConfig {
  id: number;
  backup_type: BackupType;
  schedule: BackupSchedule;
  retention_days: number;
  compression_enabled: boolean;
  encryption_enabled: boolean;
  include_offline_data: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type BackupType = 'full' | 'incremental' | 'differential';
export type BackupSchedule = 'hourly' | 'daily' | 'weekly' | 'monthly' | 'manual';

export interface BackupOperation {
  id: string;
  backup_type: BackupType;
  status: BackupStatus;
  file_path?: string;
  file_size?: number;
  entities_count: number;
  compression_ratio?: number;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

export type BackupStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

export interface RestoreOperation {
  id: string;
  backup_id: string;
  restore_type: RestoreType;
  status: RestoreStatus;
  entities_restored: number;
  conflicts_found: number;
  started_at: string;
  completed_at?: string;
  error_message?: string;
}

export type RestoreType = 'full' | 'selective' | 'merge';
export type RestoreStatus = 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';

// Monitoramento de Conectividade
export interface ConnectivityStatus {
  is_online: boolean;
  network_type: NetworkType;
  connection_speed: ConnectionSpeed;
  latency_ms: number;
  last_check: string;
  uptime_percentage: number;
  downtime_events: Array<{
    started_at: string;
    ended_at?: string;
    duration_seconds?: number;
    reason?: string;
  }>;
}

export type NetworkType = 'wifi' | 'ethernet' | 'cellular' | 'unknown';
export type ConnectionSpeed = 'fast' | 'medium' | 'slow' | 'very_slow';

export interface NetworkEvent {
  id: string;
  event_type: NetworkEventType;
  timestamp: string;
  details: any;
}

export type NetworkEventType = 
  | 'connection_lost'
  | 'connection_restored'
  | 'speed_changed'
  | 'timeout'
  | 'error';

// Configurações de Sincronização
export interface SyncSettings {
  auto_sync_enabled: boolean;
  sync_on_startup: boolean;
  sync_on_network_restore: boolean;
  batch_size: number;
  max_concurrent_operations: number;
  retry_delay_seconds: number;
  max_retry_attempts: number;
  conflict_notification_enabled: boolean;
  background_sync_enabled: boolean;
  data_compression_enabled: boolean;
  encryption_enabled: boolean;
}

export interface CreateSyncConfigRequest {
  entity_type: SyncEntityType;
  sync_direction: SyncDirection;
  sync_frequency: SyncFrequency;
  auto_sync?: boolean;
  conflict_resolution?: ConflictResolution;
}

export interface UpdateSyncConfigRequest {
  sync_direction?: SyncDirection;
  sync_frequency?: SyncFrequency;
  auto_sync?: boolean;
  conflict_resolution?: ConflictResolution;
  is_active?: boolean;
}

// Filtros e Consultas
export interface SyncOperationFilters {
  entity_type?: SyncEntityType;
  operation?: SyncOperationType;
  status?: SyncStatus;
  direction?: SyncDirection;
  date_from?: string;
  date_to?: string;
  has_conflicts?: boolean;
}

export interface SyncConflictFilters {
  entity_type?: SyncEntityType;
  conflict_type?: ConflictType;
  resolution?: ConflictResolution;
  resolved?: boolean;
  date_from?: string;
  date_to?: string;
}

// Estatísticas e Relatórios
export interface SyncReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_operations: number;
    successful_operations: number;
    failed_operations: number;
    conflicts_resolved: number;
    data_transferred_mb: number;
    average_sync_time: number;
    uptime_percentage: number;
  };
  by_entity_type: Array<{
    entity_type: SyncEntityType;
    operations: number;
    success_rate: number;
    average_time: number;
    data_size_mb: number;
  }>;
  by_operation_type: Array<{
    operation: SyncOperationType;
    count: number;
    success_rate: number;
    average_time: number;
  }>;
  network_events: Array<{
    event_type: NetworkEventType;
    count: number;
    total_duration: number;
  }>;
  performance_metrics: {
    peak_operations_per_minute: number;
    average_latency_ms: number;
    cache_hit_rate: number;
    storage_efficiency: number;
  };
}

// Validações e Integridade
export interface DataIntegrityCheck {
  id: string;
  entity_type: SyncEntityType;
  check_type: IntegrityCheckType;
  status: IntegrityCheckStatus;
  issues_found: number;
  issues_fixed: number;
  started_at: string;
  completed_at?: string;
  report?: any;
}

export type IntegrityCheckType = 
  | 'consistency'
  | 'referential'
  | 'duplicates'
  | 'orphaned'
  | 'corruption';

export type IntegrityCheckStatus = 'pending' | 'running' | 'completed' | 'failed';

export interface IntegrityIssue {
  id: string;
  check_id: string;
  entity_type: SyncEntityType;
  entity_id: number;
  issue_type: IntegrityCheckType;
  description: string;
  severity: IssueSeverity;
  auto_fixable: boolean;
  fixed: boolean;
  fix_action?: string;
  created_at: string;
  fixed_at?: string;
}

export type IssueSeverity = 'low' | 'medium' | 'high' | 'critical';
