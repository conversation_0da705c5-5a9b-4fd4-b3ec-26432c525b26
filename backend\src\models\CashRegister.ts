import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  CashRegister, 
  CreateCashRegisterRequest, 
  UpdateCashRegisterRequest 
} from '../types/cashier';

export class CashRegisterModel {
  // Buscar caixa por ID
  static async findById(id: number): Promise<CashRegister | null> {
    const cashRegister = await executeQuerySingle<CashRegister>(
      'SELECT * FROM cash_registers WHERE id = ?',
      [id]
    );
    return cashRegister || null;
  }

  // Buscar caixa por nome
  static async findByName(name: string): Promise<CashRegister | null> {
    const cashRegister = await executeQuerySingle<CashRegister>(
      'SELECT * FROM cash_registers WHERE name = ? AND is_active = 1',
      [name]
    );
    return cashRegister || null;
  }

  // Listar todos os caixas
  static async findAll(activeOnly: boolean = true): Promise<CashRegister[]> {
    let query = 'SELECT * FROM cash_registers';
    const params: any[] = [];

    if (activeOnly) {
      query += ' WHERE is_active = 1';
    }

    query += ' ORDER BY name ASC';

    return await executeQuery<CashRegister>(query, params);
  }

  // Criar caixa
  static async create(cashRegisterData: CreateCashRegisterRequest): Promise<CashRegister> {
    // Verificar se nome já existe
    const existingCashRegister = await this.findByName(cashRegisterData.name);
    if (existingCashRegister) {
      throw new Error('Cash register name already exists');
    }

    // Inserir caixa
    const result = await executeUpdate(
      `INSERT INTO cash_registers (name, description, is_active)
       VALUES (?, ?, 1)`,
      [
        cashRegisterData.name,
        cashRegisterData.description || null,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create cash register');
    }

    // Retornar caixa criado
    const newCashRegister = await this.findById(result.lastID);
    if (!newCashRegister) {
      throw new Error('Failed to retrieve created cash register');
    }

    return newCashRegister;
  }

  // Atualizar caixa
  static async update(id: number, cashRegisterData: UpdateCashRegisterRequest): Promise<CashRegister> {
    const cashRegister = await this.findById(id);
    if (!cashRegister) {
      throw new Error('Cash register not found');
    }

    // Verificar se novo nome já existe (se estiver sendo alterado)
    if (cashRegisterData.name && cashRegisterData.name !== cashRegister.name) {
      const existingCashRegister = await this.findByName(cashRegisterData.name);
      if (existingCashRegister) {
        throw new Error('Cash register name already exists');
      }
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['name', 'description', 'is_active'];

    updatableFields.forEach(field => {
      if (cashRegisterData[field as keyof UpdateCashRegisterRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = cashRegisterData[field as keyof UpdateCashRegisterRequest];
        
        // Converter boolean para integer
        if (field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return cashRegister; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE cash_registers SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar caixa atualizado
    const updatedCashRegister = await this.findById(id);
    if (!updatedCashRegister) {
      throw new Error('Failed to retrieve updated cash register');
    }

    return updatedCashRegister;
  }

  // Deletar caixa (soft delete)
  static async delete(id: number): Promise<void> {
    const cashRegister = await this.findById(id);
    if (!cashRegister) {
      throw new Error('Cash register not found');
    }

    // Verificar se há sessões ativas neste caixa
    const activeSessions = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM cash_sessions WHERE cash_register_id = ? AND status = "open"',
      [id]
    );

    if (activeSessions && activeSessions.count > 0) {
      throw new Error('Cannot delete cash register with active sessions');
    }

    await executeUpdate(
      'UPDATE cash_registers SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Obter estatísticas do caixa
  static async getStats(id: number, dateFrom?: string, dateTo?: string): Promise<{
    total_sessions: number;
    total_sales: number;
    average_session_duration: number;
    utilization_percentage: number;
    current_status: 'available' | 'in_use';
  }> {
    let whereClause = 'WHERE cs.cash_register_id = ?';
    const params: any[] = [id];

    if (dateFrom) {
      whereClause += ' AND DATE(cs.opened_at) >= ?';
      params.push(dateFrom);
    }

    if (dateTo) {
      whereClause += ' AND DATE(cs.opened_at) <= ?';
      params.push(dateTo);
    }

    // Estatísticas básicas
    const basicStats = await executeQuerySingle<{
      total_sessions: number;
      total_sales: number;
      avg_duration: number;
    }>(
      `SELECT 
         COUNT(*) as total_sessions,
         COALESCE(SUM(cs.total_sales), 0) as total_sales,
         COALESCE(AVG(
           CASE 
             WHEN cs.closed_at IS NOT NULL THEN 
               (julianday(cs.closed_at) - julianday(cs.opened_at)) * 24
             ELSE NULL
           END
         ), 0) as avg_duration
       FROM cash_sessions cs 
       ${whereClause}`,
      params
    );

    // Status atual
    const activeSession = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM cash_sessions WHERE cash_register_id = ? AND status = "open"',
      [id]
    );

    const totalSessions = basicStats?.total_sessions || 0;
    const totalSales = basicStats?.total_sales || 0;
    const avgDuration = basicStats?.avg_duration || 0;
    const currentStatus = (activeSession?.count || 0) > 0 ? 'in_use' : 'available';

    // Calcular utilização (baseado em horas de operação)
    const operatingHours = 12; // TODO: Configurável
    const utilizationPercentage = avgDuration > 0 ? Math.min(100, (avgDuration / operatingHours) * 100) : 0;

    return {
      total_sessions: totalSessions,
      total_sales: totalSales,
      average_session_duration: avgDuration,
      utilization_percentage: utilizationPercentage,
      current_status: currentStatus as 'available' | 'in_use',
    };
  }

  // Buscar caixas com status
  static async findWithStatus(): Promise<Array<CashRegister & { 
    current_status: 'available' | 'in_use';
    active_session_id?: number;
    active_user_name?: string;
    session_duration?: number;
  }>> {
    const cashRegisters = await this.findAll();
    
    const cashRegistersWithStatus = await Promise.all(
      cashRegisters.map(async (cashRegister) => {
        // Buscar sessão ativa
        const activeSession = await executeQuerySingle<{
          id: number;
          user_name: string;
          opened_at: string;
        }>(
          `SELECT cs.id, u.full_name as user_name, cs.opened_at
           FROM cash_sessions cs
           LEFT JOIN users u ON cs.user_id = u.id
           WHERE cs.cash_register_id = ? AND cs.status = 'open'`,
          [cashRegister.id]
        );

        let sessionDuration = 0;
        if (activeSession) {
          const openedAt = new Date(activeSession.opened_at);
          const now = new Date();
          sessionDuration = (now.getTime() - openedAt.getTime()) / (1000 * 60 * 60); // horas
        }

        return {
          ...cashRegister,
          current_status: activeSession ? 'in_use' as const : 'available' as const,
          active_session_id: activeSession?.id,
          active_user_name: activeSession?.user_name,
          session_duration: sessionDuration,
        };
      })
    );

    return cashRegistersWithStatus;
  }

  // Verificar se caixa pode ser deletado
  static async canDelete(id: number): Promise<boolean> {
    const activeSessions = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM cash_sessions WHERE cash_register_id = ? AND status = "open"',
      [id]
    );

    return (activeSessions?.count || 0) === 0;
  }

  // Buscar caixas disponíveis
  static async findAvailable(): Promise<CashRegister[]> {
    return await executeQuery<CashRegister>(
      `SELECT cr.* 
       FROM cash_registers cr
       LEFT JOIN cash_sessions cs ON cr.id = cs.cash_register_id AND cs.status = 'open'
       WHERE cr.is_active = 1 AND cs.id IS NULL
       ORDER BY cr.name ASC`
    );
  }
}
