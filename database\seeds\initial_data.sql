-- Dados iniciais para o Sistema PDV Adib

-- Inserir usuário administrador padrão
INSERT OR IGNORE INTO users (id, username, password_hash, full_name, email, role, is_active) VALUES 
(1, 'admin', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrador', '<EMAIL>', 'manager', 1);
-- <PERSON><PERSON> padr<PERSON>: 'password' (deve ser alterada no primeiro login)

-- Inserir usuário caixa padrão
INSERT OR IGNORE INTO users (id, username, password_hash, full_name, email, role, is_active) VALUES 
(2, 'caixa', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Operador de Caixa', '<EMAIL>', 'cashier', 1);

-- Inserir usuário cozinha padrão
INSERT OR IGNORE INTO users (id, username, password_hash, full_name, email, role, is_active) VALUES 
(3, 'cozinha', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Cozinha', '<EMAIL>', 'kitchen', 1);

-- Inserir categorias padrão
INSERT OR IGNORE INTO categories (id, name, description, color, icon) VALUES 
(1, 'Lanches', 'Hambúrgueres, sanduíches e similares', '#FF5722', 'fastfood'),
(2, 'Bebidas', 'Refrigerantes, sucos e bebidas em geral', '#2196F3', 'local_drink'),
(3, 'Sobremesas', 'Doces, sorvetes e sobremesas', '#E91E63', 'cake'),
(4, 'Pratos Principais', 'Pratos executivos e refeições completas', '#4CAF50', 'restaurant'),
(5, 'Petiscos', 'Porções e aperitivos', '#FF9800', 'tapas');

-- Inserir produtos de exemplo
INSERT OR IGNORE INTO products (id, code, name, description, category_id, price, cost, unit, stock_control, current_stock, min_stock, preparation_time, ncm, cfop) VALUES 
(1, 'HAMB001', 'Hambúrguer Clássico', 'Hambúrguer com carne, queijo, alface e tomate', 1, 15.90, 8.50, 'UN', 1, 50, 10, 15, '21069090', '5102'),
(2, 'REFRI001', 'Coca-Cola 350ml', 'Refrigerante Coca-Cola lata 350ml', 2, 4.50, 2.20, 'UN', 1, 100, 20, 0, '22021000', '5102'),
(3, 'SOBR001', 'Pudim de Leite', 'Pudim de leite condensado caseiro', 3, 8.90, 4.20, 'UN', 1, 20, 5, 5, '19019000', '5102'),
(4, 'PRATO001', 'Prato Feito', 'Arroz, feijão, carne, salada e batata frita', 4, 18.90, 12.50, 'UN', 1, 30, 5, 20, '21069090', '5102'),
(5, 'PETIS001', 'Batata Frita', 'Porção de batata frita crocante', 5, 12.90, 6.80, 'UN', 1, 40, 10, 10, '20052000', '5102');

-- Inserir insumos de exemplo
INSERT OR IGNORE INTO ingredients (id, code, name, unit, cost_per_unit, current_stock, min_stock) VALUES 
(1, 'CARNE001', 'Carne Bovina (kg)', 'KG', 25.00, 10.5, 2.0),
(2, 'QUEIJO001', 'Queijo Mussarela (kg)', 'KG', 18.50, 5.2, 1.0),
(3, 'PAO001', 'Pão de Hambúrguer (un)', 'UN', 0.80, 100, 20),
(4, 'ALFACE001', 'Alface (maço)', 'UN', 2.50, 15, 5),
(5, 'TOMATE001', 'Tomate (kg)', 'KG', 4.20, 8.3, 2.0);

-- Inserir receitas (ficha técnica)
INSERT OR IGNORE INTO recipes (product_id, ingredient_id, quantity, unit, cost) VALUES 
(1, 1, 0.150, 'KG', 3.75),  -- Hambúrguer: 150g de carne
(1, 2, 0.050, 'KG', 0.93),  -- Hambúrguer: 50g de queijo
(1, 3, 1.000, 'UN', 0.80),  -- Hambúrguer: 1 pão
(1, 4, 0.100, 'UN', 0.25),  -- Hambúrguer: folhas de alface
(1, 5, 0.080, 'KG', 0.34);  -- Hambúrguer: 80g de tomate

-- Inserir mesas padrão
INSERT OR IGNORE INTO tables (id, number, name, capacity) VALUES 
(1, 1, 'Mesa 01', 4),
(2, 2, 'Mesa 02', 4),
(3, 3, 'Mesa 03', 2),
(4, 4, 'Mesa 04', 6),
(5, 5, 'Mesa 05', 4),
(6, 6, 'Mesa 06', 2),
(7, 7, 'Mesa 07', 8),
(8, 8, 'Mesa 08', 4);

-- Inserir configurações padrão do sistema
INSERT OR IGNORE INTO settings (key, value, description, type) VALUES 
('company_name', 'Adib PDV Demo', 'Nome da empresa', 'string'),
('company_cnpj', '00.000.000/0001-00', 'CNPJ da empresa', 'string'),
('company_ie', '*********', 'Inscrição Estadual', 'string'),
('company_address', 'Rua Demo, 123', 'Endereço da empresa', 'string'),
('company_city', 'São Paulo', 'Cidade da empresa', 'string'),
('company_state', 'SP', 'Estado da empresa', 'string'),
('company_zip', '01000-000', 'CEP da empresa', 'string'),
('company_phone', '(11) 99999-9999', 'Telefone da empresa', 'string'),
('company_email', '<EMAIL>', 'Email da empresa', 'string'),
('fiscal_environment', 'homologacao', 'Ambiente fiscal (homologacao/producao)', 'string'),
('fiscal_series_nfce', '1', 'Série da NFC-e', 'number'),
('fiscal_series_nfe', '1', 'Série da NF-e', 'number'),
('fiscal_next_number_nfce', '1', 'Próximo número NFC-e', 'number'),
('fiscal_next_number_nfe', '1', 'Próximo número NF-e', 'number'),
('delivery_fee_default', '5.00', 'Taxa de entrega padrão', 'number'),
('max_discount_percent', '10', 'Desconto máximo permitido (%)', 'number'),
('auto_print_kitchen', 'true', 'Imprimir automaticamente na cozinha', 'boolean'),
('backup_enabled', 'true', 'Backup automático habilitado', 'boolean'),
('backup_interval_hours', '6', 'Intervalo de backup (horas)', 'number'),
('license_type', 'free', 'Tipo de licença', 'string'),
('max_orders_per_month', '200', 'Máximo de pedidos por mês (licença gratuita)', 'number'),
('openai_enabled', 'false', 'Insights com IA habilitados', 'boolean'),
('tef_enabled', 'false', 'TEF habilitado', 'boolean'),
('kitchen_display_enabled', 'true', 'Display da cozinha habilitado', 'boolean');

-- Inserir cliente padrão para balcão
INSERT OR IGNORE INTO customers (id, name, email, phone, cpf_cnpj) VALUES 
(1, 'Cliente Balcão', '<EMAIL>', '', '000.000.000-00');

-- Atualizar sequências (SQLite)
UPDATE sqlite_sequence SET seq = 1 WHERE name = 'users';
UPDATE sqlite_sequence SET seq = 5 WHERE name = 'categories';
UPDATE sqlite_sequence SET seq = 5 WHERE name = 'products';
UPDATE sqlite_sequence SET seq = 5 WHERE name = 'ingredients';
UPDATE sqlite_sequence SET seq = 8 WHERE name = 'tables';
UPDATE sqlite_sequence SET seq = 1 WHERE name = 'customers';
