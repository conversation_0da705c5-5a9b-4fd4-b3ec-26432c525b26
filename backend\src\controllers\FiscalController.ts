import { Request, Response } from 'express';
import { FiscalDocumentModel } from '../models/FiscalDocument';
import { FiscalConfigModel } from '../models/FiscalConfig';
import { 
  CreateFiscalDocumentRequest, 
  UpdateFiscalDocumentRequest, 
  FiscalDocumentFilters,
  CreateFiscalConfigRequest,
  UpdateFiscalConfigRequest,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class FiscalController {
  // ===== DOCUMENTOS FISCAIS =====

  // Listar documentos fiscais
  static async getDocuments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: FiscalDocumentFilters = {
        type: req.query.type as any,
        status: req.query.status as any,
        order_id: req.query.order_id ? parseInt(req.query.order_id as string) : undefined,
        customer_id: req.query.customer_id ? parseInt(req.query.customer_id as string) : undefined,
        date_from: req.query.date_from as string,
        date_to: req.query.date_to as string,
        search: req.query.search as string,
      };

      const documents = await FiscalDocumentModel.findAll(filters);

      res.status(200).json({
        success: true,
        data: documents,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get fiscal documents error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get fiscal documents',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar documento fiscal por ID
  static async getDocument(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid fiscal document ID');
      }

      const document = await FiscalDocumentModel.findById(id);
      if (!document) {
        throw new NotFoundError('Fiscal document not found');
      }

      // Incluir itens se solicitado
      if (req.query.include_items === 'true') {
        (document as any).items = await FiscalDocumentModel.getDocumentItems(id);
      }

      res.status(200).json({
        success: true,
        data: document,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get fiscal document error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get fiscal document',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Criar documento fiscal
  static async createDocument(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const documentData: CreateFiscalDocumentRequest = req.body;

      // Validações básicas
      if (!documentData.type) {
        throw new ValidationError('Document type is required');
      }

      if (!documentData.items || documentData.items.length === 0) {
        throw new ValidationError('Document must have at least one item');
      }

      const document = await FiscalDocumentModel.create(documentData);

      console.log(`Fiscal document created: ${document.access_key} by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: document,
        message: 'Fiscal document created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create fiscal document error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create fiscal document',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar documento fiscal
  static async updateDocument(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const documentData: UpdateFiscalDocumentRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid fiscal document ID');
      }

      const document = await FiscalDocumentModel.update(id, documentData);

      console.log(`Fiscal document ${id} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: document,
        message: 'Fiscal document updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update fiscal document error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update fiscal document',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Cancelar documento fiscal
  static async cancelDocument(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const { reason } = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid fiscal document ID');
      }

      if (!reason || reason.trim().length === 0) {
        throw new ValidationError('Cancellation reason is required');
      }

      // Verificar se pode cancelar
      const canCancel = await FiscalDocumentModel.canCancel(id);
      if (!canCancel.can_cancel) {
        throw new ValidationError(canCancel.reason || 'Document cannot be cancelled');
      }

      const document = await FiscalDocumentModel.cancel(id, reason);

      console.log(`Fiscal document ${id} cancelled by user ${req.user?.username}: ${reason}`);

      res.status(200).json({
        success: true,
        data: document,
        message: 'Fiscal document cancelled successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Cancel fiscal document error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to cancel fiscal document',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar itens do documento
  static async getDocumentItems(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid fiscal document ID');
      }

      const items = await FiscalDocumentModel.getDocumentItems(id);

      res.status(200).json({
        success: true,
        data: items,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get document items error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get document items',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter métricas fiscais
  static async getMetrics(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const metrics = await FiscalDocumentModel.getMetrics();

      res.status(200).json({
        success: true,
        data: metrics,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get fiscal metrics error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get fiscal metrics',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // ===== CONFIGURAÇÕES FISCAIS =====

  // Listar configurações fiscais
  static async getConfigs(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const activeOnly = req.query.active_only !== 'false';
      const configs = await FiscalConfigModel.findAll(activeOnly);

      res.status(200).json({
        success: true,
        data: configs,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get fiscal configs error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get fiscal configs',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar configuração ativa
  static async getActiveConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const config = await FiscalConfigModel.findActive();

      if (!config) {
        res.status(200).json({
          success: true,
          data: null,
          message: 'No active fiscal configuration found',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
        return;
      }

      res.status(200).json({
        success: true,
        data: config,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get active fiscal config error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get active fiscal config',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Validar configuração fiscal
  static async validateConfig(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = req.params.id ? parseInt(req.params.id) : undefined;
      
      if (id && isNaN(id)) {
        throw new ValidationError('Invalid fiscal config ID');
      }

      const validation = await FiscalConfigModel.validate(id);

      res.status(200).json({
        success: true,
        data: validation,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Validate fiscal config error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to validate fiscal config',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Testar conexão SEFAZ
  static async testSefazConnection(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = req.params.id ? parseInt(req.params.id) : undefined;
      
      if (id && isNaN(id)) {
        throw new ValidationError('Invalid fiscal config ID');
      }

      const result = await FiscalConfigModel.testSefazConnection(id);

      res.status(200).json({
        success: true,
        data: result,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Test SEFAZ connection error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to test SEFAZ connection',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
