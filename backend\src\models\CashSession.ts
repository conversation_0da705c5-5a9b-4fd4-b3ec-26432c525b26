import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  CashSession, 
  CreateCashSessionRequest, 
  CloseCashSessionRequest, 
  CashSessionFilters,
  CashMovement,
  CreateCashMovementRequest,
  CashMetrics
} from '../types/cashier';

export class CashSessionModel {
  // Buscar sessão de caixa por ID
  static async findById(id: number): Promise<CashSession | null> {
    const session = await executeQuerySingle<CashSession>(
      `SELECT cs.*, cr.name as cash_register_name, u.full_name as user_name
       FROM cash_sessions cs
       LEFT JOIN cash_registers cr ON cs.cash_register_id = cr.id
       LEFT JOIN users u ON cs.user_id = u.id
       WHERE cs.id = ?`,
      [id]
    );
    return session || null;
  }

  // Buscar sessão ativa por usuário
  static async findActiveByUser(userId: number): Promise<CashSession | null> {
    const session = await executeQuerySingle<CashSession>(
      `SELECT cs.*, cr.name as cash_register_name, u.full_name as user_name
       FROM cash_sessions cs
       LEFT JOIN cash_registers cr ON cs.cash_register_id = cr.id
       LEFT JOIN users u ON cs.user_id = u.id
       WHERE cs.user_id = ? AND cs.status = 'open'
       ORDER BY cs.opened_at DESC
       LIMIT 1`,
      [userId]
    );
    return session || null;
  }

  // Buscar sessão ativa por caixa
  static async findActiveByCashRegister(cashRegisterId: number): Promise<CashSession | null> {
    const session = await executeQuerySingle<CashSession>(
      `SELECT cs.*, cr.name as cash_register_name, u.full_name as user_name
       FROM cash_sessions cs
       LEFT JOIN cash_registers cr ON cs.cash_register_id = cr.id
       LEFT JOIN users u ON cs.user_id = u.id
       WHERE cs.cash_register_id = ? AND cs.status = 'open'
       ORDER BY cs.opened_at DESC
       LIMIT 1`,
      [cashRegisterId]
    );
    return session || null;
  }

  // Listar sessões com filtros
  static async findAll(filters: CashSessionFilters = {}): Promise<CashSession[]> {
    let query = `
      SELECT cs.*, cr.name as cash_register_name, u.full_name as user_name
      FROM cash_sessions cs
      LEFT JOIN cash_registers cr ON cs.cash_register_id = cr.id
      LEFT JOIN users u ON cs.user_id = u.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.status) {
      query += ' AND cs.status = ?';
      params.push(filters.status);
    }

    if (filters.user_id) {
      query += ' AND cs.user_id = ?';
      params.push(filters.user_id);
    }

    if (filters.cash_register_id) {
      query += ' AND cs.cash_register_id = ?';
      params.push(filters.cash_register_id);
    }

    if (filters.date_from) {
      query += ' AND DATE(cs.opened_at) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(cs.opened_at) <= ?';
      params.push(filters.date_to);
    }

    if (filters.search) {
      query += ' AND (u.full_name LIKE ? OR cr.name LIKE ? OR cs.notes LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY cs.opened_at DESC';

    return await executeQuery<CashSession>(query, params);
  }

  // Abrir sessão de caixa
  static async open(sessionData: CreateCashSessionRequest, userId: number): Promise<CashSession> {
    return await executeTransaction(async (db) => {
      // Verificar se usuário já tem sessão aberta
      const existingSession = await this.findActiveByUser(userId);
      if (existingSession) {
        throw new Error('User already has an active cash session');
      }

      // Verificar se caixa já está em uso
      const activeCashSession = await this.findActiveByCashRegister(sessionData.cash_register_id);
      if (activeCashSession) {
        throw new Error('Cash register is already in use');
      }

      // Verificar se caixa existe e está ativo
      const cashRegister = await executeQuerySingle<{ is_active: boolean }>(
        'SELECT is_active FROM cash_registers WHERE id = ?',
        [sessionData.cash_register_id]
      );

      if (!cashRegister) {
        throw new Error('Cash register not found');
      }

      if (!cashRegister.is_active) {
        throw new Error('Cash register is not active');
      }

      // Inserir sessão de caixa
      const result = await executeUpdate(
        `INSERT INTO cash_sessions (
          cash_register_id, user_id, status, opening_balance, 
          total_sales, total_cash_sales, total_card_sales, total_pix_sales, 
          total_other_sales, total_withdrawals, total_deposits, 
          opened_at, notes
        ) VALUES (?, ?, 'open', ?, 0, 0, 0, 0, 0, 0, 0, CURRENT_TIMESTAMP, ?)`,
        [
          sessionData.cash_register_id,
          userId,
          sessionData.opening_balance,
          sessionData.notes || null,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create cash session');
      }

      // Registrar movimento de abertura
      await this.addMovement({
        cash_session_id: result.lastID,
        type: 'in',
        category: 'opening',
        amount: sessionData.opening_balance,
        description: 'Opening balance',
      }, userId);

      // Retornar sessão criada
      const newSession = await this.findById(result.lastID);
      if (!newSession) {
        throw new Error('Failed to retrieve created cash session');
      }

      return newSession;
    });
  }

  // Fechar sessão de caixa
  static async close(sessionId: number, closeData: CloseCashSessionRequest, userId: number): Promise<CashSession> {
    return await executeTransaction(async (db) => {
      const session = await this.findById(sessionId);
      if (!session) {
        throw new Error('Cash session not found');
      }

      if (session.status === 'closed') {
        throw new Error('Cash session is already closed');
      }

      if (session.user_id !== userId) {
        throw new Error('Only the session owner can close it');
      }

      // Calcular saldo esperado
      const expectedBalance = session.opening_balance + session.total_cash_sales - session.total_withdrawals + session.total_deposits;
      const difference = closeData.closing_balance - expectedBalance;

      // Atualizar sessão
      await executeUpdate(
        `UPDATE cash_sessions SET 
           status = 'closed',
           closing_balance = ?,
           expected_balance = ?,
           difference = ?,
           closed_at = CURRENT_TIMESTAMP,
           notes = ?,
           updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          closeData.closing_balance,
          expectedBalance,
          difference,
          closeData.notes || session.notes,
          sessionId,
        ]
      );

      // Registrar movimento de fechamento se houver diferença
      if (difference !== 0) {
        await this.addMovement({
          cash_session_id: sessionId,
          type: difference > 0 ? 'in' : 'out',
          category: 'adjustment',
          amount: Math.abs(difference),
          description: `Closing adjustment: ${difference > 0 ? 'surplus' : 'shortage'}`,
        }, userId);
      }

      // Retornar sessão atualizada
      const updatedSession = await this.findById(sessionId);
      if (!updatedSession) {
        throw new Error('Failed to retrieve updated cash session');
      }

      return updatedSession;
    });
  }

  // Adicionar movimento de caixa
  static async addMovement(movementData: CreateCashMovementRequest, userId: number): Promise<CashMovement> {
    return await executeTransaction(async (db) => {
      // Verificar se sessão existe e está aberta
      const session = await this.findById(movementData.cash_session_id);
      if (!session) {
        throw new Error('Cash session not found');
      }

      if (session.status === 'closed') {
        throw new Error('Cannot add movement to closed session');
      }

      // Inserir movimento
      const result = await executeUpdate(
        `INSERT INTO cash_movements (
          cash_session_id, type, category, amount, description, 
          reference_type, reference_id, user_id
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          movementData.cash_session_id,
          movementData.type,
          movementData.category,
          movementData.amount,
          movementData.description,
          movementData.reference_type || null,
          movementData.reference_id || null,
          userId,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create cash movement');
      }

      // Atualizar totais da sessão
      await this.updateSessionTotals(movementData.cash_session_id);

      // Retornar movimento criado
      const newMovement = await executeQuerySingle<CashMovement>(
        `SELECT cm.*, u.full_name as user_name
         FROM cash_movements cm
         LEFT JOIN users u ON cm.user_id = u.id
         WHERE cm.id = ?`,
        [result.lastID]
      );

      if (!newMovement) {
        throw new Error('Failed to retrieve created cash movement');
      }

      return newMovement;
    });
  }

  // Atualizar totais da sessão
  static async updateSessionTotals(sessionId: number): Promise<void> {
    // Calcular totais por categoria
    const totals = await executeQuerySingle<{
      total_sales: number;
      total_cash_sales: number;
      total_card_sales: number;
      total_pix_sales: number;
      total_other_sales: number;
      total_withdrawals: number;
      total_deposits: number;
    }>(
      `SELECT 
         COALESCE(SUM(CASE WHEN category = 'sale' THEN amount ELSE 0 END), 0) as total_sales,
         COALESCE(SUM(CASE WHEN category = 'sale' AND reference_type = 'cash' THEN amount ELSE 0 END), 0) as total_cash_sales,
         COALESCE(SUM(CASE WHEN category = 'sale' AND reference_type = 'card' THEN amount ELSE 0 END), 0) as total_card_sales,
         COALESCE(SUM(CASE WHEN category = 'sale' AND reference_type = 'pix' THEN amount ELSE 0 END), 0) as total_pix_sales,
         COALESCE(SUM(CASE WHEN category = 'sale' AND reference_type NOT IN ('cash', 'card', 'pix') THEN amount ELSE 0 END), 0) as total_other_sales,
         COALESCE(SUM(CASE WHEN category = 'withdrawal' THEN amount ELSE 0 END), 0) as total_withdrawals,
         COALESCE(SUM(CASE WHEN category = 'deposit' THEN amount ELSE 0 END), 0) as total_deposits
       FROM cash_movements 
       WHERE cash_session_id = ? AND type = 'in'`,
      [sessionId]
    );

    if (totals) {
      await executeUpdate(
        `UPDATE cash_sessions SET 
           total_sales = ?,
           total_cash_sales = ?,
           total_card_sales = ?,
           total_pix_sales = ?,
           total_other_sales = ?,
           total_withdrawals = ?,
           total_deposits = ?,
           updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          totals.total_sales,
          totals.total_cash_sales,
          totals.total_card_sales,
          totals.total_pix_sales,
          totals.total_other_sales,
          totals.total_withdrawals,
          totals.total_deposits,
          sessionId,
        ]
      );
    }
  }

  // Buscar movimentos da sessão
  static async getMovements(sessionId: number): Promise<CashMovement[]> {
    return await executeQuery<CashMovement>(
      `SELECT cm.*, u.full_name as user_name
       FROM cash_movements cm
       LEFT JOIN users u ON cm.user_id = u.id
       WHERE cm.cash_session_id = ?
       ORDER BY cm.created_at ASC`,
      [sessionId]
    );
  }

  // Obter métricas de caixa
  static async getMetrics(): Promise<CashMetrics> {
    // Sessões ativas
    const activeSessions = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM cash_sessions WHERE status = "open"'
    );

    // Vendas do dia
    const todaySales = await executeQuerySingle<{
      total_cash: number;
      total_sales: number;
      transactions: number;
      largest_sale: number;
    }>(
      `SELECT 
         COALESCE(SUM(cs.total_cash_sales), 0) as total_cash,
         COALESCE(SUM(cs.total_sales), 0) as total_sales,
         COALESCE(COUNT(cs.id), 0) as transactions,
         COALESCE(MAX(cs.total_sales), 0) as largest_sale
       FROM cash_sessions cs
       WHERE DATE(cs.opened_at) = DATE('now')`
    );

    // Dinheiro em caixas
    const cashInRegisters = await executeQuerySingle<{ total: number }>(
      `SELECT COALESCE(SUM(
         cs.opening_balance + cs.total_cash_sales - cs.total_withdrawals + cs.total_deposits
       ), 0) as total
       FROM cash_sessions cs
       WHERE cs.status = 'open'`
    );

    // Métodos de pagamento do dia
    const paymentMethods = await executeQuery<{
      method: string;
      amount: number;
    }>(
      `SELECT 
         CASE 
           WHEN reference_type = 'cash' THEN 'Dinheiro'
           WHEN reference_type = 'card' THEN 'Cartão'
           WHEN reference_type = 'pix' THEN 'PIX'
           ELSE 'Outros'
         END as method,
         SUM(amount) as amount
       FROM cash_movements cm
       JOIN cash_sessions cs ON cm.cash_session_id = cs.id
       WHERE cm.category = 'sale' 
         AND DATE(cm.created_at) = DATE('now')
       GROUP BY reference_type`
    );

    const totalSales = todaySales?.total_sales || 0;
    const paymentMethodsWithPercentage = paymentMethods.map(pm => ({
      ...pm,
      percentage: totalSales > 0 ? (pm.amount / totalSales) * 100 : 0,
    }));

    return {
      active_sessions: activeSessions?.count || 0,
      total_cash_today: todaySales?.total_cash || 0,
      total_sales_today: totalSales,
      average_ticket_today: todaySales?.transactions ? totalSales / todaySales.transactions : 0,
      transactions_today: todaySales?.transactions || 0,
      largest_sale_today: todaySales?.largest_sale || 0,
      cash_in_registers: cashInRegisters?.total || 0,
      pending_withdrawals: 0, // TODO: Implementar
      sessions_by_status: {
        open: activeSessions?.count || 0,
        closed: 0, // TODO: Implementar
      },
      payment_methods_today: paymentMethodsWithPercentage,
    };
  }

  // Buscar sessões ativas
  static async findActive(): Promise<CashSession[]> {
    return await this.findAll({ status: 'open' });
  }

  // Buscar sessões do dia
  static async findToday(): Promise<CashSession[]> {
    const today = new Date().toISOString().slice(0, 10);
    return await this.findAll({
      date_from: today,
      date_to: today,
    });
  }

  // Verificar se usuário pode abrir caixa
  static async canUserOpenSession(userId: number, cashRegisterId: number): Promise<{
    can_open: boolean;
    reason?: string;
  }> {
    // Verificar se usuário já tem sessão aberta
    const existingSession = await this.findActiveByUser(userId);
    if (existingSession) {
      return {
        can_open: false,
        reason: 'User already has an active session',
      };
    }

    // Verificar se caixa está disponível
    const activeCashSession = await this.findActiveByCashRegister(cashRegisterId);
    if (activeCashSession) {
      return {
        can_open: false,
        reason: 'Cash register is already in use',
      };
    }

    return { can_open: true };
  }
}
