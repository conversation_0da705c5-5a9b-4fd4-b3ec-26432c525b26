import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  Category, 
  CreateCategoryRequest, 
  UpdateCategoryRequest, 
  CategoryFilters 
} from '../types/products';

export class CategoryModel {
  // Buscar categoria por ID
  static async findById(id: number): Promise<Category | null> {
    const category = await executeQuerySingle<Category>(
      'SELECT * FROM categories WHERE id = ?',
      [id]
    );
    return category || null;
  }

  // Buscar categoria por nome
  static async findByName(name: string): Promise<Category | null> {
    const category = await executeQuerySingle<Category>(
      'SELECT * FROM categories WHERE name = ? AND is_active = 1',
      [name]
    );
    return category || null;
  }

  // Listar categorias com filtros
  static async findAll(filters: CategoryFilters = {}): Promise<Category[]> {
    let query = 'SELECT * FROM categories WHERE 1=1';
    const params: any[] = [];

    if (filters.is_active !== undefined) {
      query += ' AND is_active = ?';
      params.push(filters.is_active ? 1 : 0);
    }

    if (filters.search) {
      query += ' AND (name LIKE ? OR description LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm);
    }

    query += ' ORDER BY name ASC';

    return await executeQuery<Category>(query, params);
  }

  // Criar categoria
  static async create(categoryData: CreateCategoryRequest): Promise<Category> {
    // Verificar se nome já existe
    const existingCategory = await this.findByName(categoryData.name);
    if (existingCategory) {
      throw new Error('Category name already exists');
    }

    // Inserir categoria
    const result = await executeUpdate(
      `INSERT INTO categories (name, description, color, icon, is_active)
       VALUES (?, ?, ?, ?, 1)`,
      [
        categoryData.name,
        categoryData.description || null,
        categoryData.color || '#2196F3',
        categoryData.icon || 'category',
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create category');
    }

    // Retornar categoria criada
    const newCategory = await this.findById(result.lastID);
    if (!newCategory) {
      throw new Error('Failed to retrieve created category');
    }

    return newCategory;
  }

  // Atualizar categoria
  static async update(id: number, categoryData: UpdateCategoryRequest): Promise<Category> {
    const category = await this.findById(id);
    if (!category) {
      throw new Error('Category not found');
    }

    // Verificar se novo nome já existe (se estiver sendo alterado)
    if (categoryData.name && categoryData.name !== category.name) {
      const existingCategory = await this.findByName(categoryData.name);
      if (existingCategory) {
        throw new Error('Category name already exists');
      }
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    if (categoryData.name !== undefined) {
      updateFields.push('name = ?');
      params.push(categoryData.name);
    }

    if (categoryData.description !== undefined) {
      updateFields.push('description = ?');
      params.push(categoryData.description);
    }

    if (categoryData.color !== undefined) {
      updateFields.push('color = ?');
      params.push(categoryData.color);
    }

    if (categoryData.icon !== undefined) {
      updateFields.push('icon = ?');
      params.push(categoryData.icon);
    }

    if (categoryData.is_active !== undefined) {
      updateFields.push('is_active = ?');
      params.push(categoryData.is_active ? 1 : 0);
    }

    if (updateFields.length === 0) {
      return category; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE categories SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar categoria atualizada
    const updatedCategory = await this.findById(id);
    if (!updatedCategory) {
      throw new Error('Failed to retrieve updated category');
    }

    return updatedCategory;
  }

  // Deletar categoria (soft delete)
  static async delete(id: number): Promise<void> {
    const category = await this.findById(id);
    if (!category) {
      throw new Error('Category not found');
    }

    // Verificar se há produtos usando esta categoria
    const productsCount = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM products WHERE category_id = ? AND is_active = 1',
      [id]
    );

    if (productsCount && productsCount.count > 0) {
      throw new Error('Cannot delete category with active products');
    }

    await executeUpdate(
      'UPDATE categories SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Contar produtos por categoria
  static async getProductCount(id: number): Promise<number> {
    const result = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM products WHERE category_id = ? AND is_active = 1',
      [id]
    );
    return result?.count || 0;
  }

  // Obter categorias com contagem de produtos
  static async findAllWithProductCount(): Promise<Array<Category & { product_count: number }>> {
    const categories = await executeQuery<Category & { product_count: number }>(
      `SELECT c.*, 
              COALESCE(p.product_count, 0) as product_count
       FROM categories c
       LEFT JOIN (
         SELECT category_id, COUNT(*) as product_count
         FROM products 
         WHERE is_active = 1
         GROUP BY category_id
       ) p ON c.id = p.category_id
       WHERE c.is_active = 1
       ORDER BY c.name ASC`
    );

    return categories;
  }

  // Verificar se categoria pode ser deletada
  static async canDelete(id: number): Promise<boolean> {
    const productCount = await this.getProductCount(id);
    return productCount === 0;
  }

  // Obter estatísticas da categoria
  static async getStats(id: number): Promise<{
    product_count: number;
    total_value: number;
    avg_price: number;
  }> {
    const stats = await executeQuerySingle<{
      product_count: number;
      total_value: number;
      avg_price: number;
    }>(
      `SELECT 
         COUNT(*) as product_count,
         COALESCE(SUM(price * current_stock), 0) as total_value,
         COALESCE(AVG(price), 0) as avg_price
       FROM products 
       WHERE category_id = ? AND is_active = 1`,
      [id]
    );

    return stats || { product_count: 0, total_value: 0, avg_price: 0 };
  }

  // Buscar categorias mais usadas
  static async findMostUsed(limit: number = 5): Promise<Array<Category & { product_count: number }>> {
    const categories = await executeQuery<Category & { product_count: number }>(
      `SELECT c.*, COUNT(p.id) as product_count
       FROM categories c
       LEFT JOIN products p ON c.id = p.category_id AND p.is_active = 1
       WHERE c.is_active = 1
       GROUP BY c.id
       ORDER BY product_count DESC, c.name ASC
       LIMIT ?`,
      [limit]
    );

    return categories;
  }
}
