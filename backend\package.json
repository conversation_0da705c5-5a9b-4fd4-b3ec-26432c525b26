{"name": "adib-pdv-backend", "version": "1.0.0", "description": "Backend do Sistema PDV Adib - Node.js + Express + SQLite", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "lint:fix": "eslint src --ext .ts --fix", "db:migrate": "node dist/database/migrate.js", "db:seed": "node dist/database/seed.js", "db:reset": "node dist/database/reset.js", "db:backup": "node dist/database/backup.js"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "compression": "^1.7.4", "dotenv": "^16.3.1", "sqlite3": "^5.1.6", "knex": "^3.0.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "socket.io": "^4.7.4", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/compression": "^1.7.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.4", "@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8", "ts-jest": "^29.1.1", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2", "supertest": "^6.3.3", "@types/supertest": "^2.0.16"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/server.ts"]}, "nodemonConfig": {"watch": ["src"], "ext": "ts", "ignore": ["src/**/*.test.ts"], "exec": "ts-node src/server.ts"}}