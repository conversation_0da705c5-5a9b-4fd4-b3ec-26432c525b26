import { Router } from 'express';
import { CashierController } from '../controllers/CashierController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para sessões de caixa
const openSessionValidation = [
  body('cash_register_id')
    .isInt({ min: 1 })
    .withMessage('Cash register ID must be a positive integer'),
  body('opening_balance')
    .isFloat({ min: 0 })
    .withMessage('Opening balance must be 0 or greater'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
];

const closeSessionValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Cash session ID must be a positive integer'),
  body('closing_balance')
    .isFloat({ min: 0 })
    .withMessage('Closing balance must be 0 or greater'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
];

const addMovementValidation = [
  body('cash_session_id')
    .isInt({ min: 1 })
    .withMessage('Cash session ID must be a positive integer'),
  body('type')
    .isIn(['in', 'out'])
    .withMessage('Movement type must be in or out'),
  body('category')
    .isIn(['sale', 'withdrawal', 'deposit', 'change', 'opening', 'closing', 'adjustment', 'refund', 'expense', 'other'])
    .withMessage('Invalid movement category'),
  body('amount')
    .isFloat({ min: 0.01 })
    .withMessage('Amount must be greater than 0'),
  body('description')
    .notEmpty()
    .withMessage('Description is required')
    .isLength({ max: 200 })
    .withMessage('Description must be at most 200 characters'),
  body('reference_type')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Reference type must be at most 50 characters'),
  body('reference_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Reference ID must be a positive integer'),
];

// Validações para caixas registradoras
const createRegisterValidation = [
  body('name')
    .notEmpty()
    .withMessage('Cash register name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Cash register name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be at most 500 characters'),
];

const updateRegisterValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Cash register ID must be a positive integer'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Cash register name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be at most 500 characters'),
];

// ===== ROTAS DE SESSÕES DE CAIXA =====

/**
 * @route GET /api/cashier/sessions
 * @desc Listar sessões de caixa
 * @access Private (read cashier)
 */
router.get('/sessions',
  authenticate,
  authorize('cashier', 'read'),
  CashierController.getSessions
);

/**
 * @route GET /api/cashier/sessions/active
 * @desc Buscar sessão ativa do usuário
 * @access Private (read cashier)
 */
router.get('/sessions/active',
  authenticate,
  authorize('cashier', 'read'),
  CashierController.getActiveSession
);

/**
 * @route GET /api/cashier/metrics
 * @desc Obter métricas de caixa
 * @access Private (read cashier)
 */
router.get('/metrics',
  authenticate,
  authorize('cashier', 'read'),
  CashierController.getMetrics
);

/**
 * @route GET /api/cashier/sessions/:id
 * @desc Buscar sessão de caixa por ID
 * @access Private (read cashier)
 */
router.get('/sessions/:id',
  authenticate,
  authorize('cashier', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  CashierController.getSession
);

/**
 * @route POST /api/cashier/sessions
 * @desc Abrir sessão de caixa
 * @access Private (create cashier)
 */
router.post('/sessions',
  authenticate,
  authorize('cashier', 'create'),
  openSessionValidation,
  validateRequest,
  logUserAction('open_cash_session'),
  CashierController.openSession
);

/**
 * @route PUT /api/cashier/sessions/:id/close
 * @desc Fechar sessão de caixa
 * @access Private (update cashier)
 */
router.put('/sessions/:id/close',
  authenticate,
  authorize('cashier', 'update'),
  closeSessionValidation,
  validateRequest,
  logUserAction('close_cash_session'),
  CashierController.closeSession
);

/**
 * @route GET /api/cashier/sessions/:id/movements
 * @desc Buscar movimentos da sessão de caixa
 * @access Private (read cashier)
 */
router.get('/sessions/:id/movements',
  authenticate,
  authorize('cashier', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  CashierController.getMovements
);

/**
 * @route POST /api/cashier/movements
 * @desc Adicionar movimento de caixa
 * @access Private (create cashier)
 */
router.post('/movements',
  authenticate,
  authorize('cashier', 'create'),
  addMovementValidation,
  validateRequest,
  logUserAction('add_cash_movement'),
  CashierController.addMovement
);

// ===== ROTAS DE CAIXAS REGISTRADORAS =====

/**
 * @route GET /api/cashier/registers
 * @desc Listar caixas registradoras
 * @access Private (read cashier)
 */
router.get('/registers',
  authenticate,
  authorize('cashier', 'read'),
  CashierController.getRegisters
);

/**
 * @route GET /api/cashier/registers/available
 * @desc Buscar caixas disponíveis
 * @access Private (read cashier)
 */
router.get('/registers/available',
  authenticate,
  authorize('cashier', 'read'),
  CashierController.getAvailableRegisters
);

/**
 * @route GET /api/cashier/registers/:id
 * @desc Buscar caixa registradora por ID
 * @access Private (read cashier)
 */
router.get('/registers/:id',
  authenticate,
  authorize('cashier', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  CashierController.getRegister
);

export default router;
