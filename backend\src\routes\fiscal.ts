import { Router } from 'express';
import { FiscalController } from '../controllers/FiscalController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para documentos fiscais
const createDocumentValidation = [
  body('type')
    .isIn(['nfce', 'nfe', 'receipt'])
    .withMessage('Invalid document type'),
  body('order_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Order ID must be a positive integer'),
  body('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('Document must have at least one item'),
  body('items.*.product_id')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  body('items.*.quantity')
    .isFloat({ min: 0.01 })
    .withMessage('Quantity must be greater than 0'),
  body('items.*.unit_price')
    .isFloat({ min: 0.01 })
    .withMessage('Unit price must be greater than 0'),
  body('items.*.cfop')
    .optional()
    .isLength({ min: 4, max: 4 })
    .withMessage('CFOP must be 4 digits'),
  body('items.*.tax_rate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Tax rate must be between 0 and 100'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
];

const updateDocumentValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Document ID must be a positive integer'),
  body('status')
    .optional()
    .isIn(['pending', 'processing', 'authorized', 'rejected', 'cancelled', 'contingency'])
    .withMessage('Invalid document status'),
  body('protocol')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Protocol must be at most 100 characters'),
  body('rejection_reason')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Rejection reason must be at most 500 characters'),
];

const cancelDocumentValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Document ID must be a positive integer'),
  body('reason')
    .notEmpty()
    .withMessage('Cancellation reason is required')
    .isLength({ min: 10, max: 255 })
    .withMessage('Cancellation reason must be between 10 and 255 characters'),
];

// Validações para configurações fiscais
const createConfigValidation = [
  body('company_name')
    .notEmpty()
    .withMessage('Company name is required')
    .isLength({ min: 2, max: 200 })
    .withMessage('Company name must be between 2 and 200 characters'),
  body('company_document')
    .notEmpty()
    .withMessage('Company document (CNPJ) is required')
    .isLength({ min: 14, max: 18 })
    .withMessage('Invalid CNPJ format'),
  body('company_ie')
    .notEmpty()
    .withMessage('State registration (IE) is required')
    .isLength({ min: 8, max: 15 })
    .withMessage('Invalid IE format'),
  body('company_address')
    .notEmpty()
    .withMessage('Company address is required')
    .isLength({ max: 200 })
    .withMessage('Address must be at most 200 characters'),
  body('company_city')
    .notEmpty()
    .withMessage('Company city is required')
    .isLength({ max: 100 })
    .withMessage('City must be at most 100 characters'),
  body('company_state')
    .notEmpty()
    .withMessage('Company state is required')
    .isLength({ min: 2, max: 2 })
    .withMessage('State must be 2 characters'),
  body('company_zip')
    .notEmpty()
    .withMessage('Company ZIP code is required')
    .isLength({ min: 8, max: 10 })
    .withMessage('Invalid ZIP code format'),
  body('company_phone')
    .optional()
    .isLength({ max: 20 })
    .withMessage('Phone must be at most 20 characters'),
  body('company_email')
    .optional()
    .isEmail()
    .withMessage('Invalid email format'),
  body('environment')
    .optional()
    .isIn(['production', 'homologation'])
    .withMessage('Environment must be production or homologation'),
  body('nfce_series')
    .optional()
    .isInt({ min: 1, max: 999 })
    .withMessage('NFC-e series must be between 1 and 999'),
  body('nfe_series')
    .optional()
    .isInt({ min: 1, max: 999 })
    .withMessage('NF-e series must be between 1 and 999'),
];

// ===== ROTAS DE DOCUMENTOS FISCAIS =====

/**
 * @route GET /api/fiscal/documents
 * @desc Listar documentos fiscais
 * @access Private (read fiscal)
 */
router.get('/documents',
  authenticate,
  authorize('fiscal', 'read'),
  FiscalController.getDocuments
);

/**
 * @route GET /api/fiscal/metrics
 * @desc Obter métricas fiscais
 * @access Private (read fiscal)
 */
router.get('/metrics',
  authenticate,
  authorize('fiscal', 'read'),
  FiscalController.getMetrics
);

/**
 * @route GET /api/fiscal/documents/:id
 * @desc Buscar documento fiscal por ID
 * @access Private (read fiscal)
 */
router.get('/documents/:id',
  authenticate,
  authorize('fiscal', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  FiscalController.getDocument
);

/**
 * @route POST /api/fiscal/documents
 * @desc Criar documento fiscal
 * @access Private (create fiscal)
 */
router.post('/documents',
  authenticate,
  authorize('fiscal', 'create'),
  createDocumentValidation,
  validateRequest,
  logUserAction('create_fiscal_document'),
  FiscalController.createDocument
);

/**
 * @route PUT /api/fiscal/documents/:id
 * @desc Atualizar documento fiscal
 * @access Private (update fiscal)
 */
router.put('/documents/:id',
  authenticate,
  authorize('fiscal', 'update'),
  updateDocumentValidation,
  validateRequest,
  logUserAction('update_fiscal_document'),
  FiscalController.updateDocument
);

/**
 * @route DELETE /api/fiscal/documents/:id
 * @desc Cancelar documento fiscal
 * @access Private (delete fiscal)
 */
router.delete('/documents/:id',
  authenticate,
  authorize('fiscal', 'delete'),
  cancelDocumentValidation,
  validateRequest,
  logUserAction('cancel_fiscal_document'),
  FiscalController.cancelDocument
);

/**
 * @route GET /api/fiscal/documents/:id/items
 * @desc Buscar itens do documento fiscal
 * @access Private (read fiscal)
 */
router.get('/documents/:id/items',
  authenticate,
  authorize('fiscal', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  FiscalController.getDocumentItems
);

// ===== ROTAS DE CONFIGURAÇÕES FISCAIS =====

/**
 * @route GET /api/fiscal/configs
 * @desc Listar configurações fiscais
 * @access Private (read fiscal)
 */
router.get('/configs',
  authenticate,
  authorize('fiscal', 'read'),
  FiscalController.getConfigs
);

/**
 * @route GET /api/fiscal/configs/active
 * @desc Buscar configuração fiscal ativa
 * @access Private (read fiscal)
 */
router.get('/configs/active',
  authenticate,
  authorize('fiscal', 'read'),
  FiscalController.getActiveConfig
);

/**
 * @route GET /api/fiscal/configs/validate/:id?
 * @desc Validar configuração fiscal
 * @access Private (read fiscal)
 */
router.get('/configs/validate/:id?',
  authenticate,
  authorize('fiscal', 'read'),
  FiscalController.validateConfig
);

/**
 * @route GET /api/fiscal/configs/test-sefaz/:id?
 * @desc Testar conexão com SEFAZ
 * @access Private (read fiscal)
 */
router.get('/configs/test-sefaz/:id?',
  authenticate,
  authorize('fiscal', 'read'),
  FiscalController.testSefazConnection
);

export default router;
