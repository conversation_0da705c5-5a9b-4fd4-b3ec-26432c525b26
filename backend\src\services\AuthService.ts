import jwt from 'jsonwebtoken';
import { UserModel } from '../models/User';
import { 
  User, 
  LoginRequest, 
  LoginResponse, 
  JWTPayload, 
  AuthenticationError,
  ValidationError 
} from '../types';

export class AuthService {
  private static readonly JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key';
  private static readonly JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '24h';

  // Fazer login
  static async login(credentials: LoginRequest): Promise<LoginResponse> {
    const { username, password } = credentials;

    // Validar entrada
    if (!username || !password) {
      throw new ValidationError('Username and password are required');
    }

    // Verificar credenciais
    const user = await UserModel.verifyPassword(username, password);
    if (!user) {
      throw new AuthenticationError('Invalid username or password');
    }

    // Verificar se usuário está ativo
    if (!user.is_active) {
      throw new AuthenticationError('User account is disabled');
    }

    // Gerar token JWT
    const token = this.generateToken(user);

    return {
      user,
      token,
      expires_in: this.JWT_EXPIRES_IN,
    };
  }

  // Gerar token JWT
  static generateToken(user: User): string {
    const payload: Omit<JWTPayload, 'iat' | 'exp'> = {
      userId: user.id,
      username: user.username,
      role: user.role,
    };

    return jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.JWT_EXPIRES_IN,
    });
  }

  // Verificar token JWT
  static verifyToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.JWT_SECRET) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Token expired');
      }
      if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid token');
      }
      throw new AuthenticationError('Token verification failed');
    }
  }

  // Renovar token
  static async refreshToken(token: string): Promise<LoginResponse> {
    const payload = this.verifyToken(token);
    
    // Buscar usuário atualizado
    const user = await UserModel.findById(payload.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.is_active) {
      throw new AuthenticationError('User account is disabled');
    }

    // Gerar novo token
    const newToken = this.generateToken(user);

    return {
      user,
      token: newToken,
      expires_in: this.JWT_EXPIRES_IN,
    };
  }

  // Validar permissões
  static hasPermission(userRole: string, resource: string, action: string): boolean {
    const { ROLE_PERMISSIONS } = require('../types');
    
    const permissions = ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS];
    if (!permissions) {
      return false;
    }

    const resourcePermission = permissions.find(p => p.resource === resource);
    if (!resourcePermission) {
      return false;
    }

    return resourcePermission.actions.includes(action);
  }

  // Middleware para verificar autenticação
  static async authenticate(token: string): Promise<User> {
    if (!token) {
      throw new AuthenticationError('No token provided');
    }

    // Remover 'Bearer ' se presente
    const cleanToken = token.startsWith('Bearer ') ? token.slice(7) : token;

    // Verificar token
    const payload = this.verifyToken(cleanToken);

    // Buscar usuário
    const user = await UserModel.findById(payload.userId);
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    if (!user.is_active) {
      throw new AuthenticationError('User account is disabled');
    }

    return user;
  }

  // Logout (invalidar token - em uma implementação real, você manteria uma blacklist)
  static async logout(token: string): Promise<void> {
    // Em uma implementação real, você adicionaria o token a uma blacklist
    // Por enquanto, apenas validamos se o token é válido
    this.verifyToken(token);
  }

  // Verificar se usuário pode acessar recurso
  static canAccess(user: User, resource: string, action: string): boolean {
    return this.hasPermission(user.role, resource, action);
  }

  // Obter permissões do usuário
  static getUserPermissions(userRole: string): any[] {
    const { ROLE_PERMISSIONS } = require('../types');
    return ROLE_PERMISSIONS[userRole as keyof typeof ROLE_PERMISSIONS] || [];
  }

  // Validar força da senha
  static validatePasswordStrength(password: string): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (password.length < 6) {
      errors.push('Password must be at least 6 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter');
    }

    if (!/\d/.test(password)) {
      errors.push('Password must contain at least one number');
    }

    return {
      isValid: errors.length === 0,
      errors,
    };
  }

  // Gerar senha temporária
  static generateTemporaryPassword(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let password = '';
    
    for (let i = 0; i < 8; i++) {
      password += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    
    return password;
  }
}
