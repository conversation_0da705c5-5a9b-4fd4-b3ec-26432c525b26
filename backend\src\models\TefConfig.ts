import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  TefConfig, 
  CreateTefConfigRequest, 
  UpdateTefConfigRequest,
  TefValidation,
  PinPadStatus
} from '../types/tef';

export class TefConfigModel {
  // Buscar configuração TEF por ID
  static async findById(id: number): Promise<TefConfig | null> {
    const config = await executeQuerySingle<TefConfig>(
      'SELECT * FROM tef_configs WHERE id = ?',
      [id]
    );
    return config || null;
  }

  // Buscar configuração ativa
  static async findActive(): Promise<TefConfig | null> {
    const config = await executeQuerySingle<TefConfig>(
      'SELECT * FROM tef_configs WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1'
    );
    return config || null;
  }

  // Buscar configuração por provedor
  static async findByProvider(provider: string): Promise<TefConfig | null> {
    const config = await executeQuerySingle<TefConfig>(
      'SELECT * FROM tef_configs WHERE provider = ? AND is_active = 1',
      [provider]
    );
    return config || null;
  }

  // Listar todas as configurações
  static async findAll(activeOnly: boolean = true): Promise<TefConfig[]> {
    let query = 'SELECT * FROM tef_configs';
    const params: any[] = [];

    if (activeOnly) {
      query += ' WHERE is_active = 1';
    }

    query += ' ORDER BY created_at DESC';

    return await executeQuery<TefConfig>(query, params);
  }

  // Criar configuração TEF
  static async create(configData: CreateTefConfigRequest): Promise<TefConfig> {
    // Desativar configurações existentes do mesmo provedor
    await executeUpdate(
      'UPDATE tef_configs SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE provider = ?',
      [configData.provider]
    );

    // Inserir nova configuração
    const result = await executeUpdate(
      `INSERT INTO tef_configs (
        provider, provider_name, terminal_id, merchant_id, api_key, 
        api_secret, endpoint_url, pinpad_port, pinpad_model, 
        timeout_seconds, auto_confirm, print_customer_receipt, 
        print_merchant_receipt, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [
        configData.provider,
        configData.provider_name,
        configData.terminal_id,
        configData.merchant_id,
        configData.api_key || null,
        configData.api_secret || null,
        configData.endpoint_url || null,
        configData.pinpad_port || null,
        configData.pinpad_model || null,
        configData.timeout_seconds || 30,
        configData.auto_confirm || false,
        configData.print_customer_receipt || true,
        configData.print_merchant_receipt || true,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create TEF config');
    }

    // Retornar configuração criada
    const newConfig = await this.findById(result.lastID);
    if (!newConfig) {
      throw new Error('Failed to retrieve created TEF config');
    }

    return newConfig;
  }

  // Atualizar configuração TEF
  static async update(id: number, configData: UpdateTefConfigRequest): Promise<TefConfig> {
    const config = await this.findById(id);
    if (!config) {
      throw new Error('TEF config not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = [
      'provider_name', 'terminal_id', 'merchant_id', 'api_key', 'api_secret',
      'endpoint_url', 'pinpad_port', 'pinpad_model', 'timeout_seconds',
      'auto_confirm', 'print_customer_receipt', 'print_merchant_receipt', 'is_active'
    ];

    updatableFields.forEach(field => {
      if (configData[field as keyof UpdateTefConfigRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = configData[field as keyof UpdateTefConfigRequest];
        
        // Converter boolean para integer
        if (field === 'auto_confirm' || field === 'print_customer_receipt' || 
            field === 'print_merchant_receipt' || field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return config; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE tef_configs SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Se esta configuração foi ativada, desativar as outras do mesmo provedor
    if (configData.is_active) {
      await executeUpdate(
        'UPDATE tef_configs SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE provider = ? AND id != ?',
        [config.provider, id]
      );
    }

    // Retornar configuração atualizada
    const updatedConfig = await this.findById(id);
    if (!updatedConfig) {
      throw new Error('Failed to retrieve updated TEF config');
    }

    return updatedConfig;
  }

  // Deletar configuração (soft delete)
  static async delete(id: number): Promise<void> {
    const config = await this.findById(id);
    if (!config) {
      throw new Error('TEF config not found');
    }

    // Verificar se há transações usando esta configuração
    const transactionsCount = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM tef_transactions WHERE created_at >= ? AND created_at <= ?',
      [config.created_at, config.updated_at]
    );

    if (transactionsCount && transactionsCount.count > 0) {
      throw new Error('Cannot delete config with associated transactions');
    }

    await executeUpdate(
      'UPDATE tef_configs SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Validar configuração TEF
  static async validate(id?: number): Promise<TefValidation> {
    const config = id ? await this.findById(id) : await this.findActive();
    
    if (!config) {
      return {
        valid: false,
        errors: ['No TEF configuration found'],
        warnings: [],
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // Validações obrigatórias
    if (!config.provider_name) {
      errors.push('Provider name is required');
    }

    if (!config.terminal_id) {
      errors.push('Terminal ID is required');
    }

    if (!config.merchant_id) {
      errors.push('Merchant ID is required');
    }

    // Validações específicas por provedor
    if (config.provider !== 'simulator') {
      if (!config.api_key) {
        warnings.push('API key not configured');
      }

      if (!config.endpoint_url) {
        warnings.push('Endpoint URL not configured');
      }
    }

    // Validações de PinPad
    let pinpadInfo;
    if (config.pinpad_port) {
      try {
        // Simular validação de PinPad
        pinpadInfo = {
          connected: true,
          model: config.pinpad_model || 'Unknown',
          version: '1.0.0',
          status: 'ready' as PinPadStatus,
        };
      } catch (error) {
        errors.push('PinPad connection failed');
        pinpadInfo = {
          connected: false,
          model: 'Unknown',
          version: 'Unknown',
          status: 'offline' as PinPadStatus,
        };
      }
    } else {
      warnings.push('PinPad port not configured');
    }

    // Status do provedor (simulado)
    const providerInfo = {
      online: true,
      response_time: 1.5,
      last_check: new Date().toISOString(),
    };

    // Informações de limite (simulado)
    const limitsInfo = {
      within_limits: true,
      daily_used: 15000,
      daily_limit: 50000,
    };

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      pinpad_info: pinpadInfo,
      provider_info: providerInfo,
      limits_info: limitsInfo,
    };
  }

  // Testar conexão com provedor
  static async testProviderConnection(id?: number): Promise<{
    success: boolean;
    response_time: number;
    status_code?: number;
    message?: string;
  }> {
    const config = id ? await this.findById(id) : await this.findActive();
    
    if (!config) {
      return {
        success: false,
        response_time: 0,
        message: 'No TEF configuration found',
      };
    }

    try {
      // Simular teste de conexão
      const startTime = Date.now();
      
      if (config.provider === 'simulator') {
        // Simulador sempre responde rapidamente
        await new Promise(resolve => setTimeout(resolve, 500));
      } else {
        // Simular chamada real para o provedor
        await new Promise(resolve => setTimeout(resolve, 1500));
      }
      
      const responseTime = (Date.now() - startTime) / 1000;

      return {
        success: true,
        response_time: responseTime,
        status_code: 200,
        message: 'Connection successful',
      };
    } catch (error) {
      return {
        success: false,
        response_time: 0,
        message: error instanceof Error ? error.message : 'Connection failed',
      };
    }
  }

  // Testar conexão com PinPad
  static async testPinPadConnection(id?: number): Promise<{
    success: boolean;
    status: PinPadStatus;
    model?: string;
    version?: string;
    message?: string;
  }> {
    const config = id ? await this.findById(id) : await this.findActive();
    
    if (!config) {
      return {
        success: false,
        status: 'offline',
        message: 'No TEF configuration found',
      };
    }

    if (!config.pinpad_port) {
      return {
        success: false,
        status: 'offline',
        message: 'PinPad port not configured',
      };
    }

    try {
      // Simular teste de conexão com PinPad
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        success: true,
        status: 'ready',
        model: config.pinpad_model || 'Generic PinPad',
        version: '1.0.0',
        message: 'PinPad connected and ready',
      };
    } catch (error) {
      return {
        success: false,
        status: 'offline',
        message: error instanceof Error ? error.message : 'PinPad connection failed',
      };
    }
  }

  // Obter configuração para transação
  static async getForTransaction(): Promise<TefConfig> {
    const config = await this.findActive();
    if (!config) {
      throw new Error('No active TEF configuration found');
    }

    const validation = await this.validate(config.id);
    if (!validation.valid) {
      throw new Error(`Invalid TEF configuration: ${validation.errors.join(', ')}`);
    }

    return config;
  }

  // Verificar se configuração pode ser deletada
  static async canDelete(id: number): Promise<boolean> {
    const transactionsCount = await executeQuerySingle<{ count: number }>(
      `SELECT COUNT(*) as count 
       FROM tef_transactions tt
       JOIN tef_configs tc ON DATE(tt.created_at) >= DATE(tc.created_at) 
         AND DATE(tt.created_at) <= DATE(tc.updated_at)
       WHERE tc.id = ?`,
      [id]
    );

    return (transactionsCount?.count || 0) === 0;
  }

  // Backup da configuração
  static async backup(): Promise<{
    config: TefConfig;
    backup_date: string;
    file_path: string;
  }> {
    const config = await this.findActive();
    if (!config) {
      throw new Error('No active TEF configuration found');
    }

    // Simular backup
    const backupDate = new Date().toISOString();
    const filePath = `/backups/tef_config_${backupDate.slice(0, 10)}.json`;

    return {
      config,
      backup_date: backupDate,
      file_path: filePath,
    };
  }
}
