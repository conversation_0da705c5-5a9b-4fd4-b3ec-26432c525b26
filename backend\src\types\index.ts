// Tipos principais do sistema

export interface User {
  id: number;
  username: string;
  full_name: string;
  email?: string;
  phone?: string;
  role: UserRole;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type UserRole = 'manager' | 'cashier' | 'kitchen';

export interface CreateUserRequest {
  username: string;
  password: string;
  full_name: string;
  email?: string;
  phone?: string;
  role: UserRole;
}

export interface UpdateUserRequest {
  full_name?: string;
  email?: string;
  phone?: string;
  role?: UserRole;
  is_active?: boolean;
}

export interface LoginRequest {
  username: string;
  password: string;
}

export interface LoginResponse {
  user: Omit<User, 'password_hash'>;
  token: string;
  expires_in: string;
}

export interface AuthenticatedRequest extends Request {
  user?: User;
}

export interface JWTPayload {
  userId: number;
  username: string;
  role: UserRole;
  iat: number;
  exp: number;
}

// Permissões por role
export interface Permission {
  resource: string;
  actions: string[];
}

export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  manager: [
    { resource: 'users', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'products', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'categories', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'orders', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'cash', actions: ['create', 'read', 'update', 'delete'] },
    { resource: 'reports', actions: ['read'] },
    { resource: 'settings', actions: ['read', 'update'] },
    { resource: 'fiscal', actions: ['create', 'read', 'update'] },
  ],
  cashier: [
    { resource: 'products', actions: ['read'] },
    { resource: 'categories', actions: ['read'] },
    { resource: 'orders', actions: ['create', 'read', 'update'] },
    { resource: 'cash', actions: ['create', 'read', 'update'] },
    { resource: 'customers', actions: ['create', 'read', 'update'] },
    { resource: 'tables', actions: ['read', 'update'] },
    { resource: 'fiscal', actions: ['create', 'read'] },
  ],
  kitchen: [
    { resource: 'orders', actions: ['read', 'update'] },
    { resource: 'products', actions: ['read'] },
  ],
};

// Configurações do sistema
export interface SystemSettings {
  company_name: string;
  company_cnpj: string;
  company_ie: string;
  company_address: string;
  company_city: string;
  company_state: string;
  company_zip: string;
  company_phone: string;
  company_email: string;
  fiscal_environment: 'homologacao' | 'producao';
  fiscal_series_nfce: number;
  fiscal_series_nfe: number;
  delivery_fee_default: number;
  max_discount_percent: number;
  auto_print_kitchen: boolean;
  backup_enabled: boolean;
  backup_interval_hours: number;
  license_type: 'free' | 'premium';
  max_orders_per_month: number;
  openai_enabled: boolean;
  tef_enabled: boolean;
  kitchen_display_enabled: boolean;
}

// Resposta padrão da API
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  timestamp: string;
}

// Paginação
export interface PaginationParams {
  page: number;
  limit: number;
  sort?: string;
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Filtros
export interface UserFilters {
  role?: UserRole;
  is_active?: boolean;
  search?: string;
}

// Erros customizados
export class AppError extends Error {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends AppError {
  constructor(message: string) {
    super(message, 400);
  }
}

export class AuthenticationError extends AppError {
  constructor(message: string = 'Authentication failed') {
    super(message, 401);
  }
}

export class AuthorizationError extends AppError {
  constructor(message: string = 'Access denied') {
    super(message, 403);
  }
}

export class NotFoundError extends AppError {
  constructor(message: string = 'Resource not found') {
    super(message, 404);
  }
}

// Re-exportar tipos de produtos
export * from './products';

