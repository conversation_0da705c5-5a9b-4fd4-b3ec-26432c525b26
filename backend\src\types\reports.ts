// Tipos para Relatórios e Analytics

export interface ReportFilter {
  date_from: string;
  date_to: string;
  user_id?: number;
  product_id?: number;
  category_id?: number;
  cash_register_id?: number;
  payment_method?: string;
  order_status?: string;
}

export interface SalesReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_orders: number;
    total_items: number;
    total_amount: number;
    average_ticket: number;
    total_discount: number;
    total_tax: number;
    net_amount: number;
  };
  by_period: Array<{
    date: string;
    orders: number;
    amount: number;
    items: number;
  }>;
  by_hour: Array<{
    hour: number;
    orders: number;
    amount: number;
  }>;
  by_payment_method: Array<{
    method: string;
    orders: number;
    amount: number;
    percentage: number;
  }>;
  by_user: Array<{
    user_id: number;
    user_name: string;
    orders: number;
    amount: number;
    percentage: number;
  }>;
  top_products: Array<{
    product_id: number;
    product_name: string;
    quantity: number;
    amount: number;
    percentage: number;
  }>;
}

export interface ProductReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_products: number;
    products_sold: number;
    total_quantity: number;
    total_amount: number;
    average_price: number;
  };
  by_product: Array<{
    product_id: number;
    product_name: string;
    category_name: string;
    quantity_sold: number;
    amount: number;
    cost: number;
    profit: number;
    margin: number;
    stock_current: number;
    stock_min: number;
  }>;
  by_category: Array<{
    category_id: number;
    category_name: string;
    products_count: number;
    quantity_sold: number;
    amount: number;
    percentage: number;
  }>;
  low_stock: Array<{
    product_id: number;
    product_name: string;
    stock_current: number;
    stock_min: number;
    status: 'critical' | 'low' | 'warning';
  }>;
}

export interface FinancialReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    gross_revenue: number;
    net_revenue: number;
    total_costs: number;
    total_taxes: number;
    total_discounts: number;
    profit: number;
    margin: number;
  };
  cash_flow: Array<{
    date: string;
    opening_balance: number;
    total_sales: number;
    total_expenses: number;
    closing_balance: number;
  }>;
  by_payment_method: Array<{
    method: string;
    amount: number;
    percentage: number;
    fees: number;
    net_amount: number;
  }>;
  expenses: Array<{
    category: string;
    amount: number;
    percentage: number;
  }>;
  taxes: Array<{
    type: string;
    amount: number;
    percentage: number;
  }>;
}

export interface UserReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_users: number;
    active_users: number;
    total_sessions: number;
    average_session_time: number;
  };
  by_user: Array<{
    user_id: number;
    user_name: string;
    role: string;
    orders_count: number;
    sales_amount: number;
    sessions_count: number;
    total_time: number;
    average_ticket: number;
    performance_score: number;
  }>;
  by_role: Array<{
    role: string;
    users_count: number;
    orders_count: number;
    sales_amount: number;
    percentage: number;
  }>;
  productivity: Array<{
    user_id: number;
    user_name: string;
    orders_per_hour: number;
    items_per_order: number;
    efficiency_score: number;
  }>;
}

export interface KitchenReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_orders: number;
    completed_orders: number;
    cancelled_orders: number;
    average_prep_time: number;
    on_time_percentage: number;
  };
  by_status: Array<{
    status: string;
    count: number;
    percentage: number;
    average_time: number;
  }>;
  by_hour: Array<{
    hour: number;
    orders: number;
    average_prep_time: number;
    on_time_count: number;
  }>;
  by_product: Array<{
    product_id: number;
    product_name: string;
    orders_count: number;
    average_prep_time: number;
    complexity_score: number;
  }>;
  performance: Array<{
    date: string;
    orders: number;
    average_time: number;
    on_time_percentage: number;
    efficiency_score: number;
  }>;
}

export interface CustomerReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_customers: number;
    new_customers: number;
    returning_customers: number;
    average_frequency: number;
    customer_lifetime_value: number;
  };
  by_frequency: Array<{
    frequency_range: string;
    customers_count: number;
    percentage: number;
    total_amount: number;
  }>;
  by_value: Array<{
    value_range: string;
    customers_count: number;
    percentage: number;
    total_amount: number;
  }>;
  top_customers: Array<{
    customer_id: number;
    customer_name: string;
    orders_count: number;
    total_amount: number;
    last_order: string;
    loyalty_score: number;
  }>;
}

// Analytics com IA
export interface AnalyticsInsight {
  id: string;
  type: InsightType;
  title: string;
  description: string;
  impact: 'high' | 'medium' | 'low';
  category: InsightCategory;
  data: any;
  recommendations: string[];
  confidence: number;
  created_at: string;
}

export type InsightType = 
  | 'trend_analysis'
  | 'anomaly_detection'
  | 'performance_optimization'
  | 'revenue_opportunity'
  | 'cost_reduction'
  | 'customer_behavior'
  | 'inventory_optimization'
  | 'staff_productivity';

export type InsightCategory = 
  | 'sales'
  | 'products'
  | 'customers'
  | 'operations'
  | 'finance'
  | 'staff';

export interface AnalyticsRequest {
  period: {
    start: string;
    end: string;
  };
  focus_areas: InsightCategory[];
  include_predictions: boolean;
  detail_level: 'summary' | 'detailed' | 'comprehensive';
}

export interface AnalyticsResponse {
  period: {
    start: string;
    end: string;
  };
  insights: AnalyticsInsight[];
  summary: {
    total_insights: number;
    high_impact_count: number;
    categories_analyzed: InsightCategory[];
    confidence_average: number;
  };
  predictions: Array<{
    metric: string;
    current_value: number;
    predicted_value: number;
    trend: 'up' | 'down' | 'stable';
    confidence: number;
  }>;
  recommendations: Array<{
    priority: 'high' | 'medium' | 'low';
    category: InsightCategory;
    action: string;
    expected_impact: string;
    implementation_effort: 'low' | 'medium' | 'high';
  }>;
}

// Configurações de relatórios
export interface ReportConfig {
  id: number;
  name: string;
  type: ReportType;
  description: string;
  filters: ReportFilter;
  schedule: ReportSchedule;
  recipients: string[];
  format: ReportFormat;
  is_active: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export type ReportType = 
  | 'sales'
  | 'products'
  | 'financial'
  | 'users'
  | 'kitchen'
  | 'customers'
  | 'analytics';

export type ReportSchedule = 
  | 'manual'
  | 'daily'
  | 'weekly'
  | 'monthly'
  | 'quarterly';

export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'json';

export interface CreateReportConfigRequest {
  name: string;
  type: ReportType;
  description?: string;
  filters: ReportFilter;
  schedule: ReportSchedule;
  recipients: string[];
  format: ReportFormat;
}

export interface UpdateReportConfigRequest {
  name?: string;
  description?: string;
  filters?: ReportFilter;
  schedule?: ReportSchedule;
  recipients?: string[];
  format?: ReportFormat;
  is_active?: boolean;
}

// Histórico de relatórios
export interface ReportHistory {
  id: number;
  config_id: number;
  config_name: string;
  type: ReportType;
  period: {
    start: string;
    end: string;
  };
  status: ReportStatus;
  file_path?: string;
  file_size?: number;
  generated_by: number;
  generated_at: string;
  error_message?: string;
}

export type ReportStatus = 'pending' | 'generating' | 'completed' | 'failed' | 'expired';

// Dashboard executivo
export interface ExecutiveDashboard {
  period: {
    start: string;
    end: string;
  };
  kpis: {
    revenue: {
      current: number;
      previous: number;
      growth: number;
      trend: 'up' | 'down' | 'stable';
    };
    orders: {
      current: number;
      previous: number;
      growth: number;
      trend: 'up' | 'down' | 'stable';
    };
    average_ticket: {
      current: number;
      previous: number;
      growth: number;
      trend: 'up' | 'down' | 'stable';
    };
    profit_margin: {
      current: number;
      previous: number;
      growth: number;
      trend: 'up' | 'down' | 'stable';
    };
  };
  charts: {
    revenue_trend: Array<{
      date: string;
      revenue: number;
      orders: number;
    }>;
    top_products: Array<{
      name: string;
      revenue: number;
      quantity: number;
    }>;
    payment_methods: Array<{
      method: string;
      amount: number;
      percentage: number;
    }>;
    hourly_performance: Array<{
      hour: number;
      orders: number;
      revenue: number;
    }>;
  };
  alerts: Array<{
    type: 'warning' | 'error' | 'info';
    message: string;
    priority: 'high' | 'medium' | 'low';
    category: string;
  }>;
  insights: AnalyticsInsight[];
}

// Comparações de período
export interface PeriodComparison {
  current_period: {
    start: string;
    end: string;
    data: any;
  };
  previous_period: {
    start: string;
    end: string;
    data: any;
  };
  comparison: {
    revenue_growth: number;
    orders_growth: number;
    items_growth: number;
    customers_growth: number;
    significant_changes: Array<{
      metric: string;
      change: number;
      impact: 'positive' | 'negative' | 'neutral';
      description: string;
    }>;
  };
}

// Exportação de dados
export interface ExportRequest {
  type: ReportType;
  format: ReportFormat;
  filters: ReportFilter;
  include_charts: boolean;
  include_raw_data: boolean;
}

export interface ExportResponse {
  file_path: string;
  file_name: string;
  file_size: number;
  download_url: string;
  expires_at: string;
}

// Métricas de performance
export interface PerformanceMetrics {
  period: {
    start: string;
    end: string;
  };
  operational: {
    orders_per_hour: number;
    average_prep_time: number;
    table_turnover: number;
    staff_efficiency: number;
  };
  financial: {
    revenue_per_seat: number;
    cost_percentage: number;
    profit_margin: number;
    cash_flow_ratio: number;
  };
  customer: {
    satisfaction_score: number;
    retention_rate: number;
    acquisition_cost: number;
    lifetime_value: number;
  };
  benchmarks: {
    industry_average: any;
    best_practices: any;
    improvement_areas: string[];
  };
}
