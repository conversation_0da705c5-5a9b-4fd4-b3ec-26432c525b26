import { Router } from 'express';
import { ReportsController } from '../controllers/ReportsController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para filtros de relatórios
const reportFilterValidation = [
  query('date_from')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for date_from'),
  query('date_to')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for date_to'),
  query('user_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('User ID must be a positive integer'),
  query('product_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  query('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Category ID must be a positive integer'),
  query('payment_method')
    .optional()
    .isIn(['cash', 'card', 'pix', 'voucher'])
    .withMessage('Invalid payment method'),
];

// Validações para analytics
const analyticsValidation = [
  body('date_from')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for date_from'),
  body('date_to')
    .optional()
    .isISO8601()
    .withMessage('Invalid date format for date_to'),
  body('focus_areas')
    .optional()
    .isArray()
    .withMessage('Focus areas must be an array'),
  body('focus_areas.*')
    .optional()
    .isIn(['sales', 'products', 'customers', 'operations', 'finance', 'staff'])
    .withMessage('Invalid focus area'),
  body('include_predictions')
    .optional()
    .isBoolean()
    .withMessage('Include predictions must be a boolean'),
  body('detail_level')
    .optional()
    .isIn(['summary', 'detailed', 'comprehensive'])
    .withMessage('Invalid detail level'),
];

// Validações para comparação de períodos
const periodComparisonValidation = [
  query('current_start')
    .notEmpty()
    .withMessage('Current start date is required')
    .isISO8601()
    .withMessage('Invalid date format for current_start'),
  query('current_end')
    .notEmpty()
    .withMessage('Current end date is required')
    .isISO8601()
    .withMessage('Invalid date format for current_end'),
  query('previous_start')
    .notEmpty()
    .withMessage('Previous start date is required')
    .isISO8601()
    .withMessage('Invalid date format for previous_start'),
  query('previous_end')
    .notEmpty()
    .withMessage('Previous end date is required')
    .isISO8601()
    .withMessage('Invalid date format for previous_end'),
];

// Validações para exportação
const exportValidation = [
  param('type')
    .isIn(['sales', 'products', 'financial', 'users', 'kitchen'])
    .withMessage('Invalid report type'),
  query('format')
    .optional()
    .isIn(['pdf', 'excel', 'csv', 'json'])
    .withMessage('Invalid export format'),
];

// ===== ROTAS DE RELATÓRIOS OPERACIONAIS =====

/**
 * @route GET /api/reports/sales
 * @desc Relatório de vendas
 * @access Private (read reports)
 */
router.get('/sales',
  authenticate,
  authorize('reports', 'read'),
  reportFilterValidation,
  validateRequest,
  ReportsController.getSalesReport
);

/**
 * @route GET /api/reports/products
 * @desc Relatório de produtos
 * @access Private (read reports)
 */
router.get('/products',
  authenticate,
  authorize('reports', 'read'),
  reportFilterValidation,
  validateRequest,
  ReportsController.getProductReport
);

/**
 * @route GET /api/reports/financial
 * @desc Relatório financeiro
 * @access Private (read reports)
 */
router.get('/financial',
  authenticate,
  authorize('reports', 'read'),
  reportFilterValidation,
  validateRequest,
  ReportsController.getFinancialReport
);

/**
 * @route GET /api/reports/executive-dashboard
 * @desc Dashboard executivo
 * @access Private (read reports)
 */
router.get('/executive-dashboard',
  authenticate,
  authorize('reports', 'read'),
  reportFilterValidation,
  validateRequest,
  ReportsController.getExecutiveDashboard
);

/**
 * @route GET /api/reports/performance-metrics
 * @desc Métricas de performance
 * @access Private (read reports)
 */
router.get('/performance-metrics',
  authenticate,
  authorize('reports', 'read'),
  reportFilterValidation,
  validateRequest,
  ReportsController.getPerformanceMetrics
);

// ===== ROTAS DE ANALYTICS COM IA =====

/**
 * @route POST /api/reports/analytics/insights
 * @desc Gerar insights com IA
 * @access Private (read reports)
 */
router.post('/analytics/insights',
  authenticate,
  authorize('reports', 'read'),
  analyticsValidation,
  validateRequest,
  logUserAction('generate_analytics_insights'),
  ReportsController.generateInsights
);

/**
 * @route GET /api/reports/analytics/quick-insights
 * @desc Insights rápidos para dashboard
 * @access Private (read reports)
 */
router.get('/analytics/quick-insights',
  authenticate,
  authorize('reports', 'read'),
  ReportsController.getQuickInsights
);

// ===== ROTAS DE COMPARAÇÕES E ANÁLISES =====

/**
 * @route GET /api/reports/compare-periods
 * @desc Comparar períodos
 * @access Private (read reports)
 */
router.get('/compare-periods',
  authenticate,
  authorize('reports', 'read'),
  periodComparisonValidation,
  validateRequest,
  ReportsController.comparePeriods
);

// ===== ROTAS DE EXPORTAÇÃO =====

/**
 * @route GET /api/reports/export/:type
 * @desc Exportar relatório
 * @access Private (read reports)
 */
router.get('/export/:type',
  authenticate,
  authorize('reports', 'read'),
  exportValidation,
  validateRequest,
  logUserAction('export_report'),
  ReportsController.exportReport
);

export default router;
