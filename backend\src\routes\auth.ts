import { Router } from 'express';
import { AuthController } from '../controllers/AuthController';
import { authenticate, logUserAction } from '../middleware/auth';
import { body, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para login
const loginValidation = [
  body('username')
    .notEmpty()
    .withMessage('Username is required')
    .isLength({ min: 3, max: 50 })
    .withMessage('Username must be between 3 and 50 characters'),
  body('password')
    .notEmpty()
    .withMessage('Password is required')
    .isLength({ min: 6 })
    .withMessage('Password must be at least 6 characters'),
];

// Validações para mudança de senha
const changePasswordValidation = [
  body('currentPassword')
    .notEmpty()
    .withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 6 })
    .withMessage('New password must be at least 6 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('New password must contain at least one lowercase letter, one uppercase letter, and one number'),
];

// Validações para atualização de perfil
const updateProfileValidation = [
  body('full_name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Full name must be between 2 and 100 characters'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Invalid email format'),
  body('phone')
    .optional()
    .matches(/^\(\d{2}\)\s\d{4,5}-\d{4}$/)
    .withMessage('Phone must be in format (XX) XXXXX-XXXX'),
];

// Validação para refresh token
const refreshTokenValidation = [
  body('token')
    .notEmpty()
    .withMessage('Token is required'),
];

/**
 * @route POST /api/auth/login
 * @desc Login do usuário
 * @access Public
 */
router.post('/login', 
  loginValidation,
  validateRequest,
  AuthController.login
);

/**
 * @route POST /api/auth/logout
 * @desc Logout do usuário
 * @access Private
 */
router.post('/logout',
  authenticate,
  logUserAction('logout'),
  AuthController.logout
);

/**
 * @route POST /api/auth/refresh
 * @desc Renovar token de acesso
 * @access Public
 */
router.post('/refresh',
  refreshTokenValidation,
  validateRequest,
  AuthController.refreshToken
);

/**
 * @route GET /api/auth/profile
 * @desc Obter perfil do usuário atual
 * @access Private
 */
router.get('/profile',
  authenticate,
  AuthController.getProfile
);

/**
 * @route PUT /api/auth/profile
 * @desc Atualizar perfil do usuário atual
 * @access Private
 */
router.put('/profile',
  authenticate,
  updateProfileValidation,
  validateRequest,
  logUserAction('update_profile'),
  AuthController.updateProfile
);

/**
 * @route PUT /api/auth/change-password
 * @desc Alterar senha do usuário atual
 * @access Private
 */
router.put('/change-password',
  authenticate,
  changePasswordValidation,
  validateRequest,
  logUserAction('change_password'),
  AuthController.changePassword
);

/**
 * @route GET /api/auth/permissions
 * @desc Obter permissões do usuário atual
 * @access Private
 */
router.get('/permissions',
  authenticate,
  AuthController.getPermissions
);

/**
 * @route GET /api/auth/check
 * @desc Verificar se token é válido
 * @access Private
 */
router.get('/check',
  authenticate,
  (req, res) => {
    res.status(200).json({
      success: true,
      data: {
        authenticated: true,
        user: req.user,
      },
      timestamp: new Date().toISOString(),
    });
  }
);

export default router;
