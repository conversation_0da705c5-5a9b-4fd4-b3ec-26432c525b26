const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = 3001;

// Dados mock para teste
const mockData = {
  users: [
    { id: 1, username: 'admin', password: 'password', role: 'manager', name: '<PERSON><PERSON><PERSON><PERSON>' },
    { id: 2, username: 'ca<PERSON><PERSON>', password: 'password', role: 'cashier', name: '<PERSON><PERSON> de Caixa' },
    { id: 3, username: 'co<PERSON><PERSON>', password: 'password', role: 'kitchen', name: '<PERSON><PERSON><PERSON>' }
  ],
  products: [
    { id: 1, name: 'Hambúrguer Clássico', price: 25.90, category: 'Hambúrgueres', active: true },
    { id: 2, name: 'Pizza Margherita', price: 35.00, category: 'Pizzas', active: true },
    { id: 3, name: 'Refrigerante Lata', price: 5.50, category: 'Bebidas', active: true },
    { id: 4, name: 'Batata Frita', price: 12.00, category: 'Acompanhamentos', active: true },
    { id: 5, name: 'Sorvete', price: 8.50, category: 'Sobremesas', active: true }
  ],
  orders: []
};

// Função para responder JSON
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, {
    'Content-Type': 'application/json',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type, Authorization'
  });
  res.end(JSON.stringify(data, null, 2));
}

// Função para ler body da requisição
function readBody(req, callback) {
  let body = '';
  req.on('data', chunk => {
    body += chunk.toString();
  });
  req.on('end', () => {
    try {
      const data = body ? JSON.parse(body) : {};
      callback(null, data);
    } catch (error) {
      callback(error, null);
    }
  });
}

// Criar servidor
const server = http.createServer((req, res) => {
  const parsedUrl = url.parse(req.url, true);
  const path = parsedUrl.pathname;
  const method = req.method;

  console.log(`${new Date().toISOString()} - ${method} ${path}`);

  // CORS preflight
  if (method === 'OPTIONS') {
    res.writeHead(200, {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    });
    res.end();
    return;
  }

  // Rotas
  if (path === '/' && method === 'GET') {
    sendJSON(res, {
      success: true,
      message: 'Sistema PDV Adib - API Básica funcionando!',
      version: '1.0.0',
      endpoints: {
        health: 'GET /api/health',
        status: 'GET /api/status',
        login: 'POST /api/auth/login',
        products: 'GET /api/products',
        orders: 'GET /api/orders',
        createOrder: 'POST /api/orders'
      },
      credentials: {
        admin: 'admin / password',
        cashier: 'caixa / password',
        kitchen: 'cozinha / password'
      }
    });
  }
  
  else if (path === '/api/health' && method === 'GET') {
    sendJSON(res, {
      success: true,
      message: 'Sistema PDV Adib funcionando!',
      timestamp: new Date().toISOString(),
      version: '1.0.0',
      status: 'online'
    });
  }
  
  else if (path === '/api/status' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: {
        system: 'PDV Adib',
        version: '1.0.0',
        environment: 'development',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
      }
    });
  }
  
  else if (path === '/api/auth/login' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }
      
      const { username, password } = data;
      const user = mockData.users.find(u => u.username === username && u.password === password);
      
      if (user) {
        sendJSON(res, {
          success: true,
          data: {
            token: 'demo-token-' + Date.now(),
            user: {
              id: user.id,
              username: user.username,
              name: user.name,
              role: user.role
            }
          },
          message: 'Login realizado com sucesso!'
        });
      } else {
        sendJSON(res, {
          success: false,
          error: 'Credenciais inválidas'
        }, 401);
      }
    });
  }
  
  else if (path === '/api/products' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.products.filter(p => p.active),
      total: mockData.products.filter(p => p.active).length
    });
  }
  
  else if (path === '/api/orders' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: mockData.orders,
      total: mockData.orders.length
    });
  }
  
  else if (path === '/api/orders' && method === 'POST') {
    readBody(req, (err, data) => {
      if (err) {
        return sendJSON(res, { success: false, error: 'Dados inválidos' }, 400);
      }
      
      const { items, total } = data;
      
      if (!items || !Array.isArray(items) || items.length === 0) {
        return sendJSON(res, { success: false, error: 'Itens são obrigatórios' }, 400);
      }
      
      const order = {
        id: mockData.orders.length + 1,
        number: 'PED' + String(mockData.orders.length + 1).padStart(3, '0'),
        status: 'pending',
        items,
        total: total || items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
        created_at: new Date().toISOString()
      };
      
      mockData.orders.push(order);
      
      sendJSON(res, {
        success: true,
        data: order,
        message: 'Pedido criado com sucesso!'
      });
    });
  }
  
  else if (path === '/api/sync/metrics' && method === 'GET') {
    sendJSON(res, {
      success: true,
      data: {
        lastSync: new Date().toISOString(),
        pendingItems: 0,
        syncStatus: 'up_to_date',
        conflicts: 0,
        totalSynced: 150
      }
    });
  }
  
  else if (path === '/api/reports/sales-summary' && method === 'GET') {
    const totalSales = mockData.orders.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = mockData.orders.length;
    const averageTicket = totalOrders > 0 ? totalSales / totalOrders : 0;
    
    sendJSON(res, {
      success: true,
      data: {
        totalSales,
        totalOrders,
        averageTicket,
        topProducts: mockData.products.slice(0, 3),
        period: 'today'
      }
    });
  }
  
  else {
    sendJSON(res, {
      success: false,
      error: 'Endpoint não encontrado',
      message: `${method} ${path} não existe`
    }, 404);
  }
});

// Iniciar servidor
server.listen(PORT, () => {
  console.log('\n🚀 Sistema PDV Adib - Servidor Básico iniciado!');
  console.log(`📡 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`🔗 Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Status: http://localhost:${PORT}/api/status`);
  console.log('\n👤 Credenciais de teste:');
  console.log('   Admin: admin / password');
  console.log('   Caixa: caixa / password');
  console.log('   Cozinha: cozinha / password');
  console.log('\n📚 Endpoints disponíveis:');
  console.log('   GET  / - Informações da API');
  console.log('   GET  /api/health - Health check');
  console.log('   GET  /api/status - Status do sistema');
  console.log('   POST /api/auth/login - Login');
  console.log('   GET  /api/products - Lista de produtos');
  console.log('   GET  /api/orders - Lista de pedidos');
  console.log('   POST /api/orders - Criar pedido');
  console.log('   GET  /api/sync/metrics - Métricas de sync');
  console.log('   GET  /api/reports/sales-summary - Relatório de vendas');
  console.log('\n✨ Sistema pronto para uso!');
  console.log('\n🌐 Teste no navegador: http://localhost:' + PORT);
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🔄 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('\n🔄 Encerrando servidor...');
  server.close(() => {
    console.log('✅ Servidor encerrado');
    process.exit(0);
  });
});
