#!/usr/bin/env node

/**
 * Script para testar as APIs de Sincronização Offline-First
 * Testa sincronização, dados offline e resolução de conflitos
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar sincronização básica
async function testBasicSync() {
  log('\n🔄 Testando Sincronização Básica...', 'blue');

  try {
    // Iniciar sincronização
    log('Iniciando sincronização...', 'cyan');
    const syncRequest = {
      entity_types: ['orders', 'products', 'payments'],
      direction: 'bidirectional',
      force_sync: false,
      resolve_conflicts: false
    };

    const syncResponse = await axios.post(`${API_BASE_URL}/sync/start`, syncRequest, {
      headers: getHeaders()
    });
    
    const queueId = syncResponse.data.data.queue_id;
    log(`✅ Sincronização iniciada com queue ID: ${queueId}`, 'green');
    log(`   - Total de operações: ${syncResponse.data.data.total_operations}`, 'cyan');
    log(`   - Duração estimada: ${syncResponse.data.data.estimated_duration}s`, 'cyan');

    // Aguardar um pouco e verificar status
    await new Promise(resolve => setTimeout(resolve, 2000));

    log('Verificando status da sincronização...', 'cyan');
    const statusResponse = await axios.get(`${API_BASE_URL}/sync/status/${queueId}`, {
      headers: getHeaders()
    });
    
    const queueStatus = statusResponse.data.data;
    log(`✅ Status da sincronização:`, 'green');
    log(`   - Status: ${queueStatus.status}`, 'cyan');
    log(`   - Operações concluídas: ${queueStatus.completed_operations}/${queueStatus.total_operations}`, 'cyan');
    log(`   - Operações falharam: ${queueStatus.failed_operations}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de sincronização: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar métricas de sincronização
async function testSyncMetrics() {
  log('\n📊 Testando Métricas de Sincronização...', 'blue');

  try {
    log('Obtendo métricas de sincronização...', 'cyan');
    const metricsResponse = await axios.get(`${API_BASE_URL}/sync/metrics`, {
      headers: getHeaders()
    });
    
    const metrics = metricsResponse.data.data;
    log(`✅ Métricas obtidas:`, 'green');
    log(`   - Total de entidades: ${metrics.total_entities}`, 'cyan');
    log(`   - Entidades sincronizadas: ${metrics.synced_entities}`, 'cyan');
    log(`   - Entidades pendentes: ${metrics.pending_entities}`, 'cyan');
    log(`   - Conflitos: ${metrics.conflict_entities}`, 'cyan');
    log(`   - Taxa de sucesso: ${metrics.sync_success_rate}%`, 'cyan');
    log(`   - Status da rede: ${metrics.network_status}`, 'cyan');
    log(`   - Uso de armazenamento: ${metrics.storage_usage.total_size} MB`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de métricas: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar dados offline
async function testOfflineData() {
  log('\n💾 Testando Dados Offline...', 'blue');

  try {
    // Salvar dados offline
    log('Salvando dados offline...', 'cyan');
    const offlineData = {
      entity_type: 'orders',
      entity_id: 999,
      data: {
        id: 999,
        total_amount: 150.00,
        status: 'pending',
        created_offline: true,
        timestamp: new Date().toISOString()
      }
    };

    await axios.post(`${API_BASE_URL}/sync/offline`, offlineData, {
      headers: getHeaders()
    });
    log('✅ Dados offline salvos com sucesso', 'green');

    // Buscar dados offline
    log('Buscando dados offline...', 'cyan');
    const getOfflineResponse = await axios.get(`${API_BASE_URL}/sync/offline/orders/999`, {
      headers: getHeaders()
    });
    
    const retrievedData = getOfflineResponse.data.data;
    log(`✅ Dados offline recuperados: ${retrievedData.length} registro(s)`, 'green');

    // Buscar todos os dados de um tipo
    log('Buscando todos os dados offline de orders...', 'cyan');
    const getAllOfflineResponse = await axios.get(`${API_BASE_URL}/sync/offline/orders`, {
      headers: getHeaders()
    });
    
    const allData = getAllOfflineResponse.data.data;
    log(`✅ Total de dados offline de orders: ${allData.length}`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de dados offline: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar conectividade
async function testConnectivity() {
  log('\n🌐 Testando Status de Conectividade...', 'blue');

  try {
    log('Verificando status de conectividade...', 'cyan');
    const connectivityResponse = await axios.get(`${API_BASE_URL}/sync/connectivity`, {
      headers: getHeaders()
    });
    
    const connectivity = connectivityResponse.data.data;
    log(`✅ Status de conectividade:`, 'green');
    log(`   - Online: ${connectivity.is_online}`, 'cyan');
    log(`   - Status da rede: ${connectivity.network_status}`, 'cyan');
    log(`   - Sincronização disponível: ${connectivity.sync_available}`, 'cyan');
    log(`   - Última verificação: ${new Date(connectivity.last_check).toLocaleString('pt-BR')}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de conectividade: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar sincronização forçada
async function testForceSync() {
  log('\n⚡ Testando Sincronização Forçada...', 'blue');

  try {
    log('Forçando sincronização de entidade específica...', 'cyan');
    await axios.post(`${API_BASE_URL}/sync/force/orders/1`, {}, {
      headers: getHeaders()
    });
    log('✅ Sincronização forçada executada com sucesso', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro na sincronização forçada: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar resolução de conflitos
async function testConflictResolution() {
  log('\n⚔️ Testando Resolução de Conflitos...', 'blue');

  try {
    // Simular resolução de conflito
    log('Simulando resolução de conflito...', 'cyan');
    const conflictId = 'conflict_123';
    const resolutionData = {
      resolution: 'server_wins'
    };

    try {
      await axios.post(`${API_BASE_URL}/sync/conflicts/${conflictId}/resolve`, resolutionData, {
        headers: getHeaders()
      });
      log('✅ Conflito resolvido com sucesso', 'green');
    } catch (error) {
      if (error.response?.status === 404) {
        log('✅ Teste de conflito funcionando (conflito não encontrado, como esperado)', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro no teste de resolução de conflitos: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar limpeza de dados
async function testDataCleanup() {
  log('\n🧹 Testando Limpeza de Dados...', 'blue');

  try {
    log('Executando limpeza de dados de sincronização...', 'cyan');
    const cleanupResponse = await axios.delete(`${API_BASE_URL}/sync/cleanup?days=30`, {
      headers: getHeaders()
    });
    
    const cleanupResult = cleanupResponse.data.data;
    log(`✅ Limpeza concluída:`, 'green');
    log(`   - Registros limpos: ${cleanupResult.cleaned_records}`, 'cyan');
    log(`   - Dias de retenção: ${cleanupResult.days_old}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro na limpeza de dados: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar iniciar sync com entity_types inválidos
    log('Testando entity_types inválidos...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/sync/start`, {
        entity_types: ['invalid_entity'],
        direction: 'up'
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de entity_types funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar salvar dados offline sem campos obrigatórios
    log('Testando dados offline sem campos obrigatórios...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/sync/offline`, {
        entity_type: 'orders'
        // Faltando entity_id e data
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de dados offline funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar resolver conflito sem resolution
    log('Testando resolução de conflito sem resolution...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/sync/conflicts/test/resolve`, {
        // Faltando resolution
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de resolução de conflito funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs de Sincronização Offline-First - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      basic_sync: await testBasicSync(),
      sync_metrics: await testSyncMetrics(),
      offline_data: await testOfflineData(),
      connectivity: await testConnectivity(),
      force_sync: await testForceSync(),
      conflict_resolution: await testConflictResolution(),
      data_cleanup: await testDataCleanup(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase().replace(/_/g, ' ')}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! Sistema Offline-First funcionando corretamente.', 'green');
      log('\n🔄 Sistema pronto para funcionar offline com sincronização automática!', 'cyan');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
