import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
} from '@mui/material';
import {
  CreditCard,
  CheckCircle,
  Error,
  Warning,
  Cancel,
  Print,
  Refresh,
  Settings,
  Payment,
  CloudDone,
  CloudOff,
  DeviceHub,
  Assessment,
  TrendingUp,
  TrendingDown,
} from '@mui/icons-material';
import { useAuthStore } from '../services/auth';

interface TefTransaction {
  id: number;
  type: 'sale' | 'cancellation' | 'administrative';
  operation: 'credit' | 'debit' | 'voucher' | 'cancel';
  amount: number;
  installments: number;
  card_type: 'credit' | 'debit' | 'voucher' | 'unknown';
  card_brand?: string;
  card_number_masked?: string;
  authorization_code?: string;
  nsu?: string;
  status: 'pending' | 'processing' | 'approved' | 'denied' | 'cancelled' | 'error';
  order_number?: string;
  user_name?: string;
  created_at: string;
  processed_at?: string;
}

interface TefMetrics {
  transactions_today: number;
  transactions_pending: number;
  transactions_approved: number;
  transactions_denied: number;
  total_amount_today: number;
  approval_rate: number;
  pinpad_status: 'ready' | 'processing' | 'offline';
  provider_status: 'online' | 'offline' | 'unstable';
  daily_limit_used: number;
  daily_limit_total: number;
  last_transaction_at?: string;
}

interface TefConfig {
  id: number;
  provider: string;
  provider_name: string;
  terminal_id: string;
  merchant_id: string;
  pinpad_model?: string;
  is_active: boolean;
}

const Tef: React.FC = () => {
  const [transactions, setTransactions] = useState<TefTransaction[]>([]);
  const [metrics, setMetrics] = useState<TefMetrics | null>(null);
  const [config, setConfig] = useState<TefConfig | null>(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedTransaction, setSelectedTransaction] = useState<TefTransaction | null>(null);
  const [detailsDialog, setDetailsDialog] = useState(false);
  const [cancelDialog, setCancelDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);
  const [processing, setProcessing] = useState(false);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular métricas
        const mockMetrics: TefMetrics = {
          transactions_today: 87,
          transactions_pending: 2,
          transactions_approved: 82,
          transactions_denied: 3,
          total_amount_today: 4250.80,
          approval_rate: 96.5,
          pinpad_status: 'ready',
          provider_status: 'online',
          daily_limit_used: 4250.80,
          daily_limit_total: 50000.00,
          last_transaction_at: '2024-12-19T16:45:00Z',
        };

        // Simular configuração
        const mockConfig: TefConfig = {
          id: 1,
          provider: 'cielo',
          provider_name: 'Cielo',
          terminal_id: 'T1234567',
          merchant_id: 'M9876543',
          pinpad_model: 'Ingenico iWL250',
          is_active: true,
        };

        // Simular transações TEF
        const mockTransactions: TefTransaction[] = [
          {
            id: 1,
            type: 'sale',
            operation: 'credit',
            amount: 89.90,
            installments: 3,
            card_type: 'credit',
            card_brand: 'Visa',
            card_number_masked: '****1234',
            authorization_code: 'ABC123',
            nsu: '000001',
            status: 'approved',
            order_number: '20241219001',
            user_name: 'João Silva',
            created_at: '2024-12-19T16:45:00Z',
            processed_at: '2024-12-19T16:45:15Z',
          },
          {
            id: 2,
            type: 'sale',
            operation: 'debit',
            amount: 45.50,
            installments: 1,
            card_type: 'debit',
            card_brand: 'Mastercard',
            card_number_masked: '****5678',
            authorization_code: 'DEF456',
            nsu: '000002',
            status: 'approved',
            order_number: '20241219002',
            user_name: 'Maria Santos',
            created_at: '2024-12-19T16:30:00Z',
            processed_at: '2024-12-19T16:30:08Z',
          },
          {
            id: 3,
            type: 'sale',
            operation: 'credit',
            amount: 125.00,
            installments: 1,
            card_type: 'credit',
            status: 'processing',
            user_name: 'Pedro Costa',
            created_at: '2024-12-19T16:50:00Z',
          },
          {
            id: 4,
            type: 'sale',
            operation: 'debit',
            amount: 67.80,
            installments: 1,
            card_type: 'debit',
            status: 'denied',
            order_number: '20241219004',
            user_name: 'Ana Lima',
            created_at: '2024-12-19T16:20:00Z',
            processed_at: '2024-12-19T16:20:12Z',
          },
        ];

        setMetrics(mockMetrics);
        setConfig(mockConfig);
        setTransactions(mockTransactions);
      } catch (error) {
        console.error('Error loading TEF data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Filtrar transações
  const filteredTransactions = transactions.filter(transaction => {
    if (statusFilter === 'all') return true;
    return transaction.status === statusFilter;
  });

  // Obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved': return 'success';
      case 'pending': return 'warning';
      case 'processing': return 'info';
      case 'denied': return 'error';
      case 'cancelled': return 'default';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  // Obter ícone do status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved': return <CheckCircle />;
      case 'pending': return <Warning />;
      case 'processing': return <Refresh />;
      case 'denied': return <Error />;
      case 'cancelled': return <Cancel />;
      case 'error': return <Error />;
      default: return <CreditCard />;
    }
  };

  // Obter nome da operação
  const getOperationName = (operation: string) => {
    switch (operation) {
      case 'credit': return 'Crédito';
      case 'debit': return 'Débito';
      case 'voucher': return 'Voucher';
      case 'cancel': return 'Cancelamento';
      default: return operation;
    }
  };

  // Formatar moeda
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  // Formatar data/hora
  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  // Simular nova transação
  const simulateTransaction = async (operation: 'credit' | 'debit', amount: number) => {
    setProcessing(true);
    
    // Simular processamento
    const newTransaction: TefTransaction = {
      id: Date.now(),
      type: 'sale',
      operation,
      amount,
      installments: operation === 'credit' ? 1 : 1,
      card_type: operation,
      status: 'processing',
      user_name: user?.full_name || 'Usuário',
      created_at: new Date().toISOString(),
    };

    setTransactions([newTransaction, ...transactions]);

    // Simular aprovação após 3 segundos
    setTimeout(() => {
      const approvedTransaction = {
        ...newTransaction,
        status: 'approved' as const,
        card_brand: operation === 'credit' ? 'Visa' : 'Mastercard',
        card_number_masked: '****1234',
        authorization_code: 'SIM123',
        nsu: `00000${Date.now().toString().slice(-3)}`,
        processed_at: new Date().toISOString(),
      };

      setTransactions(prev => prev.map(t => 
        t.id === newTransaction.id ? approvedTransaction : t
      ));
      setProcessing(false);
    }, 3000);
  };

  // Cancelar transação
  const cancelTransaction = async () => {
    if (!selectedTransaction || !cancelReason.trim()) {
      return;
    }

    // Simular cancelamento
    setTransactions(transactions.map(t =>
      t.id === selectedTransaction.id
        ? { ...t, status: 'cancelled' as const }
        : t
    ));

    setCancelDialog(false);
    setCancelReason('');
    setSelectedTransaction(null);

    console.log(`Transaction ${selectedTransaction.id} cancelled: ${cancelReason}`);
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              Sistema TEF
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                startIcon={<CreditCard />}
                onClick={() => simulateTransaction('credit', 50.00)}
                disabled={processing}
                color="primary"
              >
                Simular Crédito
              </Button>
              <Button
                variant="contained"
                startIcon={<Payment />}
                onClick={() => simulateTransaction('debit', 30.00)}
                disabled={processing}
                color="secondary"
              >
                Simular Débito
              </Button>
              <IconButton>
                <Refresh />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Status do Sistema */}
      {config && metrics && (
        <Paper sx={{ m: 2, p: 2, bgcolor: metrics.provider_status === 'online' ? 'success.light' : 'error.light' }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h6">
                {config.provider_name} - Terminal {config.terminal_id}
              </Typography>
              <Typography variant="body2">
                PinPad: {config.pinpad_model || 'Não configurado'} | 
                Status: {metrics.pinpad_status === 'ready' ? 'Pronto' : 'Offline'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'flex-end' }}>
                {metrics.provider_status === 'online' ? (
                  <Chip icon={<CloudDone />} label="Provedor Online" color="success" />
                ) : (
                  <Chip icon={<CloudOff />} label="Provedor Offline" color="error" />
                )}
                {metrics.pinpad_status === 'ready' ? (
                  <Chip icon={<DeviceHub />} label="PinPad Pronto" color="success" />
                ) : (
                  <Chip icon={<DeviceHub />} label="PinPad Offline" color="error" />
                )}
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Processamento */}
      {processing && (
        <Paper sx={{ m: 2, p: 2, bgcolor: 'info.light' }}>
          <Typography variant="h6" gutterBottom>
            Processando Transação...
          </Typography>
          <LinearProgress />
          <Typography variant="body2" sx={{ mt: 1 }}>
            Aguarde a confirmação do PinPad
          </Typography>
        </Paper>
      )}

      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Transações" />
          <Tab label="Métricas" />
          <Tab label="Configuração" />
        </Tabs>

        {/* Tab Transações */}
        {selectedTab === 0 && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} md={6}>
                <FormControl size="small" sx={{ minWidth: 200 }}>
                  <InputLabel>Filtrar por Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Filtrar por Status"
                  >
                    <MenuItem value="all">Todos</MenuItem>
                    <MenuItem value="approved">Aprovados</MenuItem>
                    <MenuItem value="pending">Pendentes</MenuItem>
                    <MenuItem value="processing">Processando</MenuItem>
                    <MenuItem value="denied">Negados</MenuItem>
                    <MenuItem value="cancelled">Cancelados</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Operação</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Cartão</TableCell>
                    <TableCell>NSU</TableCell>
                    <TableCell align="right">Valor</TableCell>
                    <TableCell>Parcelas</TableCell>
                    <TableCell>Data/Hora</TableCell>
                    <TableCell align="center">Ações</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredTransactions.map((transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell>
                        <Chip
                          label={getOperationName(transaction.operation)}
                          size="small"
                          variant="outlined"
                          color={transaction.operation === 'credit' ? 'primary' : 'secondary'}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(transaction.status)}
                          label={transaction.status}
                          color={getStatusColor(transaction.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {transaction.card_brand && transaction.card_number_masked ? (
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {transaction.card_brand}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {transaction.card_number_masked}
                            </Typography>
                          </Box>
                        ) : (
                          '-'
                        )}
                      </TableCell>
                      <TableCell>
                        {transaction.nsu || '-'}
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(transaction.amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        {transaction.installments}x
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDateTime(transaction.created_at)}
                        </Typography>
                        {transaction.processed_at && (
                          <Typography variant="caption" color="text.secondary">
                            Proc: {new Date(transaction.processed_at).toLocaleTimeString('pt-BR')}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedTransaction(transaction);
                            setDetailsDialog(true);
                          }}
                        >
                          <Assessment />
                        </IconButton>
                        {transaction.status === 'approved' && (
                          <>
                            <IconButton size="small">
                              <Print />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => {
                                setSelectedTransaction(transaction);
                                setCancelDialog(true);
                              }}
                            >
                              <Cancel />
                            </IconButton>
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Tab Métricas */}
        {selectedTab === 1 && metrics && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <CreditCard sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h4" color="primary">
                      {metrics.transactions_today}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Transações Hoje
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <CheckCircle sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {metrics.approval_rate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Taxa de Aprovação
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <TrendingUp sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                    <Typography variant="h4" color="info.main">
                      {formatCurrency(metrics.total_amount_today)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Volume Hoje
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Warning sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {metrics.transactions_pending}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pendentes
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Status do Sistema
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          {metrics.provider_status === 'online' ? (
                            <CloudDone color="success" />
                          ) : (
                            <CloudOff color="error" />
                          )}
                        </ListItemIcon>
                        <ListItemText
                          primary="Provedor TEF"
                          secondary={metrics.provider_status === 'online' ? 'Online' : 'Offline'}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <DeviceHub color={metrics.pinpad_status === 'ready' ? 'success' : 'error'} />
                        </ListItemIcon>
                        <ListItemText
                          primary="PinPad"
                          secondary={metrics.pinpad_status === 'ready' ? 'Pronto' : 'Offline'}
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Limite Diário
                    </Typography>
                    <Box sx={{ mt: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">
                          Usado: {formatCurrency(metrics.daily_limit_used)}
                        </Typography>
                        <Typography variant="body2">
                          Limite: {formatCurrency(metrics.daily_limit_total)}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={(metrics.daily_limit_used / metrics.daily_limit_total) * 100}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                      <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                        {((metrics.daily_limit_used / metrics.daily_limit_total) * 100).toFixed(1)}% utilizado
                      </Typography>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Tab Configuração */}
        {selectedTab === 2 && config && (
          <Box sx={{ p: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              Configurações do sistema TEF. Para alterar, entre em contato com o administrador.
            </Alert>

            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Configuração do Provedor
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Provedor"
                      value={config.provider_name}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Terminal ID"
                      value={config.terminal_id}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Merchant ID"
                      value={config.merchant_id}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Modelo do PinPad"
                      value={config.pinpad_model || 'Não configurado'}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Box>
        )}
      </Box>

      {/* Dialog Detalhes */}
      <Dialog open={detailsDialog} onClose={() => setDetailsDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Detalhes da Transação TEF
        </DialogTitle>
        <DialogContent>
          {selectedTransaction && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Operação: {getOperationName(selectedTransaction.operation)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Status: {selectedTransaction.status}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Valor: {formatCurrency(selectedTransaction.amount)}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Parcelas: {selectedTransaction.installments}x
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    NSU: {selectedTransaction.nsu || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Autorização: {selectedTransaction.authorization_code || 'N/A'}
                  </Typography>
                </Grid>
              </Grid>

              {selectedTransaction.card_brand && (
                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  Cartão: {selectedTransaction.card_brand} {selectedTransaction.card_number_masked}
                </Typography>
              )}

              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Criado em: {formatDateTime(selectedTransaction.created_at)}
              </Typography>

              {selectedTransaction.processed_at && (
                <Typography variant="body2" color="text.secondary">
                  Processado em: {formatDateTime(selectedTransaction.processed_at)}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialog(false)}>
            Fechar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog Cancelamento */}
      <Dialog open={cancelDialog} onClose={() => setCancelDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Cancelar Transação TEF
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            O cancelamento de transação TEF é irreversível. Informe o motivo do cancelamento.
          </Alert>
          <TextField
            fullWidth
            label="Motivo do Cancelamento"
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
            multiline
            rows={3}
            placeholder="Ex: Cliente desistiu, erro no valor, etc."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCancelDialog(false)}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={cancelTransaction}
            disabled={!cancelReason.trim() || cancelReason.length < 10}
          >
            Confirmar Cancelamento
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Tef;
