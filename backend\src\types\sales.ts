// Tipos para vendas e pedidos

export interface Customer {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  cpf_cnpj?: string;
  birth_date?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateCustomerRequest {
  name: string;
  email?: string;
  phone?: string;
  cpf_cnpj?: string;
  birth_date?: string;
}

export interface UpdateCustomerRequest {
  name?: string;
  email?: string;
  phone?: string;
  cpf_cnpj?: string;
  birth_date?: string;
  is_active?: boolean;
}

export interface CustomerAddress {
  id: number;
  customer_id: number;
  street: string;
  number?: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zip_code: string;
  is_default: boolean;
  delivery_fee: number;
  created_at: string;
}

export interface CreateCustomerAddressRequest {
  customer_id: number;
  street: string;
  number?: string;
  complement?: string;
  neighborhood: string;
  city: string;
  state: string;
  zip_code: string;
  is_default?: boolean;
  delivery_fee?: number;
}

export interface Table {
  id: number;
  number: number;
  name?: string;
  capacity: number;
  status: TableStatus;
  qr_code?: string;
  created_at: string;
  updated_at: string;
}

export type TableStatus = 'available' | 'occupied' | 'reserved' | 'maintenance';

export interface CreateTableRequest {
  number: number;
  name?: string;
  capacity?: number;
}

export interface UpdateTableRequest {
  number?: number;
  name?: string;
  capacity?: number;
  status?: TableStatus;
}

export interface Order {
  id: number;
  order_number: string;
  type: OrderType;
  status: OrderStatus;
  customer_id?: number;
  customer?: Customer;
  table_id?: number;
  table?: Table;
  user_id: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
  cash_register_id?: number;
  subtotal: number;
  discount: number;
  delivery_fee: number;
  total: number;
  payment_status: PaymentStatus;
  notes?: string;
  delivery_address?: string;
  estimated_time?: number;
  created_at: string;
  updated_at: string;
  items?: OrderItem[];
  payments?: Payment[];
}

export type OrderType = 'counter' | 'table' | 'delivery';
export type OrderStatus = 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
export type PaymentStatus = 'pending' | 'paid' | 'cancelled';

export interface CreateOrderRequest {
  type: OrderType;
  customer_id?: number;
  table_id?: number;
  notes?: string;
  delivery_address?: string;
  estimated_time?: number;
  items: CreateOrderItemRequest[];
}

export interface UpdateOrderRequest {
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  notes?: string;
  estimated_time?: number;
  discount?: number;
}

export interface OrderItem {
  id: number;
  order_id: number;
  product_id: number;
  product?: {
    id: number;
    name: string;
    price: number;
    preparation_time: number;
    kitchen_printer?: string;
  };
  quantity: number;
  unit_price: number;
  total_price: number;
  notes?: string;
  status: OrderItemStatus;
  created_at: string;
  updated_at: string;
}

export type OrderItemStatus = 'pending' | 'preparing' | 'ready' | 'delivered';

export interface CreateOrderItemRequest {
  product_id: number;
  quantity: number;
  unit_price?: number; // Se não fornecido, usa preço do produto
  notes?: string;
}

export interface UpdateOrderItemRequest {
  quantity?: number;
  unit_price?: number;
  notes?: string;
  status?: OrderItemStatus;
}

export interface Payment {
  id: number;
  order_id: number;
  method: PaymentMethod;
  amount: number;
  received_amount?: number;
  change_amount: number;
  installments: number;
  card_brand?: string;
  authorization_code?: string;
  nsu?: string;
  status: PaymentTransactionStatus;
  processed_at?: string;
  created_at: string;
}

export type PaymentMethod = 'cash' | 'credit_card' | 'debit_card' | 'pix' | 'voucher';
export type PaymentTransactionStatus = 'pending' | 'approved' | 'cancelled' | 'refunded';

export interface CreatePaymentRequest {
  order_id: number;
  method: PaymentMethod;
  amount: number;
  received_amount?: number;
  installments?: number;
  card_brand?: string;
}

export interface UpdatePaymentRequest {
  status?: PaymentTransactionStatus;
  authorization_code?: string;
  nsu?: string;
  processed_at?: string;
}

export interface CashRegister {
  id: number;
  user_id: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
  opening_amount: number;
  closing_amount?: number;
  total_sales: number;
  total_cash: number;
  total_card: number;
  total_pix: number;
  total_voucher: number;
  withdrawals: number;
  supplies: number;
  status: CashRegisterStatus;
  opened_at: string;
  closed_at?: string;
}

export type CashRegisterStatus = 'open' | 'closed';

export interface CreateCashRegisterRequest {
  opening_amount: number;
}

export interface UpdateCashRegisterRequest {
  closing_amount?: number;
  total_sales?: number;
  total_cash?: number;
  total_card?: number;
  total_pix?: number;
  total_voucher?: number;
  withdrawals?: number;
  supplies?: number;
  status?: CashRegisterStatus;
}

export interface CashMovement {
  id: number;
  cash_register_id: number;
  type: CashMovementType;
  amount: number;
  description?: string;
  user_id: number;
  user?: {
    id: number;
    username: string;
    full_name: string;
  };
  created_at: string;
}

export type CashMovementType = 'opening' | 'sale' | 'withdrawal' | 'supply' | 'closing';

export interface CreateCashMovementRequest {
  cash_register_id: number;
  type: CashMovementType;
  amount: number;
  description?: string;
}

// Filtros
export interface OrderFilters {
  type?: OrderType;
  status?: OrderStatus;
  payment_status?: PaymentStatus;
  customer_id?: number;
  table_id?: number;
  user_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CustomerFilters {
  is_active?: boolean;
  search?: string;
}

export interface PaymentFilters {
  method?: PaymentMethod;
  status?: PaymentTransactionStatus;
  order_id?: number;
  date_from?: string;
  date_to?: string;
}

// Relatórios
export interface SalesReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_orders: number;
    total_sales: number;
    total_items: number;
    average_ticket: number;
    cancelled_orders: number;
    cancelled_amount: number;
  };
  by_payment_method: Array<{
    method: PaymentMethod;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_order_type: Array<{
    type: OrderType;
    count: number;
    amount: number;
    percentage: number;
  }>;
  top_products: Array<{
    product_id: number;
    product_name: string;
    quantity_sold: number;
    total_revenue: number;
  }>;
  hourly_sales: Array<{
    hour: number;
    orders: number;
    amount: number;
  }>;
}

export interface CashRegisterReport {
  cash_register: CashRegister;
  movements: CashMovement[];
  summary: {
    opening_amount: number;
    total_sales: number;
    total_cash: number;
    total_card: number;
    total_pix: number;
    total_voucher: number;
    withdrawals: number;
    supplies: number;
    expected_closing: number;
    actual_closing?: number;
    difference?: number;
  };
}

// Validações e regras de negócio
export interface OrderValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
  stock_issues?: Array<{
    product_id: number;
    product_name: string;
    requested: number;
    available: number;
  }>;
}

export interface PaymentValidation {
  valid: boolean;
  errors: string[];
  total_paid: number;
  total_order: number;
  remaining: number;
  change: number;
}

// Configurações de PDV
export interface POSSettings {
  auto_print_receipt: boolean;
  auto_print_kitchen: boolean;
  allow_negative_stock: boolean;
  require_customer_cpf: boolean;
  default_payment_method: PaymentMethod;
  max_discount_percent: number;
  enable_delivery: boolean;
  default_delivery_fee: number;
  enable_table_service: boolean;
  kitchen_display_enabled: boolean;
  fiscal_integration_enabled: boolean;
}
