import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  Order, 
  CreateOrderRequest, 
  UpdateOrderRequest, 
  OrderFilters,
  OrderItem,
  CreateOrderItemRequest,
  UpdateOrderItemRequest,
  OrderValidation
} from '../types/sales';
import { ProductModel } from './Product';
import { StockMovementModel } from './StockMovement';

export class OrderModel {
  // Buscar pedido por ID
  static async findById(id: number): Promise<Order | null> {
    const order = await executeQuerySingle<Order>(
      `SELECT o.*, 
              c.name as customer_name,
              t.number as table_number,
              u.username, u.full_name as user_name
       FROM orders o
       LEFT JOIN customers c ON o.customer_id = c.id
       LEFT JOIN tables t ON o.table_id = t.id
       LEFT JOIN users u ON o.user_id = u.id
       WHERE o.id = ?`,
      [id]
    );
    return order || null;
  }

  // Buscar pedido por número
  static async findByOrderNumber(orderNumber: string): Promise<Order | null> {
    const order = await executeQuerySingle<Order>(
      `SELECT o.*, 
              c.name as customer_name,
              t.number as table_number,
              u.username, u.full_name as user_name
       FROM orders o
       LEFT JOIN customers c ON o.customer_id = c.id
       LEFT JOIN tables t ON o.table_id = t.id
       LEFT JOIN users u ON o.user_id = u.id
       WHERE o.order_number = ?`,
      [orderNumber]
    );
    return order || null;
  }

  // Listar pedidos com filtros
  static async findAll(filters: OrderFilters = {}): Promise<Order[]> {
    let query = `
      SELECT o.*, 
             c.name as customer_name,
             t.number as table_number,
             u.username, u.full_name as user_name
      FROM orders o
      LEFT JOIN customers c ON o.customer_id = c.id
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.user_id = u.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.type) {
      query += ' AND o.type = ?';
      params.push(filters.type);
    }

    if (filters.status) {
      query += ' AND o.status = ?';
      params.push(filters.status);
    }

    if (filters.payment_status) {
      query += ' AND o.payment_status = ?';
      params.push(filters.payment_status);
    }

    if (filters.customer_id) {
      query += ' AND o.customer_id = ?';
      params.push(filters.customer_id);
    }

    if (filters.table_id) {
      query += ' AND o.table_id = ?';
      params.push(filters.table_id);
    }

    if (filters.user_id) {
      query += ' AND o.user_id = ?';
      params.push(filters.user_id);
    }

    if (filters.date_from) {
      query += ' AND DATE(o.created_at) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(o.created_at) <= ?';
      params.push(filters.date_to);
    }

    if (filters.search) {
      query += ' AND (o.order_number LIKE ? OR c.name LIKE ? OR o.notes LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY o.created_at DESC';

    return await executeQuery<Order>(query, params);
  }

  // Gerar número do pedido
  static async generateOrderNumber(): Promise<string> {
    const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
    
    const result = await executeQuerySingle<{ count: number }>(
      `SELECT COUNT(*) as count FROM orders 
       WHERE DATE(created_at) = DATE('now')`
    );
    
    const dailyCount = (result?.count || 0) + 1;
    return `${today}${dailyCount.toString().padStart(4, '0')}`;
  }

  // Criar pedido
  static async create(orderData: CreateOrderRequest, userId: number): Promise<Order> {
    return await executeTransaction(async (db) => {
      // Validar pedido
      const validation = await this.validateOrder(orderData);
      if (!validation.valid) {
        throw new Error(`Order validation failed: ${validation.errors.join(', ')}`);
      }

      // Gerar número do pedido
      const orderNumber = await this.generateOrderNumber();

      // Calcular totais
      let subtotal = 0;
      for (const item of orderData.items) {
        const product = await ProductModel.findById(item.product_id);
        if (!product) {
          throw new Error(`Product ${item.product_id} not found`);
        }
        const unitPrice = item.unit_price || product.price;
        subtotal += unitPrice * item.quantity;
      }

      const deliveryFee = orderData.type === 'delivery' ? 5.00 : 0; // TODO: Configurável
      const total = subtotal + deliveryFee;

      // Inserir pedido
      const result = await executeUpdate(
        `INSERT INTO orders (
          order_number, type, status, customer_id, table_id, user_id,
          subtotal, discount, delivery_fee, total, payment_status,
          notes, delivery_address, estimated_time
        ) VALUES (?, ?, 'pending', ?, ?, ?, ?, 0, ?, ?, 'pending', ?, ?, ?)`,
        [
          orderNumber,
          orderData.type,
          orderData.customer_id || null,
          orderData.table_id || null,
          userId,
          subtotal,
          deliveryFee,
          total,
          orderData.notes || null,
          orderData.delivery_address || null,
          orderData.estimated_time || null,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create order');
      }

      const orderId = result.lastID;

      // Inserir itens do pedido
      for (const item of orderData.items) {
        const product = await ProductModel.findById(item.product_id);
        if (!product) {
          throw new Error(`Product ${item.product_id} not found`);
        }

        const unitPrice = item.unit_price || product.price;
        const totalPrice = unitPrice * item.quantity;

        await executeUpdate(
          `INSERT INTO order_items (
            order_id, product_id, quantity, unit_price, total_price, notes, status
          ) VALUES (?, ?, ?, ?, ?, ?, 'pending')`,
          [
            orderId,
            item.product_id,
            item.quantity,
            unitPrice,
            totalPrice,
            item.notes || null,
          ]
        );

        // Baixar estoque se produto tem controle de estoque
        if (product.stock_control) {
          await ProductModel.updateStock(item.product_id, item.quantity, 'subtract');
          
          // Registrar movimentação de estoque
          await StockMovementModel.create({
            type: 'product',
            item_id: item.product_id,
            movement_type: 'out',
            quantity: item.quantity,
            reason: `Sale - Order ${orderNumber}`,
            order_id: orderId,
          }, userId);
        }
      }

      // Atualizar status da mesa se for pedido de mesa
      if (orderData.table_id) {
        await executeUpdate(
          'UPDATE tables SET status = "occupied" WHERE id = ?',
          [orderData.table_id]
        );
      }

      // Retornar pedido criado
      const newOrder = await this.findById(orderId);
      if (!newOrder) {
        throw new Error('Failed to retrieve created order');
      }

      return newOrder;
    });
  }

  // Atualizar pedido
  static async update(id: number, orderData: UpdateOrderRequest): Promise<Order> {
    const order = await this.findById(id);
    if (!order) {
      throw new Error('Order not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['status', 'payment_status', 'notes', 'estimated_time', 'discount'];

    updatableFields.forEach(field => {
      if (orderData[field as keyof UpdateOrderRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        params.push(orderData[field as keyof UpdateOrderRequest]);
      }
    });

    if (updateFields.length === 0) {
      return order; // Nada para atualizar
    }

    // Recalcular total se desconto foi alterado
    if (orderData.discount !== undefined) {
      const newTotal = order.subtotal + order.delivery_fee - orderData.discount;
      updateFields.push('total = ?');
      params.push(newTotal);
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE orders SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Liberar mesa se pedido foi entregue/cancelado
    if (orderData.status === 'delivered' || orderData.status === 'cancelled') {
      if (order.table_id) {
        await executeUpdate(
          'UPDATE tables SET status = "available" WHERE id = ?',
          [order.table_id]
        );
      }
    }

    // Retornar pedido atualizado
    const updatedOrder = await this.findById(id);
    if (!updatedOrder) {
      throw new Error('Failed to retrieve updated order');
    }

    return updatedOrder;
  }

  // Cancelar pedido
  static async cancel(id: number, userId: number): Promise<Order> {
    return await executeTransaction(async (db) => {
      const order = await this.findById(id);
      if (!order) {
        throw new Error('Order not found');
      }

      if (order.status === 'cancelled') {
        throw new Error('Order is already cancelled');
      }

      if (order.payment_status === 'paid') {
        throw new Error('Cannot cancel paid order');
      }

      // Buscar itens do pedido
      const items = await this.getOrderItems(id);

      // Devolver estoque
      for (const item of items) {
        const product = await ProductModel.findById(item.product_id);
        if (product && product.stock_control) {
          await ProductModel.updateStock(item.product_id, item.quantity, 'add');
          
          // Registrar movimentação de estoque
          await StockMovementModel.create({
            type: 'product',
            item_id: item.product_id,
            movement_type: 'in',
            quantity: item.quantity,
            reason: `Order cancellation - Order ${order.order_number}`,
            order_id: id,
          }, userId);
        }
      }

      // Atualizar status do pedido
      await executeUpdate(
        'UPDATE orders SET status = "cancelled", payment_status = "cancelled", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );

      // Liberar mesa
      if (order.table_id) {
        await executeUpdate(
          'UPDATE tables SET status = "available" WHERE id = ?',
          [order.table_id]
        );
      }

      // Retornar pedido atualizado
      const updatedOrder = await this.findById(id);
      if (!updatedOrder) {
        throw new Error('Failed to retrieve updated order');
      }

      return updatedOrder;
    });
  }

  // Buscar itens do pedido
  static async getOrderItems(orderId: number): Promise<OrderItem[]> {
    return await executeQuery<OrderItem>(
      `SELECT oi.*, p.name as product_name, p.price as product_price
       FROM order_items oi
       LEFT JOIN products p ON oi.product_id = p.id
       WHERE oi.order_id = ?
       ORDER BY oi.created_at ASC`,
      [orderId]
    );
  }

  // Adicionar item ao pedido
  static async addItem(orderId: number, itemData: CreateOrderItemRequest, userId: number): Promise<OrderItem> {
    const order = await this.findById(orderId);
    if (!order) {
      throw new Error('Order not found');
    }

    if (order.status === 'delivered' || order.status === 'cancelled') {
      throw new Error('Cannot add items to completed order');
    }

    const product = await ProductModel.findById(itemData.product_id);
    if (!product) {
      throw new Error('Product not found');
    }

    const unitPrice = itemData.unit_price || product.price;
    const totalPrice = unitPrice * itemData.quantity;

    // Inserir item
    const result = await executeUpdate(
      `INSERT INTO order_items (
        order_id, product_id, quantity, unit_price, total_price, notes, status
      ) VALUES (?, ?, ?, ?, ?, ?, 'pending')`,
      [
        orderId,
        itemData.product_id,
        itemData.quantity,
        unitPrice,
        totalPrice,
        itemData.notes || null,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to add order item');
    }

    // Baixar estoque
    if (product.stock_control) {
      await ProductModel.updateStock(itemData.product_id, itemData.quantity, 'subtract');
      
      await StockMovementModel.create({
        type: 'product',
        item_id: itemData.product_id,
        movement_type: 'out',
        quantity: itemData.quantity,
        reason: `Sale - Order ${order.order_number}`,
        order_id: orderId,
      }, userId);
    }

    // Atualizar totais do pedido
    await this.recalculateOrderTotals(orderId);

    // Retornar item criado
    const newItem = await executeQuerySingle<OrderItem>(
      `SELECT oi.*, p.name as product_name, p.price as product_price
       FROM order_items oi
       LEFT JOIN products p ON oi.product_id = p.id
       WHERE oi.id = ?`,
      [result.lastID]
    );

    if (!newItem) {
      throw new Error('Failed to retrieve created order item');
    }

    return newItem;
  }

  // Recalcular totais do pedido
  static async recalculateOrderTotals(orderId: number): Promise<void> {
    const result = await executeQuerySingle<{ subtotal: number }>(
      'SELECT COALESCE(SUM(total_price), 0) as subtotal FROM order_items WHERE order_id = ?',
      [orderId]
    );

    const subtotal = result?.subtotal || 0;
    
    const order = await this.findById(orderId);
    if (order) {
      const total = subtotal + order.delivery_fee - order.discount;
      
      await executeUpdate(
        'UPDATE orders SET subtotal = ?, total = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [subtotal, total, orderId]
      );
    }
  }

  // Validar pedido
  static async validateOrder(orderData: CreateOrderRequest): Promise<OrderValidation> {
    const errors: string[] = [];
    const warnings: string[] = [];
    const stockIssues: any[] = [];

    // Validar itens
    if (!orderData.items || orderData.items.length === 0) {
      errors.push('Order must have at least one item');
    }

    // Validar produtos e estoque
    for (const item of orderData.items || []) {
      const product = await ProductModel.findById(item.product_id);
      if (!product) {
        errors.push(`Product ${item.product_id} not found`);
        continue;
      }

      if (!product.is_active) {
        errors.push(`Product ${product.name} is not active`);
        continue;
      }

      if (product.stock_control) {
        const available = await ProductModel.checkAvailability(item.product_id, item.quantity);
        if (!available) {
          stockIssues.push({
            product_id: item.product_id,
            product_name: product.name,
            requested: item.quantity,
            available: product.current_stock,
          });
          errors.push(`Insufficient stock for ${product.name}`);
        }
      }

      if (item.quantity <= 0) {
        errors.push(`Invalid quantity for ${product.name}`);
      }
    }

    // Validar mesa (se fornecida)
    if (orderData.table_id) {
      const table = await executeQuerySingle<{ status: string }>(
        'SELECT status FROM tables WHERE id = ?',
        [orderData.table_id]
      );
      
      if (!table) {
        errors.push('Table not found');
      } else if (table.status === 'occupied') {
        warnings.push('Table is already occupied');
      } else if (table.status === 'maintenance') {
        errors.push('Table is under maintenance');
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      stock_issues: stockIssues.length > 0 ? stockIssues : undefined,
    };
  }

  // Buscar pedidos em aberto
  static async findOpen(): Promise<Order[]> {
    return await this.findAll({
      status: 'pending',
    });
  }

  // Buscar pedidos do dia
  static async findToday(): Promise<Order[]> {
    const today = new Date().toISOString().slice(0, 10);
    return await this.findAll({
      date_from: today,
      date_to: today,
    });
  }
}
