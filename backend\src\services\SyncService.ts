import { 
  SyncOperation, 
  SyncQueue, 
  SyncConflict, 
  SyncRequest, 
  SyncResponse,
  SyncEntityType,
  SyncOperationType,
  SyncStatus,
  ConflictResolution,
  OfflineStorage,
  SyncMetrics,
  NetworkStatus
} from '../types/sync';
import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';

/**
 * Serviço de Sincronização Offline-First
 * Gerencia sincronização bidirecional de dados
 */
export class SyncService {
  private static instance: SyncService;
  private syncQueue: Map<string, SyncQueue> = new Map();
  private isOnline: boolean = true;
  private networkStatus: NetworkStatus = 'online';
  private syncInProgress: boolean = false;

  private constructor() {
    this.initializeNetworkMonitoring();
    this.startBackgroundSync();
  }

  static getInstance(): SyncService {
    if (!SyncService.instance) {
      SyncService.instance = new SyncService();
    }
    return SyncService.instance;
  }

  // ===== SINCRONIZAÇÃO PRINCIPAL =====

  // Iniciar sincronização
  async startSync(request: SyncRequest): Promise<SyncResponse> {
    try {
      if (this.syncInProgress) {
        throw new Error('Sync already in progress');
      }

      this.syncInProgress = true;
      const queueId = this.generateId();
      
      // Criar fila de sincronização
      const operations = await this.buildSyncOperations(request);
      const queue: SyncQueue = {
        id: queueId,
        operations,
        status: 'pending',
        total_operations: operations.length,
        completed_operations: 0,
        failed_operations: 0,
        started_at: new Date().toISOString(),
      };

      this.syncQueue.set(queueId, queue);

      // Processar fila em background
      this.processSyncQueue(queueId);

      return {
        queue_id: queueId,
        total_operations: operations.length,
        estimated_duration: operations.length * 0.5, // 0.5s por operação
        started_at: queue.started_at,
        status: 'running',
      };
    } catch (error) {
      this.syncInProgress = false;
      throw error;
    }
  }

  // Construir operações de sincronização
  private async buildSyncOperations(request: SyncRequest): Promise<SyncOperation[]> {
    const operations: SyncOperation[] = [];
    const entityTypes = request.entity_types || ['orders', 'products', 'payments'];

    for (const entityType of entityTypes) {
      // Buscar dados pendentes de sincronização
      const pendingData = await this.getPendingSyncData(entityType, request.since_timestamp);
      
      for (const data of pendingData) {
        operations.push({
          id: this.generateId(),
          entity_type: entityType,
          entity_id: data.id,
          operation: data.operation || 'update',
          direction: request.direction || 'up',
          status: 'pending',
          data: data,
          retry_count: 0,
          max_retries: 3,
          created_at: new Date().toISOString(),
        });
      }
    }

    return operations;
  }

  // Processar fila de sincronização
  private async processSyncQueue(queueId: string): Promise<void> {
    try {
      const queue = this.syncQueue.get(queueId);
      if (!queue) return;

      queue.status = 'running';

      for (const operation of queue.operations) {
        try {
          await this.processOperation(operation);
          queue.completed_operations++;
        } catch (error) {
          operation.status = 'failed';
          operation.error_message = error instanceof Error ? error.message : 'Unknown error';
          queue.failed_operations++;
          
          console.error(`Sync operation failed: ${operation.id}`, error);
        }
      }

      queue.status = queue.failed_operations > 0 ? 'failed' : 'completed';
      queue.completed_at = new Date().toISOString();

      console.log(`Sync queue ${queueId} completed: ${queue.completed_operations}/${queue.total_operations} operations`);
    } catch (error) {
      console.error(`Sync queue ${queueId} failed:`, error);
    } finally {
      this.syncInProgress = false;
    }
  }

  // Processar operação individual
  private async processOperation(operation: SyncOperation): Promise<void> {
    operation.status = 'processing';
    operation.processed_at = new Date().toISOString();

    try {
      switch (operation.operation) {
        case 'create':
          await this.syncCreate(operation);
          break;
        case 'update':
          await this.syncUpdate(operation);
          break;
        case 'delete':
          await this.syncDelete(operation);
          break;
        default:
          throw new Error(`Unknown operation type: ${operation.operation}`);
      }

      operation.status = 'completed';
      operation.synced_at = new Date().toISOString();
    } catch (error) {
      operation.retry_count++;
      
      if (operation.retry_count >= operation.max_retries) {
        operation.status = 'failed';
        operation.error_message = error instanceof Error ? error.message : 'Max retries exceeded';
      } else {
        operation.status = 'pending';
        // Reagendar para retry
        setTimeout(() => this.processOperation(operation), 5000 * operation.retry_count);
      }
    }
  }

  // ===== OPERAÇÕES DE SINCRONIZAÇÃO =====

  private async syncCreate(operation: SyncOperation): Promise<void> {
    // Simular criação no servidor
    await this.delay(100);
    
    // Verificar se já existe (conflito)
    const existing = await this.checkExistingEntity(operation.entity_type, operation.entity_id);
    if (existing) {
      await this.handleConflict(operation, existing);
      return;
    }

    // Criar no servidor
    console.log(`Creating ${operation.entity_type} ${operation.entity_id}`);
  }

  private async syncUpdate(operation: SyncOperation): Promise<void> {
    // Simular atualização no servidor
    await this.delay(100);
    
    // Verificar versão (conflito de atualização concorrente)
    const serverData = await this.getServerData(operation.entity_type, operation.entity_id);
    if (serverData && serverData.version !== operation.data.version) {
      await this.handleConflict(operation, serverData);
      return;
    }

    // Atualizar no servidor
    console.log(`Updating ${operation.entity_type} ${operation.entity_id}`);
  }

  private async syncDelete(operation: SyncOperation): Promise<void> {
    // Simular exclusão no servidor
    await this.delay(100);
    
    // Verificar se foi modificado no servidor (conflito delete-update)
    const serverData = await this.getServerData(operation.entity_type, operation.entity_id);
    if (serverData && serverData.updated_at > operation.data.deleted_at) {
      await this.handleConflict(operation, serverData);
      return;
    }

    // Deletar no servidor
    console.log(`Deleting ${operation.entity_type} ${operation.entity_id}`);
  }

  // ===== RESOLUÇÃO DE CONFLITOS =====

  private async handleConflict(operation: SyncOperation, serverData: any): Promise<void> {
    const conflict: SyncConflict = {
      id: this.generateId(),
      entity_type: operation.entity_type,
      entity_id: operation.entity_id,
      operation_id: operation.id,
      conflict_type: 'concurrent_update',
      server_data: serverData,
      client_data: operation.data,
      resolution: 'manual', // Padrão para resolução manual
      created_at: new Date().toISOString(),
    };

    // Salvar conflito para resolução posterior
    await this.saveConflict(conflict);
    
    operation.status = 'conflict';
    operation.conflict_data = conflict;
  }

  async resolveConflict(conflictId: string, resolution: ConflictResolution, resolvedData?: any): Promise<void> {
    const conflict = await this.getConflict(conflictId);
    if (!conflict) {
      throw new Error('Conflict not found');
    }

    switch (resolution) {
      case 'server_wins':
        conflict.resolved_data = conflict.server_data;
        break;
      case 'client_wins':
        conflict.resolved_data = conflict.client_data;
        break;
      case 'merge':
        conflict.resolved_data = this.mergeData(conflict.server_data, conflict.client_data);
        break;
      case 'manual':
        if (!resolvedData) {
          throw new Error('Resolved data required for manual resolution');
        }
        conflict.resolved_data = resolvedData;
        break;
    }

    conflict.resolution = resolution;
    conflict.resolved_at = new Date().toISOString();

    await this.updateConflict(conflict);
    
    // Aplicar resolução
    await this.applyConflictResolution(conflict);
  }

  private mergeData(serverData: any, clientData: any): any {
    // Estratégia simples de merge - campos do cliente sobrescrevem servidor
    // exceto timestamps que mantém o mais recente
    const merged = { ...serverData, ...clientData };
    
    if (serverData.updated_at && clientData.updated_at) {
      merged.updated_at = serverData.updated_at > clientData.updated_at 
        ? serverData.updated_at 
        : clientData.updated_at;
    }

    return merged;
  }

  // ===== ARMAZENAMENTO OFFLINE =====

  async saveOfflineData(entityType: SyncEntityType, entityId: number, data: any): Promise<void> {
    const offlineData: OfflineStorage = {
      id: this.generateId(),
      entity_type: entityType,
      entity_id: entityId,
      data,
      version: data.version || 1,
      last_modified: new Date().toISOString(),
      is_dirty: true,
      is_deleted: false,
      sync_status: 'pending',
      created_offline: !this.isOnline,
    };

    await executeUpdate(
      `INSERT OR REPLACE INTO offline_storage 
       (id, entity_type, entity_id, data, version, last_modified, is_dirty, is_deleted, sync_status, created_offline)
       VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        offlineData.id,
        offlineData.entity_type,
        offlineData.entity_id,
        JSON.stringify(offlineData.data),
        offlineData.version,
        offlineData.last_modified,
        offlineData.is_dirty ? 1 : 0,
        offlineData.is_deleted ? 1 : 0,
        offlineData.sync_status,
        offlineData.created_offline ? 1 : 0,
      ]
    );
  }

  async getOfflineData(entityType: SyncEntityType, entityId?: number): Promise<OfflineStorage[]> {
    let query = `SELECT * FROM offline_storage WHERE entity_type = ?`;
    const params: any[] = [entityType];

    if (entityId) {
      query += ' AND entity_id = ?';
      params.push(entityId);
    }

    query += ' ORDER BY last_modified DESC';

    const results = await executeQuery<any>(query, params);
    
    return results.map(row => ({
      ...row,
      data: JSON.parse(row.data),
      is_dirty: row.is_dirty === 1,
      is_deleted: row.is_deleted === 1,
      created_offline: row.created_offline === 1,
    }));
  }

  // ===== MÉTRICAS E STATUS =====

  async getSyncMetrics(): Promise<SyncMetrics> {
    const totalEntities = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM offline_storage'
    );

    const syncedEntities = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM offline_storage WHERE sync_status = "synced"'
    );

    const pendingEntities = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM offline_storage WHERE sync_status = "pending"'
    );

    const conflictEntities = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM offline_storage WHERE sync_status = "conflict"'
    );

    return {
      total_entities: totalEntities?.count || 0,
      synced_entities: syncedEntities?.count || 0,
      pending_entities: pendingEntities?.count || 0,
      conflict_entities: conflictEntities?.count || 0,
      error_entities: 0,
      last_sync_duration: 2.5,
      average_sync_time: 1.8,
      sync_success_rate: 95.2,
      network_status: this.networkStatus,
      storage_usage: {
        total_size: 15.6,
        offline_size: 8.2,
        cache_size: 7.4,
      },
    };
  }

  getSyncQueueStatus(queueId: string): SyncQueue | null {
    return this.syncQueue.get(queueId) || null;
  }

  // ===== MONITORAMENTO DE REDE =====

  private initializeNetworkMonitoring(): void {
    // Simular monitoramento de rede
    setInterval(() => {
      this.checkNetworkStatus();
    }, 5000);
  }

  private async checkNetworkStatus(): Promise<void> {
    try {
      // Simular verificação de conectividade
      const wasOnline = this.isOnline;
      this.isOnline = Math.random() > 0.05; // 95% de uptime
      
      if (!wasOnline && this.isOnline) {
        console.log('Network restored - triggering sync');
        await this.onNetworkRestored();
      } else if (wasOnline && !this.isOnline) {
        console.log('Network lost - switching to offline mode');
        this.onNetworkLost();
      }

      this.networkStatus = this.isOnline ? 'online' : 'offline';
    } catch (error) {
      this.isOnline = false;
      this.networkStatus = 'offline';
    }
  }

  private async onNetworkRestored(): Promise<void> {
    // Sincronizar dados pendentes quando a rede for restaurada
    try {
      await this.startSync({
        entity_types: ['orders', 'payments', 'products'],
        direction: 'bidirectional',
        force_sync: false,
      });
    } catch (error) {
      console.error('Auto-sync on network restore failed:', error);
    }
  }

  private onNetworkLost(): void {
    // Pausar sincronizações em andamento
    this.syncQueue.forEach(queue => {
      if (queue.status === 'running') {
        queue.status = 'paused';
      }
    });
  }

  // ===== SINCRONIZAÇÃO EM BACKGROUND =====

  private startBackgroundSync(): void {
    // Sincronização automática a cada 5 minutos
    setInterval(async () => {
      if (this.isOnline && !this.syncInProgress) {
        try {
          await this.startSync({
            entity_types: ['orders', 'payments'],
            direction: 'up',
            force_sync: false,
          });
        } catch (error) {
          console.error('Background sync failed:', error);
        }
      }
    }, 5 * 60 * 1000);
  }

  // ===== UTILITÁRIOS =====

  private async getPendingSyncData(entityType: SyncEntityType, sinceTimestamp?: string): Promise<any[]> {
    // Simular busca de dados pendentes
    const mockData = [];
    const count = Math.floor(Math.random() * 5) + 1;
    
    for (let i = 0; i < count; i++) {
      mockData.push({
        id: Math.floor(Math.random() * 1000),
        operation: 'update',
        version: 1,
        updated_at: new Date().toISOString(),
      });
    }
    
    return mockData;
  }

  private async checkExistingEntity(entityType: SyncEntityType, entityId: number): Promise<any | null> {
    // Simular verificação de entidade existente
    return Math.random() > 0.8 ? { id: entityId, version: 1 } : null;
  }

  private async getServerData(entityType: SyncEntityType, entityId: number): Promise<any | null> {
    // Simular busca de dados do servidor
    return Math.random() > 0.7 ? { 
      id: entityId, 
      version: 2, 
      updated_at: new Date().toISOString() 
    } : null;
  }

  private async saveConflict(conflict: SyncConflict): Promise<void> {
    // Simular salvamento de conflito
    console.log(`Conflict saved: ${conflict.id}`);
  }

  private async getConflict(conflictId: string): Promise<SyncConflict | null> {
    // Simular busca de conflito
    return {
      id: conflictId,
      entity_type: 'orders',
      entity_id: 1,
      operation_id: 'op1',
      conflict_type: 'concurrent_update',
      server_data: { id: 1, version: 2 },
      client_data: { id: 1, version: 1 },
      resolution: 'manual',
      created_at: new Date().toISOString(),
    };
  }

  private async updateConflict(conflict: SyncConflict): Promise<void> {
    // Simular atualização de conflito
    console.log(`Conflict updated: ${conflict.id}`);
  }

  private async applyConflictResolution(conflict: SyncConflict): Promise<void> {
    // Simular aplicação da resolução
    console.log(`Conflict resolution applied: ${conflict.id}`);
  }

  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // ===== API PÚBLICA =====

  isNetworkOnline(): boolean {
    return this.isOnline;
  }

  getNetworkStatus(): NetworkStatus {
    return this.networkStatus;
  }

  async forceSyncEntity(entityType: SyncEntityType, entityId: number): Promise<void> {
    await this.startSync({
      entity_types: [entityType],
      direction: 'bidirectional',
      force_sync: true,
    });
  }
}

// Instância singleton
export const syncService = SyncService.getInstance();
