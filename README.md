# 🏪 Adib PDV - Sistema de Ponto de Venda

Sistema PDV completo e offline-first com integração fiscal, TEF e insights inteligentes.

## 🚀 Características Principais

- **Offline-First**: Funciona 100% sem internet
- **Multicanal**: Balcão, Mesas e Delivery
- **Fiscal**: NFC-e/NFe via ACBr Suite
- **TEF**: Integração com PinPads
- **KDS**: Sistema para cozinha em tempo real
- **IA**: Insights diários com GPT-4
- **Multiplataforma**: Windows, Linux, macOS

## 🏗️ Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │     Fiscal      │
│ Electron+React  │◄──►│  Node.js+API    │◄──►│ Python+ACBr     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     SQLite      │
                    │   (Database)    │
                    └─────────────────┘
```

## 📦 Stack Tecnológica

| Camada | Tecnologia |
|--------|------------|
| **Frontend** | Electron + React + TypeScript |
| **Backend** | Node.js + Express + TypeScript |
| **Fiscal** | Python + FastAPI + ACBr |
| **Database** | SQLite |
| **Comunicação** | WebSocket + REST API |
| **Build** | Webpack + Electron Builder |

## 🛠️ Instalação e Configuração

### Pré-requisitos
- Node.js 18+
- Python 3.9+
- Git

### Instalação
```bash
# Clone o repositório
git clone https://github.com/seu-usuario/adib-pdv.git
cd adib-pdv

# Instale todas as dependências
npm run setup

# Configure o banco de dados
npm run db:migrate
npm run db:seed
```

### Desenvolvimento
```bash
# Inicia todos os serviços em modo desenvolvimento
npm run dev

# Ou inicie individualmente:
npm run dev:frontend  # Frontend (Electron + React)
npm run dev:backend   # Backend (Node.js API)
npm run dev:fiscal    # Fiscal (Python FastAPI)
```

### Build e Distribuição
```bash
# Build completo
npm run build

# Gerar executável
npm run dist
```

## 📁 Estrutura do Projeto

```
adib-pdv/
├── frontend/           # Electron + React
│   ├── src/
│   │   ├── components/ # Componentes React
│   │   ├── pages/      # Páginas principais
│   │   ├── services/   # APIs e WebSocket
│   │   └── utils/      # Utilitários
│   └── public/         # Arquivos estáticos
├── backend/            # Node.js API
│   ├── src/
│   │   ├── controllers/# Controladores
│   │   ├── models/     # Modelos SQLite
│   │   ├── services/   # Lógica de negócio
│   │   ├── routes/     # Rotas da API
│   │   └── middleware/ # Middlewares
├── fiscal/             # Python + ACBr
│   ├── src/
│   │   ├── nfce/      # NFC-e
│   │   ├── nfe/       # NF-e
│   │   └── utils/     # Utilitários fiscais
├── database/           # Schemas e migrations
├── docs/              # Documentação
└── scripts/           # Scripts de build
```

## 🎯 Funcionalidades

### ✅ Implementadas
- [ ] Estrutura base do projeto
- [ ] Configuração de desenvolvimento

### 🚧 Em Desenvolvimento
- [ ] Sistema de autenticação
- [ ] Gestão de produtos
- [ ] PDV multicanal
- [ ] Integração fiscal

### 📋 Planejadas
- [ ] TEF e pagamentos
- [ ] KDS (Kitchen Display)
- [ ] Relatórios e insights
- [ ] Deploy e distribuição

## 🔧 Configuração

### Banco de Dados
O sistema usa SQLite para máxima compatibilidade e simplicidade:
- Arquivo único: `database/adib.db`
- Migrations automáticas
- Backup integrado

### Fiscal
Configuração do módulo fiscal:
- Certificado digital A1/A3
- Configuração SEFAZ por estado
- Modo homologação/produção

### TEF
Suporte a múltiplas operadoras:
- Cielo
- Rede
- Stone
- GetNet

## 📊 Licenciamento

- **Gratuito**: Até 200 pedidos/mês, 1 terminal
- **Premium**: Pedidos ilimitados, múltiplos terminais

## 🤝 Contribuição

1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para detalhes.

## 📞 Suporte

- 📧 Email: <EMAIL>
- 💬 Discord: [Comunidade Adib PDV](https://discord.gg/adibpdv)
- 📖 Docs: [docs.adibpdv.com](https://docs.adibpdv.com)

---

**Desenvolvido com ❤️ pela equipe Adib PDV**
