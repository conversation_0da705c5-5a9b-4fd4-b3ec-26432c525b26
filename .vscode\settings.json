{"typescript.preferences.importModuleSpecifier": "relative", "typescript.suggest.autoImports": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": true, "source.organizeImports": true}, "files.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/.DS_Store": true, "**/Thumbs.db": true, "**/__pycache__": true, "**/*.pyc": true}, "search.exclude": {"**/node_modules": true, "**/dist": true, "**/build": true, "**/.git": true, "**/logs": true, "**/database/backups": true}, "emmet.includeLanguages": {"typescript": "html", "typescriptreact": "html"}, "typescript.updateImportsOnFileMove.enabled": "always", "javascript.updateImportsOnFileMove.enabled": "always", "eslint.workingDirectories": ["frontend", "backend"], "python.defaultInterpreterPath": "./fiscal/venv/bin/python", "python.linting.enabled": true, "python.linting.pylintEnabled": true, "python.formatting.provider": "black", "files.associations": {"*.sql": "sql"}, "sqltools.connections": [{"name": "Adib PDV Database", "driver": "SQLite", "database": "./database/adib.db"}]}