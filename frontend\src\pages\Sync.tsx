import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>rid,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  LinearProgress,
  Switch,
  FormControlLabel,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Sync,
  CloudDone,
  CloudOff,
  Warning,
  CheckCircle,
  Error,
  Refresh,
  Storage,
  NetworkCheck,
  ExpandMore,
  PlayArrow,
  Pause,
  Stop,
  Settings,
  DataUsage,
  Timeline,
  Speed,
} from '@mui/icons-material';
import { useAuthStore } from '../services/auth';

interface SyncMetrics {
  total_entities: number;
  synced_entities: number;
  pending_entities: number;
  conflict_entities: number;
  error_entities: number;
  last_sync_duration: number;
  average_sync_time: number;
  sync_success_rate: number;
  network_status: 'online' | 'offline' | 'slow' | 'unstable';
  storage_usage: {
    total_size: number;
    offline_size: number;
    cache_size: number;
  };
}

interface SyncQueue {
  id: string;
  status: 'pending' | 'running' | 'completed' | 'failed' | 'paused';
  total_operations: number;
  completed_operations: number;
  failed_operations: number;
  started_at: string;
  completed_at?: string;
}

interface ConnectivityStatus {
  is_online: boolean;
  network_status: 'online' | 'offline' | 'slow' | 'unstable';
  last_check: string;
  sync_available: boolean;
}

const SyncPage: React.FC = () => {
  const [metrics, setMetrics] = useState<SyncMetrics | null>(null);
  const [connectivity, setConnectivity] = useState<ConnectivityStatus | null>(null);
  const [currentSync, setCurrentSync] = useState<SyncQueue | null>(null);
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true);
  const [syncDialog, setSyncDialog] = useState(false);
  const [selectedEntities, setSelectedEntities] = useState<string[]>(['orders', 'products', 'payments']);
  const [syncDirection, setSyncDirection] = useState('bidirectional');
  const [loading, setLoading] = useState(false);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular métricas de sincronização
        const mockMetrics: SyncMetrics = {
          total_entities: 1247,
          synced_entities: 1198,
          pending_entities: 35,
          conflict_entities: 8,
          error_entities: 6,
          last_sync_duration: 2.3,
          average_sync_time: 1.8,
          sync_success_rate: 96.1,
          network_status: 'online',
          storage_usage: {
            total_size: 45.2,
            offline_size: 18.7,
            cache_size: 26.5,
          },
        };

        // Simular status de conectividade
        const mockConnectivity: ConnectivityStatus = {
          is_online: true,
          network_status: 'online',
          last_check: new Date().toISOString(),
          sync_available: true,
        };

        setMetrics(mockMetrics);
        setConnectivity(mockConnectivity);
      } catch (error) {
        console.error('Error loading sync data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();

    // Atualizar dados a cada 10 segundos
    const interval = setInterval(loadData, 10000);
    return () => clearInterval(interval);
  }, []);

  // Iniciar sincronização
  const startSync = async () => {
    try {
      setLoading(true);
      
      // Simular início da sincronização
      const mockQueue: SyncQueue = {
        id: `sync_${Date.now()}`,
        status: 'running',
        total_operations: 45,
        completed_operations: 0,
        failed_operations: 0,
        started_at: new Date().toISOString(),
      };

      setCurrentSync(mockQueue);
      setSyncDialog(false);

      // Simular progresso da sincronização
      const progressInterval = setInterval(() => {
        setCurrentSync(prev => {
          if (!prev || prev.completed_operations >= prev.total_operations) {
            clearInterval(progressInterval);
            return prev ? { ...prev, status: 'completed', completed_at: new Date().toISOString() } : null;
          }
          
          return {
            ...prev,
            completed_operations: prev.completed_operations + Math.floor(Math.random() * 3) + 1,
          };
        });
      }, 1000);

      console.log(`Sync started by user ${user?.username}`);
    } catch (error) {
      console.error('Error starting sync:', error);
    } finally {
      setLoading(false);
    }
  };

  // Obter cor do status de rede
  const getNetworkStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'success';
      case 'offline': return 'error';
      case 'slow': return 'warning';
      case 'unstable': return 'warning';
      default: return 'default';
    }
  };

  // Obter ícone do status de rede
  const getNetworkStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CloudDone />;
      case 'offline': return <CloudOff />;
      case 'slow': return <Speed />;
      case 'unstable': return <NetworkCheck />;
      default: return <NetworkCheck />;
    }
  };

  // Formatar tamanho em MB
  const formatSize = (sizeInMB: number): string => {
    return `${sizeInMB.toFixed(1)} MB`;
  };

  // Calcular progresso da sincronização
  const getSyncProgress = (): number => {
    if (!currentSync || currentSync.total_operations === 0) return 0;
    return (currentSync.completed_operations / currentSync.total_operations) * 100;
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              Sincronização Offline-First
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end', alignItems: 'center' }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={autoSyncEnabled}
                    onChange={(e) => setAutoSyncEnabled(e.target.checked)}
                  />
                }
                label="Auto Sync"
              />
              <Button
                variant="contained"
                startIcon={<Sync />}
                onClick={() => setSyncDialog(true)}
                disabled={loading || currentSync?.status === 'running'}
              >
                Sincronizar
              </Button>
              <IconButton>
                <Refresh />
              </IconButton>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Status de Conectividade */}
      {connectivity && (
        <Paper sx={{ m: 2, p: 2, bgcolor: connectivity.is_online ? 'success.light' : 'error.light' }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {getNetworkStatusIcon(connectivity.network_status)}
                <Typography variant="h6">
                  Status: {connectivity.is_online ? 'Online' : 'Offline'}
                </Typography>
                <Chip
                  label={connectivity.network_status}
                  color={getNetworkStatusColor(connectivity.network_status) as any}
                  size="small"
                />
              </Box>
              <Typography variant="body2">
                Última verificação: {new Date(connectivity.last_check).toLocaleTimeString('pt-BR')}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'flex-end' }}>
                {connectivity.sync_available ? (
                  <Chip icon={<CheckCircle />} label="Sync Disponível" color="success" />
                ) : (
                  <Chip icon={<Error />} label="Sync Indisponível" color="error" />
                )}
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Sincronização em Andamento */}
      {currentSync && currentSync.status === 'running' && (
        <Paper sx={{ m: 2, p: 2, bgcolor: 'info.light' }}>
          <Typography variant="h6" gutterBottom>
            Sincronização em Andamento
          </Typography>
          <LinearProgress 
            variant="determinate" 
            value={getSyncProgress()} 
            sx={{ mb: 1 }}
          />
          <Typography variant="body2">
            {currentSync.completed_operations} de {currentSync.total_operations} operações concluídas
            ({getSyncProgress().toFixed(1)}%)
          </Typography>
        </Paper>
      )}

      <Box sx={{ flexGrow: 1, overflow: 'auto', p: 2 }}>
        <Grid container spacing={3}>
          {/* Métricas de Sincronização */}
          {metrics && (
            <>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <DataUsage sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h4" color="primary">
                      {metrics.total_entities}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Total de Entidades
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <CheckCircle sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {metrics.synced_entities}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Sincronizadas
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Warning sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {metrics.pending_entities}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pendentes
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Error sx={{ fontSize: 40, color: 'error.main', mb: 1 }} />
                    <Typography variant="h4" color="error.main">
                      {metrics.conflict_entities}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Conflitos
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </>
          )}

          {/* Informações Detalhadas */}
          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Performance de Sincronização
                </Typography>
                {metrics && (
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <Timeline />
                      </ListItemIcon>
                      <ListItemText
                        primary="Última Sincronização"
                        secondary={`${metrics.last_sync_duration}s`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <Speed />
                      </ListItemIcon>
                      <ListItemText
                        primary="Tempo Médio"
                        secondary={`${metrics.average_sync_time}s`}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CheckCircle />
                      </ListItemIcon>
                      <ListItemText
                        primary="Taxa de Sucesso"
                        secondary={`${metrics.sync_success_rate}%`}
                      />
                    </ListItem>
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          <Grid item xs={12} md={6}>
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Uso de Armazenamento
                </Typography>
                {metrics && (
                  <List>
                    <ListItem>
                      <ListItemIcon>
                        <Storage />
                      </ListItemIcon>
                      <ListItemText
                        primary="Total"
                        secondary={formatSize(metrics.storage_usage.total_size)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <CloudOff />
                      </ListItemIcon>
                      <ListItemText
                        primary="Dados Offline"
                        secondary={formatSize(metrics.storage_usage.offline_size)}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon>
                        <DataUsage />
                      </ListItemIcon>
                      <ListItemText
                        primary="Cache"
                        secondary={formatSize(metrics.storage_usage.cache_size)}
                      />
                    </ListItem>
                  </List>
                )}
              </CardContent>
            </Card>
          </Grid>

          {/* Configurações de Sincronização */}
          <Grid item xs={12}>
            <Accordion>
              <AccordionSummary expandIcon={<ExpandMore />}>
                <Settings sx={{ mr: 2 }} />
                <Typography variant="h6">Configurações de Sincronização</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={<Switch checked={autoSyncEnabled} onChange={(e) => setAutoSyncEnabled(e.target.checked)} />}
                      label="Sincronização Automática"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={<Switch defaultChecked />}
                      label="Sincronizar ao Restaurar Conexão"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={<Switch defaultChecked />}
                      label="Notificações de Conflito"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <FormControlLabel
                      control={<Switch defaultChecked />}
                      label="Compressão de Dados"
                    />
                  </Grid>
                </Grid>
              </AccordionDetails>
            </Accordion>
          </Grid>
        </Grid>
      </Box>

      {/* Dialog de Sincronização */}
      <Dialog open={syncDialog} onClose={() => setSyncDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Configurar Sincronização
        </DialogTitle>
        <DialogContent>
          <Box sx={{ mt: 2 }}>
            <Typography variant="h6" gutterBottom>
              Entidades para Sincronizar
            </Typography>
            <Grid container spacing={1}>
              {['orders', 'products', 'payments', 'users', 'kitchen_orders'].map((entity) => (
                <Grid item key={entity}>
                  <Chip
                    label={entity}
                    color={selectedEntities.includes(entity) ? 'primary' : 'default'}
                    onClick={() => {
                      if (selectedEntities.includes(entity)) {
                        setSelectedEntities(selectedEntities.filter(e => e !== entity));
                      } else {
                        setSelectedEntities([...selectedEntities, entity]);
                      }
                    }}
                    variant={selectedEntities.includes(entity) ? 'filled' : 'outlined'}
                  />
                </Grid>
              ))}
            </Grid>

            <FormControl fullWidth sx={{ mt: 3 }}>
              <InputLabel>Direção da Sincronização</InputLabel>
              <Select
                value={syncDirection}
                onChange={(e) => setSyncDirection(e.target.value)}
                label="Direção da Sincronização"
              >
                <MenuItem value="up">Upload (Local → Servidor)</MenuItem>
                <MenuItem value="down">Download (Servidor → Local)</MenuItem>
                <MenuItem value="bidirectional">Bidirecional</MenuItem>
              </Select>
            </FormControl>

            <Alert severity="info" sx={{ mt: 2 }}>
              A sincronização irá processar {selectedEntities.length} tipo(s) de entidade.
              Tempo estimado: ~{selectedEntities.length * 0.5} segundos.
            </Alert>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setSyncDialog(false)}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            onClick={startSync}
            disabled={selectedEntities.length === 0}
            startIcon={<PlayArrow />}
          >
            Iniciar Sincronização
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SyncPage;
