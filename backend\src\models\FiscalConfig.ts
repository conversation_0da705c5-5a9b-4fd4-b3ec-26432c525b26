import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  FiscalConfig, 
  CreateFiscalConfigRequest, 
  UpdateFiscalConfigRequest,
  FiscalValidation
} from '../types/fiscal';

export class FiscalConfigModel {
  // Buscar configuração fiscal por ID
  static async findById(id: number): Promise<FiscalConfig | null> {
    const config = await executeQuerySingle<FiscalConfig>(
      'SELECT * FROM fiscal_configs WHERE id = ?',
      [id]
    );
    return config || null;
  }

  // Buscar configuração ativa
  static async findActive(): Promise<FiscalConfig | null> {
    const config = await executeQuerySingle<FiscalConfig>(
      'SELECT * FROM fiscal_configs WHERE is_active = 1 ORDER BY created_at DESC LIMIT 1'
    );
    return config || null;
  }

  // Listar todas as configurações
  static async findAll(activeOnly: boolean = true): Promise<FiscalConfig[]> {
    let query = 'SELECT * FROM fiscal_configs';
    const params: any[] = [];

    if (activeOnly) {
      query += ' WHERE is_active = 1';
    }

    query += ' ORDER BY created_at DESC';

    return await executeQuery<FiscalConfig>(query, params);
  }

  // Criar configuração fiscal
  static async create(configData: CreateFiscalConfigRequest): Promise<FiscalConfig> {
    // Desativar configurações existentes
    await executeUpdate(
      'UPDATE fiscal_configs SET is_active = 0, updated_at = CURRENT_TIMESTAMP'
    );

    // Inserir nova configuração
    const result = await executeUpdate(
      `INSERT INTO fiscal_configs (
        company_name, company_document, company_ie, company_address, 
        company_city, company_state, company_zip, company_phone, 
        company_email, environment, nfce_series, nfe_series, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [
        configData.company_name,
        configData.company_document,
        configData.company_ie,
        configData.company_address,
        configData.company_city,
        configData.company_state,
        configData.company_zip,
        configData.company_phone || null,
        configData.company_email || null,
        configData.environment || 'homologation',
        configData.nfce_series || 1,
        configData.nfe_series || 1,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create fiscal config');
    }

    // Retornar configuração criada
    const newConfig = await this.findById(result.lastID);
    if (!newConfig) {
      throw new Error('Failed to retrieve created fiscal config');
    }

    return newConfig;
  }

  // Atualizar configuração fiscal
  static async update(id: number, configData: UpdateFiscalConfigRequest): Promise<FiscalConfig> {
    const config = await this.findById(id);
    if (!config) {
      throw new Error('Fiscal config not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = [
      'company_name', 'company_document', 'company_ie', 'company_address',
      'company_city', 'company_state', 'company_zip', 'company_phone',
      'company_email', 'certificate_path', 'certificate_password',
      'environment', 'nfce_series', 'nfe_series', 'csc_id', 'csc_token',
      'is_active'
    ];

    updatableFields.forEach(field => {
      if (configData[field as keyof UpdateFiscalConfigRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = configData[field as keyof UpdateFiscalConfigRequest];
        
        // Converter boolean para integer
        if (field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return config; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE fiscal_configs SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Se esta configuração foi ativada, desativar as outras
    if (configData.is_active) {
      await executeUpdate(
        'UPDATE fiscal_configs SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id != ?',
        [id]
      );
    }

    // Retornar configuração atualizada
    const updatedConfig = await this.findById(id);
    if (!updatedConfig) {
      throw new Error('Failed to retrieve updated fiscal config');
    }

    return updatedConfig;
  }

  // Deletar configuração (soft delete)
  static async delete(id: number): Promise<void> {
    const config = await this.findById(id);
    if (!config) {
      throw new Error('Fiscal config not found');
    }

    // Verificar se há documentos usando esta configuração
    const documentsCount = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM fiscal_documents WHERE created_at >= ? AND created_at <= ?',
      [config.created_at, config.updated_at]
    );

    if (documentsCount && documentsCount.count > 0) {
      throw new Error('Cannot delete config with associated documents');
    }

    await executeUpdate(
      'UPDATE fiscal_configs SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Validar configuração fiscal
  static async validate(id?: number): Promise<FiscalValidation> {
    const config = id ? await this.findById(id) : await this.findActive();
    
    if (!config) {
      return {
        valid: false,
        errors: ['No fiscal configuration found'],
        warnings: [],
      };
    }

    const errors: string[] = [];
    const warnings: string[] = [];

    // Validações obrigatórias
    if (!config.company_name) {
      errors.push('Company name is required');
    }

    if (!config.company_document) {
      errors.push('Company document (CNPJ) is required');
    }

    if (!config.company_ie) {
      errors.push('State registration (IE) is required');
    }

    if (!config.company_address) {
      errors.push('Company address is required');
    }

    if (!config.company_city) {
      errors.push('Company city is required');
    }

    if (!config.company_state) {
      errors.push('Company state is required');
    }

    if (!config.company_zip) {
      errors.push('Company ZIP code is required');
    }

    // Validações de certificado
    let certificateInfo;
    if (config.certificate_path) {
      try {
        // Simular validação de certificado
        certificateInfo = {
          valid: true,
          expires_at: '2025-12-31T23:59:59Z',
          days_to_expire: 365,
          subject: 'CN=Empresa Teste',
        };

        if (certificateInfo.days_to_expire <= 30) {
          warnings.push(`Certificate expires in ${certificateInfo.days_to_expire} days`);
        }
      } catch (error) {
        errors.push('Invalid certificate file');
      }
    } else {
      warnings.push('No certificate configured');
    }

    // Validações de ambiente
    if (config.environment === 'production') {
      if (!config.certificate_path) {
        errors.push('Certificate is required for production environment');
      }
      if (!config.csc_id || !config.csc_token) {
        warnings.push('CSC (Código de Segurança do Contribuinte) not configured');
      }
    }

    // Status do SEFAZ (simulado)
    const sefazStatus = {
      online: true,
      last_check: new Date().toISOString(),
      response_time: 1.2,
    };

    return {
      valid: errors.length === 0,
      errors,
      warnings,
      certificate_info: certificateInfo,
      sefaz_status: sefazStatus,
    };
  }

  // Testar conexão com SEFAZ
  static async testSefazConnection(id?: number): Promise<{
    success: boolean;
    response_time: number;
    status_code?: number;
    message?: string;
  }> {
    const config = id ? await this.findById(id) : await this.findActive();
    
    if (!config) {
      return {
        success: false,
        response_time: 0,
        message: 'No fiscal configuration found',
      };
    }

    try {
      // Simular teste de conexão
      const startTime = Date.now();
      
      // Aqui seria feita a chamada real para o SEFAZ
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const responseTime = (Date.now() - startTime) / 1000;

      return {
        success: true,
        response_time: responseTime,
        status_code: 200,
        message: 'Connection successful',
      };
    } catch (error) {
      return {
        success: false,
        response_time: 0,
        message: error instanceof Error ? error.message : 'Connection failed',
      };
    }
  }

  // Obter configuração para emissão
  static async getForEmission(): Promise<FiscalConfig> {
    const config = await this.findActive();
    if (!config) {
      throw new Error('No active fiscal configuration found');
    }

    const validation = await this.validate(config.id);
    if (!validation.valid) {
      throw new Error(`Invalid fiscal configuration: ${validation.errors.join(', ')}`);
    }

    return config;
  }

  // Verificar se configuração pode ser deletada
  static async canDelete(id: number): Promise<boolean> {
    const documentsCount = await executeQuerySingle<{ count: number }>(
      `SELECT COUNT(*) as count 
       FROM fiscal_documents fd
       JOIN fiscal_configs fc ON DATE(fd.created_at) >= DATE(fc.created_at) 
         AND DATE(fd.created_at) <= DATE(fc.updated_at)
       WHERE fc.id = ?`,
      [id]
    );

    return (documentsCount?.count || 0) === 0;
  }

  // Backup da configuração
  static async backup(): Promise<{
    config: FiscalConfig;
    backup_date: string;
    file_path: string;
  }> {
    const config = await this.findActive();
    if (!config) {
      throw new Error('No active fiscal configuration found');
    }

    // Simular backup
    const backupDate = new Date().toISOString();
    const filePath = `/backups/fiscal_config_${backupDate.slice(0, 10)}.json`;

    return {
      config,
      backup_date: backupDate,
      file_path: filePath,
    };
  }
}
