import { 
  PinPadRequest, 
  PinPadResponse, 
  PinPadCommand, 
  PinPadStatus,
  CardType,
  TefOperation 
} from '../types/tef';

/**
 * Simulador de PinPad para testes e demonstração
 * Simula as operações de um PinPad real sem hardware
 */
export class PinPadSimulator {
  private static instance: PinPadSimulator;
  private status: PinPadStatus = 'ready';
  private isConnected: boolean = true;
  private processingTimeout?: NodeJS.Timeout;

  private constructor() {}

  static getInstance(): PinPadSimulator {
    if (!PinPadSimulator.instance) {
      PinPadSimulator.instance = new PinPadSimulator();
    }
    return PinPadSimulator.instance;
  }

  // Simular conexão com PinPad
  async connect(): Promise<boolean> {
    try {
      // Simular tempo de conexão
      await this.delay(1000);
      this.isConnected = true;
      this.status = 'ready';
      console.log('PinPad Simulator: Connected');
      return true;
    } catch (error) {
      this.isConnected = false;
      this.status = 'offline';
      console.error('PinPad Simulator: Connection failed', error);
      return false;
    }
  }

  // Simular desconexão
  async disconnect(): Promise<void> {
    this.isConnected = false;
    this.status = 'offline';
    if (this.processingTimeout) {
      clearTimeout(this.processingTimeout);
    }
    console.log('PinPad Simulator: Disconnected');
  }

  // Verificar status da conexão
  isReady(): boolean {
    return this.isConnected && this.status === 'ready';
  }

  // Obter status atual
  getStatus(): PinPadStatus {
    return this.status;
  }

  // Processar comando do PinPad
  async processCommand(request: PinPadRequest): Promise<PinPadResponse> {
    if (!this.isConnected) {
      return this.createErrorResponse(request.command, 'PinPad not connected');
    }

    if (this.status === 'processing') {
      return this.createErrorResponse(request.command, 'PinPad busy');
    }

    try {
      this.status = 'processing';

      switch (request.command) {
        case 'sale_credit':
          return await this.processCreditSale(request);
        case 'sale_debit':
          return await this.processDebitSale(request);
        case 'cancel':
          return await this.processCancel(request);
        case 'administrative':
          return await this.processAdministrative(request);
        case 'reprint':
          return await this.processReprint(request);
        case 'status':
          return await this.processStatus(request);
        case 'initialize':
          return await this.processInitialize(request);
        case 'close':
          return await this.processClose(request);
        default:
          return this.createErrorResponse(request.command, 'Unknown command');
      }
    } catch (error) {
      this.status = 'ready';
      return this.createErrorResponse(request.command, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  // Processar venda no crédito
  private async processCreditSale(request: PinPadRequest): Promise<PinPadResponse> {
    if (!request.amount || request.amount <= 0) {
      this.status = 'ready';
      return this.createErrorResponse(request.command, 'Invalid amount');
    }

    // Simular inserção do cartão
    this.status = 'waiting_card';
    await this.delay(2000);

    // Simular digitação da senha
    this.status = 'waiting_password';
    await this.delay(3000);

    // Simular processamento
    this.status = 'processing';
    await this.delay(2000);

    // Simular aprovação (95% de chance)
    const approved = Math.random() > 0.05;

    this.status = 'ready';

    if (approved) {
      return {
        success: true,
        command: request.command,
        status: 'approved',
        data: {
          amount: request.amount,
          installments: request.installments || 1,
        },
        card_type: 'credit',
        card_brand: this.getRandomCardBrand(),
        card_number_masked: this.generateMaskedCardNumber(),
        authorization_code: this.generateAuthCode(),
        nsu: this.generateNSU(),
        transaction_id: this.generateTransactionId(),
        receipt_customer: this.generateCustomerReceipt(request.amount!, 'credit', request.installments),
        receipt_merchant: this.generateMerchantReceipt(request.amount!, 'credit', request.installments),
        timestamp: new Date().toISOString(),
      };
    } else {
      return {
        success: false,
        command: request.command,
        status: 'denied',
        error: 'Transaction denied by issuer',
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Processar venda no débito
  private async processDebitSale(request: PinPadRequest): Promise<PinPadResponse> {
    if (!request.amount || request.amount <= 0) {
      this.status = 'ready';
      return this.createErrorResponse(request.command, 'Invalid amount');
    }

    // Simular inserção do cartão
    this.status = 'waiting_card';
    await this.delay(1500);

    // Simular digitação da senha
    this.status = 'waiting_password';
    await this.delay(2500);

    // Simular processamento
    this.status = 'processing';
    await this.delay(1500);

    // Simular aprovação (97% de chance para débito)
    const approved = Math.random() > 0.03;

    this.status = 'ready';

    if (approved) {
      return {
        success: true,
        command: request.command,
        status: 'approved',
        data: {
          amount: request.amount,
          installments: 1,
        },
        card_type: 'debit',
        card_brand: this.getRandomCardBrand(),
        card_number_masked: this.generateMaskedCardNumber(),
        authorization_code: this.generateAuthCode(),
        nsu: this.generateNSU(),
        transaction_id: this.generateTransactionId(),
        receipt_customer: this.generateCustomerReceipt(request.amount!, 'debit'),
        receipt_merchant: this.generateMerchantReceipt(request.amount!, 'debit'),
        timestamp: new Date().toISOString(),
      };
    } else {
      return {
        success: false,
        command: request.command,
        status: 'denied',
        error: 'Insufficient funds',
        timestamp: new Date().toISOString(),
      };
    }
  }

  // Processar cancelamento
  private async processCancel(request: PinPadRequest): Promise<PinPadResponse> {
    this.status = 'processing';
    await this.delay(2000);

    this.status = 'ready';

    return {
      success: true,
      command: request.command,
      status: 'approved',
      data: {
        original_transaction_id: request.transaction_id,
      },
      authorization_code: this.generateAuthCode(),
      nsu: this.generateNSU(),
      transaction_id: this.generateTransactionId(),
      receipt_customer: this.generateCancelReceipt(),
      receipt_merchant: this.generateCancelReceipt(),
      timestamp: new Date().toISOString(),
    };
  }

  // Processar função administrativa
  private async processAdministrative(request: PinPadRequest): Promise<PinPadResponse> {
    this.status = 'processing';
    await this.delay(1000);

    this.status = 'ready';

    return {
      success: true,
      command: request.command,
      status: 'approved',
      data: {
        menu_option: 'administrative_report',
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Processar reimpressão
  private async processReprint(request: PinPadRequest): Promise<PinPadResponse> {
    this.status = 'processing';
    await this.delay(500);

    this.status = 'ready';

    return {
      success: true,
      command: request.command,
      status: 'approved',
      data: {
        transaction_id: request.transaction_id,
      },
      receipt_customer: 'REIMPRESSÃO\n' + this.generateCustomerReceipt(50.00, 'credit'),
      receipt_merchant: 'REIMPRESSÃO\n' + this.generateMerchantReceipt(50.00, 'credit'),
      timestamp: new Date().toISOString(),
    };
  }

  // Processar status
  private async processStatus(request: PinPadRequest): Promise<PinPadResponse> {
    return {
      success: true,
      command: request.command,
      status: this.status,
      data: {
        model: 'Ingenico iWL250 Simulator',
        version: '1.0.0',
        serial: 'SIM123456789',
        battery: 85,
        signal: 4,
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Processar inicialização
  private async processInitialize(request: PinPadRequest): Promise<PinPadResponse> {
    this.status = 'processing';
    await this.delay(3000);

    this.status = 'ready';

    return {
      success: true,
      command: request.command,
      status: 'ready',
      data: {
        initialized: true,
        model: 'Ingenico iWL250 Simulator',
        version: '1.0.0',
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Processar fechamento
  private async processClose(request: PinPadRequest): Promise<PinPadResponse> {
    this.status = 'processing';
    await this.delay(1000);

    await this.disconnect();

    return {
      success: true,
      command: request.command,
      status: 'offline',
      data: {
        closed: true,
      },
      timestamp: new Date().toISOString(),
    };
  }

  // Criar resposta de erro
  private createErrorResponse(command: PinPadCommand, error: string): PinPadResponse {
    return {
      success: false,
      command,
      status: 'error',
      error,
      timestamp: new Date().toISOString(),
    };
  }

  // Utilitários para gerar dados simulados
  private getRandomCardBrand(): string {
    const brands = ['Visa', 'Mastercard', 'Elo', 'American Express'];
    return brands[Math.floor(Math.random() * brands.length)];
  }

  private generateMaskedCardNumber(): string {
    const lastFour = Math.floor(1000 + Math.random() * 9000);
    return `****${lastFour}`;
  }

  private generateAuthCode(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  private generateNSU(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private generateTransactionId(): string {
    return Date.now().toString() + Math.floor(Math.random() * 1000);
  }

  private generateCustomerReceipt(amount: number, operation: TefOperation, installments?: number): string {
    const date = new Date().toLocaleString('pt-BR');
    const installmentText = installments && installments > 1 ? ` ${installments}x` : '';
    
    return `
=== VIA DO CLIENTE ===
RESTAURANTE ADIB LTDA
CNPJ: 12.345.678/0001-95

${operation.toUpperCase()}${installmentText}
Valor: R$ ${amount.toFixed(2)}
Data: ${date}
NSU: ${this.generateNSU()}
Autorização: ${this.generateAuthCode()}

Obrigado pela preferência!
=======================
    `.trim();
  }

  private generateMerchantReceipt(amount: number, operation: TefOperation, installments?: number): string {
    const date = new Date().toLocaleString('pt-BR');
    const installmentText = installments && installments > 1 ? ` ${installments}x` : '';
    
    return `
=== VIA DO ESTABELECIMENTO ===
RESTAURANTE ADIB LTDA
CNPJ: 12.345.678/0001-95

${operation.toUpperCase()}${installmentText}
Valor: R$ ${amount.toFixed(2)}
Data: ${date}
NSU: ${this.generateNSU()}
Autorização: ${this.generateAuthCode()}
Terminal: T1234567

TRANSAÇÃO APROVADA
============================
    `.trim();
  }

  private generateCancelReceipt(): string {
    const date = new Date().toLocaleString('pt-BR');
    
    return `
=== CANCELAMENTO ===
RESTAURANTE ADIB LTDA
CNPJ: 12.345.678/0001-95

CANCELAMENTO DE TRANSAÇÃO
Data: ${date}
NSU: ${this.generateNSU()}
Autorização: ${this.generateAuthCode()}

CANCELAMENTO APROVADO
===================
    `.trim();
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Instância singleton
export const pinPadSimulator = PinPadSimulator.getInstance();
