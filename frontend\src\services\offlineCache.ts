/**
 * Serviço de Cache Offline
 * Gerencia armazenamento local e sincronização de dados
 */

interface CacheEntry {
  data: any;
  timestamp: number;
  version: number;
  isDirty: boolean;
  isDeleted: boolean;
}

interface SyncOperation {
  id: string;
  entityType: string;
  entityId: number;
  operation: 'create' | 'update' | 'delete';
  data: any;
  timestamp: number;
  retryCount: number;
}

class OfflineCacheService {
  private static instance: OfflineCacheService;
  private isOnline: boolean = navigator.onLine;
  private syncQueue: SyncOperation[] = [];
  private syncInProgress: boolean = false;

  private constructor() {
    this.initializeNetworkListeners();
    this.loadSyncQueue();
    this.startPeriodicSync();
  }

  static getInstance(): OfflineCacheService {
    if (!OfflineCacheService.instance) {
      OfflineCacheService.instance = new OfflineCacheService();
    }
    return OfflineCacheService.instance;
  }

  // ===== GERENCIAMENTO DE REDE =====

  private initializeNetworkListeners(): void {
    window.addEventListener('online', () => {
      console.log('Network restored - starting sync');
      this.isOnline = true;
      this.syncPendingOperations();
    });

    window.addEventListener('offline', () => {
      console.log('Network lost - switching to offline mode');
      this.isOnline = false;
    });
  }

  isNetworkOnline(): boolean {
    return this.isOnline;
  }

  // ===== CACHE DE DADOS =====

  async set(key: string, data: any, entityType?: string, entityId?: number): Promise<void> {
    const entry: CacheEntry = {
      data,
      timestamp: Date.now(),
      version: 1,
      isDirty: !this.isOnline,
      isDeleted: false,
    };

    try {
      localStorage.setItem(`cache_${key}`, JSON.stringify(entry));
      
      // Se offline, adicionar à fila de sincronização
      if (!this.isOnline && entityType && entityId) {
        this.addToSyncQueue({
          id: `${entityType}_${entityId}_${Date.now()}`,
          entityType,
          entityId,
          operation: 'update',
          data,
          timestamp: Date.now(),
          retryCount: 0,
        });
      }
    } catch (error) {
      console.error('Error saving to cache:', error);
    }
  }

  async get(key: string): Promise<any | null> {
    try {
      const cached = localStorage.getItem(`cache_${key}`);
      if (!cached) return null;

      const entry: CacheEntry = JSON.parse(cached);
      
      // Verificar se não expirou (24 horas)
      const maxAge = 24 * 60 * 60 * 1000;
      if (Date.now() - entry.timestamp > maxAge) {
        this.remove(key);
        return null;
      }

      return entry.data;
    } catch (error) {
      console.error('Error reading from cache:', error);
      return null;
    }
  }

  async remove(key: string): Promise<void> {
    try {
      localStorage.removeItem(`cache_${key}`);
    } catch (error) {
      console.error('Error removing from cache:', error);
    }
  }

  async clear(): Promise<void> {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'));
      keys.forEach(key => localStorage.removeItem(key));
    } catch (error) {
      console.error('Error clearing cache:', error);
    }
  }

  // ===== OPERAÇÕES OFFLINE =====

  async saveOfflineOperation(entityType: string, entityId: number, operation: 'create' | 'update' | 'delete', data: any): Promise<void> {
    const syncOp: SyncOperation = {
      id: `${entityType}_${entityId}_${Date.now()}`,
      entityType,
      entityId,
      operation,
      data,
      timestamp: Date.now(),
      retryCount: 0,
    };

    this.addToSyncQueue(syncOp);
    
    // Salvar também no cache local
    await this.set(`${entityType}_${entityId}`, data, entityType, entityId);
  }

  private addToSyncQueue(operation: SyncOperation): void {
    this.syncQueue.push(operation);
    this.saveSyncQueue();
  }

  private saveSyncQueue(): void {
    try {
      localStorage.setItem('sync_queue', JSON.stringify(this.syncQueue));
    } catch (error) {
      console.error('Error saving sync queue:', error);
    }
  }

  private loadSyncQueue(): void {
    try {
      const saved = localStorage.getItem('sync_queue');
      if (saved) {
        this.syncQueue = JSON.parse(saved);
      }
    } catch (error) {
      console.error('Error loading sync queue:', error);
      this.syncQueue = [];
    }
  }

  // ===== SINCRONIZAÇÃO =====

  async syncPendingOperations(): Promise<void> {
    if (!this.isOnline || this.syncInProgress || this.syncQueue.length === 0) {
      return;
    }

    this.syncInProgress = true;
    console.log(`Starting sync of ${this.syncQueue.length} operations`);

    const operations = [...this.syncQueue];
    const successfulOps: string[] = [];

    for (const operation of operations) {
      try {
        await this.syncOperation(operation);
        successfulOps.push(operation.id);
        console.log(`Synced operation: ${operation.id}`);
      } catch (error) {
        console.error(`Failed to sync operation ${operation.id}:`, error);
        operation.retryCount++;
        
        // Remover operações que falharam muitas vezes
        if (operation.retryCount >= 3) {
          successfulOps.push(operation.id);
          console.warn(`Removing failed operation after 3 retries: ${operation.id}`);
        }
      }
    }

    // Remover operações bem-sucedidas da fila
    this.syncQueue = this.syncQueue.filter(op => !successfulOps.includes(op.id));
    this.saveSyncQueue();

    this.syncInProgress = false;
    console.log(`Sync completed. ${successfulOps.length} operations processed.`);
  }

  private async syncOperation(operation: SyncOperation): Promise<void> {
    const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';
    const token = localStorage.getItem('auth_token');

    if (!token) {
      throw new Error('No auth token available');
    }

    const headers = {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json',
    };

    let url: string;
    let method: string;
    let body: any;

    switch (operation.operation) {
      case 'create':
        url = `${baseUrl}/${operation.entityType}`;
        method = 'POST';
        body = operation.data;
        break;
      case 'update':
        url = `${baseUrl}/${operation.entityType}/${operation.entityId}`;
        method = 'PUT';
        body = operation.data;
        break;
      case 'delete':
        url = `${baseUrl}/${operation.entityType}/${operation.entityId}`;
        method = 'DELETE';
        body = undefined;
        break;
      default:
        throw new Error(`Unknown operation: ${operation.operation}`);
    }

    const response = await fetch(url, {
      method,
      headers,
      body: body ? JSON.stringify(body) : undefined,
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  }

  private startPeriodicSync(): void {
    // Tentar sincronizar a cada 30 segundos
    setInterval(() => {
      if (this.isOnline) {
        this.syncPendingOperations();
      }
    }, 30000);
  }

  // ===== MÉTRICAS =====

  getCacheStats(): any {
    const cacheKeys = Object.keys(localStorage).filter(key => key.startsWith('cache_'));
    const totalSize = cacheKeys.reduce((size, key) => {
      return size + (localStorage.getItem(key)?.length || 0);
    }, 0);

    return {
      totalEntries: cacheKeys.length,
      totalSizeBytes: totalSize,
      totalSizeKB: Math.round(totalSize / 1024),
      pendingSync: this.syncQueue.length,
      isOnline: this.isOnline,
      syncInProgress: this.syncInProgress,
    };
  }

  getPendingOperations(): SyncOperation[] {
    return [...this.syncQueue];
  }

  // ===== UTILITÁRIOS =====

  async preloadData(entityType: string, data: any[]): Promise<void> {
    for (const item of data) {
      await this.set(`${entityType}_${item.id}`, item);
    }
  }

  async getOfflineData(entityType: string): Promise<any[]> {
    const keys = Object.keys(localStorage)
      .filter(key => key.startsWith(`cache_${entityType}_`));
    
    const data = [];
    for (const key of keys) {
      const item = await this.get(key.replace('cache_', ''));
      if (item) {
        data.push(item);
      }
    }
    
    return data;
  }

  // Forçar sincronização imediata
  async forcSync(): Promise<void> {
    if (this.isOnline) {
      await this.syncPendingOperations();
    } else {
      throw new Error('Cannot sync while offline');
    }
  }

  // Limpar dados antigos
  async cleanup(maxAgeHours: number = 24): Promise<number> {
    const keys = Object.keys(localStorage).filter(key => key.startsWith('cache_'));
    const maxAge = maxAgeHours * 60 * 60 * 1000;
    let cleaned = 0;

    for (const key of keys) {
      try {
        const cached = localStorage.getItem(key);
        if (cached) {
          const entry: CacheEntry = JSON.parse(cached);
          if (Date.now() - entry.timestamp > maxAge) {
            localStorage.removeItem(key);
            cleaned++;
          }
        }
      } catch (error) {
        // Remove entradas corrompidas
        localStorage.removeItem(key);
        cleaned++;
      }
    }

    return cleaned;
  }
}

// Instância singleton
export const offlineCache = OfflineCacheService.getInstance();

// Hook para usar o cache offline
export const useOfflineCache = () => {
  return {
    cache: offlineCache,
    isOnline: offlineCache.isNetworkOnline(),
    stats: offlineCache.getCacheStats(),
    pendingOps: offlineCache.getPendingOperations(),
  };
};
