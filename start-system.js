#!/usr/bin/env node

/**
 * Script simplificado para iniciar o Sistema PDV Adib
 * Inicia apenas o backend com as funcionalidades essenciais
 */

const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs');

// Configuração básica
const app = express();
const PORT = process.env.PORT || 3001;

// Middleware básico
app.use(cors());
app.use(express.json());
app.use(express.static('frontend/dist'));

// Logs simples
app.use((req, res, next) => {
  console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Rotas básicas
app.get('/api/health', (req, res) => {
  res.json({
    success: true,
    message: 'Sistema PDV Adib funcionando!',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    status: 'online'
  });
});

app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    data: {
      system: 'PDV Adib',
      version: '1.0.0',
      environment: 'development',
      database: 'SQLite',
      features: [
        'PDV (Ponto de Venda)',
        'Sistema de Cozinha (KDS)',
        'Gestão de Produtos',
        'Relatórios',
        'TEF',
        'Offline-First',
        'Sincronização'
      ],
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      timestamp: new Date().toISOString()
    }
  });
});

// Rota de login simplificada
app.post('/api/auth/login', (req, res) => {
  const { username, password } = req.body;
  
  // Credenciais padrão para teste
  const users = {
    'admin': { password: 'password', role: 'manager', name: 'Administrador' },
    'caixa': { password: 'password', role: 'cashier', name: 'Operador de Caixa' },
    'cozinha': { password: 'password', role: 'kitchen', name: 'Cozinha' }
  };
  
  const user = users[username];
  
  if (user && user.password === password) {
    res.json({
      success: true,
      data: {
        token: 'demo-token-' + Date.now(),
        user: {
          id: Math.floor(Math.random() * 1000),
          username,
          name: user.name,
          role: user.role
        }
      },
      message: 'Login realizado com sucesso!'
    });
  } else {
    res.status(401).json({
      success: false,
      error: 'Credenciais inválidas',
      message: 'Usuário ou senha incorretos'
    });
  }
});

// Rota para produtos (mock)
app.get('/api/products', (req, res) => {
  const products = [
    {
      id: 1,
      name: 'Hambúrguer Clássico',
      description: 'Hambúrguer com carne, queijo, alface e tomate',
      price: 25.90,
      category: 'Hambúrgueres',
      image: '/images/hamburger.jpg',
      active: true
    },
    {
      id: 2,
      name: 'Pizza Margherita',
      description: 'Pizza com molho de tomate, mussarela e manjericão',
      price: 35.00,
      category: 'Pizzas',
      image: '/images/pizza.jpg',
      active: true
    },
    {
      id: 3,
      name: 'Refrigerante Lata',
      description: 'Refrigerante gelado 350ml',
      price: 5.50,
      category: 'Bebidas',
      image: '/images/soda.jpg',
      active: true
    }
  ];
  
  res.json({
    success: true,
    data: products,
    total: products.length
  });
});

// Rota para pedidos (mock)
app.get('/api/orders', (req, res) => {
  const orders = [
    {
      id: 1,
      number: '001',
      status: 'pending',
      items: [
        { id: 1, name: 'Hambúrguer Clássico', quantity: 2, price: 25.90 },
        { id: 3, name: 'Refrigerante Lata', quantity: 2, price: 5.50 }
      ],
      total: 62.80,
      createdAt: new Date().toISOString()
    }
  ];
  
  res.json({
    success: true,
    data: orders,
    total: orders.length
  });
});

// Rota para criar pedido
app.post('/api/orders', (req, res) => {
  const { items, total } = req.body;
  
  const order = {
    id: Math.floor(Math.random() * 10000),
    number: String(Math.floor(Math.random() * 1000)).padStart(3, '0'),
    status: 'pending',
    items,
    total,
    createdAt: new Date().toISOString()
  };
  
  res.json({
    success: true,
    data: order,
    message: 'Pedido criado com sucesso!'
  });
});

// Rota para métricas de sincronização
app.get('/api/sync/metrics', (req, res) => {
  res.json({
    success: true,
    data: {
      lastSync: new Date().toISOString(),
      pendingItems: 0,
      syncStatus: 'up_to_date',
      conflicts: 0,
      totalSynced: 150
    }
  });
});

// Rota para relatórios
app.get('/api/reports/sales-summary', (req, res) => {
  res.json({
    success: true,
    data: {
      totalSales: 1250.50,
      totalOrders: 45,
      averageTicket: 27.79,
      topProducts: [
        { name: 'Hambúrguer Clássico', quantity: 25, revenue: 647.50 },
        { name: 'Pizza Margherita', quantity: 15, revenue: 525.00 },
        { name: 'Refrigerante Lata', quantity: 40, revenue: 220.00 }
      ],
      period: 'today'
    }
  });
});

// Servir frontend (se existir)
app.get('*', (req, res) => {
  const frontendPath = path.join(__dirname, 'frontend', 'dist', 'index.html');
  
  if (fs.existsSync(frontendPath)) {
    res.sendFile(frontendPath);
  } else {
    res.json({
      success: true,
      message: 'Sistema PDV Adib - API funcionando!',
      frontend: 'Frontend não encontrado. Execute "npm run build" para gerar.',
      api: {
        health: '/api/health',
        status: '/api/status',
        login: 'POST /api/auth/login',
        products: '/api/products',
        orders: '/api/orders'
      },
      credentials: {
        admin: 'admin / password',
        cashier: 'caixa / password',
        kitchen: 'cozinha / password'
      }
    });
  }
});

// Tratamento de erros
app.use((err, req, res, next) => {
  console.error('Erro:', err);
  res.status(500).json({
    success: false,
    error: 'Erro interno do servidor',
    message: err.message
  });
});

// Iniciar servidor
app.listen(PORT, () => {
  console.log('\n🚀 Sistema PDV Adib iniciado com sucesso!');
  console.log(`📡 Servidor rodando em: http://localhost:${PORT}`);
  console.log(`🔗 API Health Check: http://localhost:${PORT}/api/health`);
  console.log(`📊 Status do Sistema: http://localhost:${PORT}/api/status`);
  console.log('\n👤 Credenciais de teste:');
  console.log('   Admin: admin / password');
  console.log('   Caixa: caixa / password');
  console.log('   Cozinha: cozinha / password');
  console.log('\n📚 Endpoints disponíveis:');
  console.log('   GET  /api/health - Health check');
  console.log('   GET  /api/status - Status do sistema');
  console.log('   POST /api/auth/login - Login');
  console.log('   GET  /api/products - Lista de produtos');
  console.log('   GET  /api/orders - Lista de pedidos');
  console.log('   POST /api/orders - Criar pedido');
  console.log('   GET  /api/sync/metrics - Métricas de sync');
  console.log('   GET  /api/reports/sales-summary - Relatório de vendas');
  console.log('\n✨ Sistema pronto para uso!');
});

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('\n🔄 Recebido SIGTERM. Encerrando servidor graciosamente...');
  process.exit(0);
});

process.on('SIGINT', () => {
  console.log('\n🔄 Recebido SIGINT. Encerrando servidor graciosamente...');
  process.exit(0);
});
