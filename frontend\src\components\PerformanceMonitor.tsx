import React, { useEffect, useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  LinearProgress,
  Chip,
  Alert,
  IconButton,
  Collapse,
} from '@mui/material';
import {
  Speed,
  Memory,
  Storage,
  NetworkCheck,
  ExpandMore,
  ExpandLess,
  Warning,
  CheckCircle,
  Error,
} from '@mui/icons-material';

interface PerformanceMetrics {
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  storage: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    status: 'online' | 'offline' | 'slow';
    latency: number;
    speed: number;
  };
  fps: number;
  loadTime: number;
  errors: number;
}

/**
 * Componente para monitoramento de performance em tempo real
 */
const PerformanceMonitor: React.FC = () => {
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [expanded, setExpanded] = useState(false);
  const [alerts, setAlerts] = useState<string[]>([]);

  // Simular coleta de métricas
  useEffect(() => {
    const collectMetrics = () => {
      // Simular dados de performance
      const newMetrics: PerformanceMetrics = {
        memory: {
          used: Math.random() * 500 + 100,
          total: 1024,
          percentage: Math.random() * 60 + 20,
        },
        storage: {
          used: Math.random() * 2000 + 500,
          total: 5000,
          percentage: Math.random() * 40 + 30,
        },
        network: {
          status: navigator.onLine ? 'online' : 'offline',
          latency: Math.random() * 100 + 20,
          speed: Math.random() * 50 + 10,
        },
        fps: Math.random() * 10 + 55,
        loadTime: Math.random() * 1000 + 500,
        errors: Math.floor(Math.random() * 3),
      };

      setMetrics(newMetrics);

      // Verificar alertas
      const newAlerts: string[] = [];
      if (newMetrics.memory.percentage > 80) {
        newAlerts.push('Alto uso de memória');
      }
      if (newMetrics.network.latency > 100) {
        newAlerts.push('Alta latência de rede');
      }
      if (newMetrics.fps < 30) {
        newAlerts.push('FPS baixo detectado');
      }
      if (newMetrics.loadTime > 2000) {
        newAlerts.push('Tempo de carregamento alto');
      }

      setAlerts(newAlerts);
    };

    // Coletar métricas iniciais
    collectMetrics();

    // Atualizar a cada 5 segundos
    const interval = setInterval(collectMetrics, 5000);

    return () => clearInterval(interval);
  }, []);

  // Obter cor baseada na performance
  const getPerformanceColor = (percentage: number): 'success' | 'warning' | 'error' => {
    if (percentage < 50) return 'success';
    if (percentage < 80) return 'warning';
    return 'error';
  };

  // Obter ícone de status de rede
  const getNetworkIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle color="success" />;
      case 'offline':
        return <Error color="error" />;
      case 'slow':
        return <Warning color="warning" />;
      default:
        return <NetworkCheck />;
    }
  };

  if (!metrics) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent>
          <Typography variant="h6">Carregando métricas...</Typography>
          <LinearProgress />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card sx={{ mb: 2 }}>
      <CardContent>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Speed />
            Performance Monitor
          </Typography>
          <IconButton onClick={() => setExpanded(!expanded)}>
            {expanded ? <ExpandLess /> : <ExpandMore />}
          </IconButton>
        </Box>

        {/* Alertas */}
        {alerts.length > 0 && (
          <Box sx={{ mb: 2 }}>
            {alerts.map((alert, index) => (
              <Alert key={index} severity="warning" sx={{ mb: 1 }}>
                {alert}
              </Alert>
            ))}
          </Box>
        )}

        {/* Métricas básicas */}
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Memory color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Memória
              </Typography>
              <Typography variant="h6">
                {metrics.memory.percentage.toFixed(1)}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={metrics.memory.percentage}
                color={getPerformanceColor(metrics.memory.percentage)}
                sx={{ mt: 1 }}
              />
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Storage color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Armazenamento
              </Typography>
              <Typography variant="h6">
                {metrics.storage.percentage.toFixed(1)}%
              </Typography>
              <LinearProgress
                variant="determinate"
                value={metrics.storage.percentage}
                color={getPerformanceColor(metrics.storage.percentage)}
                sx={{ mt: 1 }}
              />
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              {getNetworkIcon(metrics.network.status)}
              <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                Rede
              </Typography>
              <Typography variant="h6">
                {metrics.network.latency.toFixed(0)}ms
              </Typography>
              <Chip
                label={metrics.network.status}
                color={metrics.network.status === 'online' ? 'success' : 'error'}
                size="small"
                sx={{ mt: 1 }}
              />
            </Box>
          </Grid>

          <Grid item xs={6} sm={3}>
            <Box sx={{ textAlign: 'center' }}>
              <Speed color="primary" sx={{ fontSize: 32, mb: 1 }} />
              <Typography variant="body2" color="text.secondary">
                FPS
              </Typography>
              <Typography variant="h6">
                {metrics.fps.toFixed(0)}
              </Typography>
              <Chip
                label={metrics.fps >= 30 ? 'Bom' : 'Baixo'}
                color={metrics.fps >= 30 ? 'success' : 'warning'}
                size="small"
                sx={{ mt: 1 }}
              />
            </Box>
          </Grid>
        </Grid>

        {/* Métricas detalhadas */}
        <Collapse in={expanded}>
          <Box sx={{ mt: 3, pt: 2, borderTop: 1, borderColor: 'divider' }}>
            <Typography variant="h6" gutterBottom>
              Métricas Detalhadas
            </Typography>

            <Grid container spacing={2}>
              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Uso de Memória
                </Typography>
                <Typography variant="body1">
                  {metrics.memory.used.toFixed(1)} MB / {metrics.memory.total} MB
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Uso de Armazenamento
                </Typography>
                <Typography variant="body1">
                  {metrics.storage.used.toFixed(1)} MB / {metrics.storage.total} MB
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Velocidade de Rede
                </Typography>
                <Typography variant="body1">
                  {metrics.network.speed.toFixed(1)} Mbps
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Tempo de Carregamento
                </Typography>
                <Typography variant="body1">
                  {metrics.loadTime.toFixed(0)} ms
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Erros JavaScript
                </Typography>
                <Typography variant="body1">
                  {metrics.errors} erro(s)
                </Typography>
              </Grid>

              <Grid item xs={12} sm={6}>
                <Typography variant="body2" color="text.secondary">
                  Status da Aplicação
                </Typography>
                <Chip
                  label={alerts.length === 0 ? 'Saudável' : 'Atenção'}
                  color={alerts.length === 0 ? 'success' : 'warning'}
                  icon={alerts.length === 0 ? <CheckCircle /> : <Warning />}
                />
              </Grid>
            </Grid>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default PerformanceMonitor;
