import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Grid,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Chip,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  Receipt,
  CheckCircle,
  Error,
  Warning,
  Cancel,
  Print,
  Download,
  Refresh,
  Settings,
  Security,
  CloudDone,
  CloudOff,
  Description,
  Assignment,
  Assessment,
} from '@mui/icons-material';
import { useAuthStore } from '../services/auth';

interface FiscalDocument {
  id: number;
  type: 'nfce' | 'nfe' | 'receipt';
  order_id?: number;
  series: number;
  number: number;
  access_key: string;
  status: 'pending' | 'processing' | 'authorized' | 'rejected' | 'cancelled';
  issue_date: string;
  total_amount: number;
  customer_name?: string;
  order_number?: string;
  protocol?: string;
  rejection_reason?: string;
}

interface FiscalMetrics {
  documents_today: number;
  documents_pending: number;
  documents_rejected: number;
  total_amount_today: number;
  authorization_rate: number;
  contingency_active: boolean;
  certificate_expires_in: number;
  sefaz_status: 'online' | 'offline' | 'unstable';
  last_document_at?: string;
}

interface FiscalConfig {
  id: number;
  company_name: string;
  company_document: string;
  company_ie: string;
  environment: 'production' | 'homologation';
  is_active: boolean;
}

const Fiscal: React.FC = () => {
  const [documents, setDocuments] = useState<FiscalDocument[]>([]);
  const [metrics, setMetrics] = useState<FiscalMetrics | null>(null);
  const [config, setConfig] = useState<FiscalConfig | null>(null);
  const [selectedTab, setSelectedTab] = useState(0);
  const [selectedDocument, setSelectedDocument] = useState<FiscalDocument | null>(null);
  const [detailsDialog, setDetailsDialog] = useState(false);
  const [cancelDialog, setCancelDialog] = useState(false);
  const [cancelReason, setCancelReason] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [loading, setLoading] = useState(false);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular métricas
        const mockMetrics: FiscalMetrics = {
          documents_today: 45,
          documents_pending: 3,
          documents_rejected: 2,
          total_amount_today: 2850.75,
          authorization_rate: 95.6,
          contingency_active: false,
          certificate_expires_in: 120,
          sefaz_status: 'online',
          last_document_at: '2024-12-19T14:30:00Z',
        };

        // Simular configuração
        const mockConfig: FiscalConfig = {
          id: 1,
          company_name: 'Restaurante Adib LTDA',
          company_document: '12.345.678/0001-95',
          company_ie: '*********',
          environment: 'homologation',
          is_active: true,
        };

        // Simular documentos fiscais
        const mockDocuments: FiscalDocument[] = [
          {
            id: 1,
            type: 'nfce',
            order_id: 1001,
            series: 1,
            number: 123,
            access_key: '3524121234567800019565001000000123*********0',
            status: 'authorized',
            issue_date: '2024-12-19T14:30:00Z',
            total_amount: 45.90,
            customer_name: 'João Silva',
            order_number: '20241219001',
            protocol: '135240000000123',
          },
          {
            id: 2,
            type: 'nfce',
            order_id: 1002,
            series: 1,
            number: 124,
            access_key: '3524121234567800019565001000000124*********1',
            status: 'pending',
            issue_date: '2024-12-19T15:15:00Z',
            total_amount: 28.50,
            order_number: '20241219002',
          },
          {
            id: 3,
            type: 'nfce',
            order_id: 1003,
            series: 1,
            number: 125,
            access_key: '3524121234567800019565001000000125*********2',
            status: 'rejected',
            issue_date: '2024-12-19T15:45:00Z',
            total_amount: 67.80,
            order_number: '20241219003',
            rejection_reason: 'Erro na validação do certificado digital',
          },
        ];

        setMetrics(mockMetrics);
        setConfig(mockConfig);
        setDocuments(mockDocuments);
      } catch (error) {
        console.error('Error loading fiscal data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Filtrar documentos
  const filteredDocuments = documents.filter(doc => {
    if (statusFilter === 'all') return true;
    return doc.status === statusFilter;
  });

  // Obter cor do status
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'authorized': return 'success';
      case 'pending': return 'warning';
      case 'processing': return 'info';
      case 'rejected': return 'error';
      case 'cancelled': return 'default';
      default: return 'default';
    }
  };

  // Obter ícone do status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'authorized': return <CheckCircle />;
      case 'pending': return <Warning />;
      case 'processing': return <Refresh />;
      case 'rejected': return <Error />;
      case 'cancelled': return <Cancel />;
      default: return <Description />;
    }
  };

  // Obter nome do tipo de documento
  const getDocumentTypeName = (type: string) => {
    switch (type) {
      case 'nfce': return 'NFC-e';
      case 'nfe': return 'NF-e';
      case 'receipt': return 'Cupom';
      default: return type;
    }
  };

  // Formatar moeda
  const formatCurrency = (value: number): string => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL',
    }).format(value);
  };

  // Formatar data/hora
  const formatDateTime = (dateString: string): string => {
    return new Date(dateString).toLocaleString('pt-BR');
  };

  // Cancelar documento
  const cancelDocument = async () => {
    if (!selectedDocument || !cancelReason.trim()) {
      return;
    }

    // Simular cancelamento
    setDocuments(documents.map(doc =>
      doc.id === selectedDocument.id
        ? { ...doc, status: 'cancelled' as const }
        : doc
    ));

    setCancelDialog(false);
    setCancelReason('');
    setSelectedDocument(null);

    console.log(`Document ${selectedDocument.id} cancelled: ${cancelReason}`);
  };

  // Reprocessar documento
  const reprocessDocument = async (documentId: number) => {
    // Simular reprocessamento
    setDocuments(documents.map(doc =>
      doc.id === documentId
        ? { ...doc, status: 'processing' as const }
        : doc
    ));

    console.log(`Document ${documentId} reprocessing...`);
  };

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              Sistema Fiscal
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <Button
                variant="outlined"
                startIcon={<Refresh />}
                onClick={() => window.location.reload()}
              >
                Atualizar
              </Button>
              <Button
                variant="outlined"
                startIcon={<Settings />}
              >
                Configurações
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Status da Configuração */}
      {config && (
        <Paper sx={{ m: 2, p: 2, bgcolor: config.environment === 'production' ? 'success.light' : 'warning.light' }}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={8}>
              <Typography variant="h6">
                {config.company_name}
              </Typography>
              <Typography variant="body2">
                CNPJ: {config.company_document} | IE: {config.company_ie}
              </Typography>
              <Typography variant="body2">
                Ambiente: {config.environment === 'production' ? 'Produção' : 'Homologação'}
              </Typography>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, justifyContent: 'flex-end' }}>
                {metrics?.sefaz_status === 'online' ? (
                  <Chip icon={<CloudDone />} label="SEFAZ Online" color="success" />
                ) : (
                  <Chip icon={<CloudOff />} label="SEFAZ Offline" color="error" />
                )}
                {metrics?.contingency_active && (
                  <Chip icon={<Warning />} label="Contingência" color="warning" />
                )}
              </Box>
            </Grid>
          </Grid>
        </Paper>
      )}

      <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
        <Tabs value={selectedTab} onChange={(e, newValue) => setSelectedTab(newValue)} sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tab label="Documentos" />
          <Tab label="Métricas" />
          <Tab label="Configuração" />
        </Tabs>

        {/* Tab Documentos */}
        {selectedTab === 0 && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={2} sx={{ mb: 2 }}>
              <Grid item xs={12} md={6}>
                <FormControl size="small" sx={{ minWidth: 200 }}>
                  <InputLabel>Filtrar por Status</InputLabel>
                  <Select
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    label="Filtrar por Status"
                  >
                    <MenuItem value="all">Todos</MenuItem>
                    <MenuItem value="authorized">Autorizados</MenuItem>
                    <MenuItem value="pending">Pendentes</MenuItem>
                    <MenuItem value="rejected">Rejeitados</MenuItem>
                    <MenuItem value="cancelled">Cancelados</MenuItem>
                  </Select>
                </FormControl>
              </Grid>
            </Grid>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Tipo</TableCell>
                    <TableCell>Número</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Pedido</TableCell>
                    <TableCell>Cliente</TableCell>
                    <TableCell align="right">Valor</TableCell>
                    <TableCell>Data/Hora</TableCell>
                    <TableCell align="center">Ações</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {filteredDocuments.map((document) => (
                    <TableRow key={document.id}>
                      <TableCell>
                        <Chip
                          label={getDocumentTypeName(document.type)}
                          size="small"
                          variant="outlined"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {document.series.toString().padStart(3, '0')}-{document.number.toString().padStart(9, '0')}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {document.access_key.slice(-8)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(document.status)}
                          label={document.status}
                          color={getStatusColor(document.status) as any}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {document.order_number && (
                          <Typography variant="body2">
                            #{document.order_number.slice(-3)}
                          </Typography>
                        )}
                      </TableCell>
                      <TableCell>
                        {document.customer_name || '-'}
                      </TableCell>
                      <TableCell align="right">
                        <Typography variant="body2" fontWeight="bold">
                          {formatCurrency(document.total_amount)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {formatDateTime(document.issue_date)}
                        </Typography>
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          size="small"
                          onClick={() => {
                            setSelectedDocument(document);
                            setDetailsDialog(true);
                          }}
                        >
                          <Description />
                        </IconButton>
                        {document.status === 'authorized' && (
                          <>
                            <IconButton size="small">
                              <Print />
                            </IconButton>
                            <IconButton size="small">
                              <Download />
                            </IconButton>
                            <IconButton
                              size="small"
                              onClick={() => {
                                setSelectedDocument(document);
                                setCancelDialog(true);
                              }}
                            >
                              <Cancel />
                            </IconButton>
                          </>
                        )}
                        {document.status === 'rejected' && (
                          <IconButton
                            size="small"
                            onClick={() => reprocessDocument(document.id)}
                          >
                            <Refresh />
                          </IconButton>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        )}

        {/* Tab Métricas */}
        {selectedTab === 1 && metrics && (
          <Box sx={{ p: 2 }}>
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Receipt sx={{ fontSize: 40, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h4" color="primary">
                      {metrics.documents_today}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Documentos Hoje
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Warning sx={{ fontSize: 40, color: 'warning.main', mb: 1 }} />
                    <Typography variant="h4" color="warning.main">
                      {metrics.documents_pending}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Pendentes
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <CheckCircle sx={{ fontSize: 40, color: 'success.main', mb: 1 }} />
                    <Typography variant="h4" color="success.main">
                      {metrics.authorization_rate.toFixed(1)}%
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Taxa de Autorização
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Card>
                  <CardContent sx={{ textAlign: 'center' }}>
                    <Assessment sx={{ fontSize: 40, color: 'info.main', mb: 1 }} />
                    <Typography variant="h4" color="info.main">
                      {formatCurrency(metrics.total_amount_today)}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Faturamento Hoje
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>

            <Grid container spacing={3} sx={{ mt: 2 }}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Status do Sistema
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          {metrics.sefaz_status === 'online' ? (
                            <CloudDone color="success" />
                          ) : (
                            <CloudOff color="error" />
                          )}
                        </ListItemIcon>
                        <ListItemText
                          primary="SEFAZ"
                          secondary={metrics.sefaz_status === 'online' ? 'Online' : 'Offline'}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <Security color={metrics.certificate_expires_in > 30 ? 'success' : 'warning'} />
                        </ListItemIcon>
                        <ListItemText
                          primary="Certificado Digital"
                          secondary={`Expira em ${metrics.certificate_expires_in} dias`}
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          {metrics.contingency_active ? (
                            <Warning color="warning" />
                          ) : (
                            <CheckCircle color="success" />
                          )}
                        </ListItemIcon>
                        <ListItemText
                          primary="Contingência"
                          secondary={metrics.contingency_active ? 'Ativa' : 'Inativa'}
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Últimas Atividades
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Último documento: {metrics.last_document_at ? formatDateTime(metrics.last_document_at) : 'Nenhum'}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                      Documentos rejeitados: {metrics.documents_rejected}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        )}

        {/* Tab Configuração */}
        {selectedTab === 2 && config && (
          <Box sx={{ p: 2 }}>
            <Alert severity="info" sx={{ mb: 2 }}>
              Sistema configurado para ambiente de {config.environment === 'production' ? 'produção' : 'homologação'}.
              Para alterar configurações, entre em contato com o administrador.
            </Alert>

            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom>
                  Dados da Empresa
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Razão Social"
                      value={config.company_name}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="CNPJ"
                      value={config.company_document}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Inscrição Estadual"
                      value={config.company_ie}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                  <Grid item xs={12} md={6}>
                    <TextField
                      fullWidth
                      label="Ambiente"
                      value={config.environment === 'production' ? 'Produção' : 'Homologação'}
                      InputProps={{ readOnly: true }}
                      variant="outlined"
                      size="small"
                    />
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Box>
        )}
      </Box>

      {/* Dialog Detalhes */}
      <Dialog open={detailsDialog} onClose={() => setDetailsDialog(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Detalhes do Documento Fiscal
        </DialogTitle>
        <DialogContent>
          {selectedDocument && (
            <Box>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Tipo: {getDocumentTypeName(selectedDocument.type)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Número: {selectedDocument.series}-{selectedDocument.number}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Status: {selectedDocument.status}
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Valor: {formatCurrency(selectedDocument.total_amount)}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Data: {formatDateTime(selectedDocument.issue_date)}
                  </Typography>
                  {selectedDocument.protocol && (
                    <Typography variant="body2" color="text.secondary">
                      Protocolo: {selectedDocument.protocol}
                    </Typography>
                  )}
                </Grid>
              </Grid>

              <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                Chave de Acesso: {selectedDocument.access_key}
              </Typography>

              {selectedDocument.rejection_reason && (
                <Alert severity="error" sx={{ mt: 2 }}>
                  <strong>Motivo da Rejeição:</strong> {selectedDocument.rejection_reason}
                </Alert>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDetailsDialog(false)}>
            Fechar
          </Button>
        </DialogActions>
      </Dialog>

      {/* Dialog Cancelamento */}
      <Dialog open={cancelDialog} onClose={() => setCancelDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Cancelar Documento Fiscal
        </DialogTitle>
        <DialogContent>
          <Alert severity="warning" sx={{ mb: 2 }}>
            O cancelamento de documento fiscal é irreversível. Informe o motivo do cancelamento.
          </Alert>
          <TextField
            fullWidth
            label="Motivo do Cancelamento"
            value={cancelReason}
            onChange={(e) => setCancelReason(e.target.value)}
            multiline
            rows={3}
            placeholder="Ex: Erro no pedido, cliente desistiu, etc."
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setCancelDialog(false)}>
            Cancelar
          </Button>
          <Button
            variant="contained"
            color="error"
            onClick={cancelDocument}
            disabled={!cancelReason.trim() || cancelReason.length < 10}
          >
            Confirmar Cancelamento
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Fiscal;
