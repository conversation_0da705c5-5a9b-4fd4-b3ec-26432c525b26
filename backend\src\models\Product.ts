import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  Product, 
  CreateProductRequest, 
  UpdateProductRequest, 
  ProductFilters,
  ProductWithRecipe 
} from '../types/products';

export class ProductModel {
  // Buscar produto por ID
  static async findById(id: number): Promise<Product | null> {
    const product = await executeQuerySingle<Product>(
      `SELECT p.*, c.name as category_name, c.color as category_color
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.id = ?`,
      [id]
    );
    return product || null;
  }

  // Buscar produto por código
  static async findByCode(code: string): Promise<Product | null> {
    const product = await executeQuerySingle<Product>(
      `SELECT p.*, c.name as category_name, c.color as category_color
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.code = ? AND p.is_active = 1`,
      [code]
    );
    return product || null;
  }

  // Buscar produto por código de barras
  static async findByBarcode(barcode: string): Promise<Product | null> {
    const product = await executeQuerySingle<Product>(
      `SELECT p.*, c.name as category_name, c.color as category_color
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.barcode = ? AND p.is_active = 1`,
      [barcode]
    );
    return product || null;
  }

  // Listar produtos com filtros
  static async findAll(filters: ProductFilters = {}): Promise<Product[]> {
    let query = `
      SELECT p.*, c.name as category_name, c.color as category_color
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.category_id) {
      query += ' AND p.category_id = ?';
      params.push(filters.category_id);
    }

    if (filters.is_active !== undefined) {
      query += ' AND p.is_active = ?';
      params.push(filters.is_active ? 1 : 0);
    }

    if (filters.stock_control !== undefined) {
      query += ' AND p.stock_control = ?';
      params.push(filters.stock_control ? 1 : 0);
    }

    if (filters.low_stock) {
      query += ' AND p.stock_control = 1 AND p.current_stock <= p.min_stock';
    }

    if (filters.search) {
      query += ' AND (p.name LIKE ? OR p.description LIKE ? OR p.code LIKE ? OR p.barcode LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY p.name ASC';

    return await executeQuery<Product>(query, params);
  }

  // Criar produto
  static async create(productData: CreateProductRequest): Promise<Product> {
    // Verificar se código já existe (se fornecido)
    if (productData.code) {
      const existingProduct = await this.findByCode(productData.code);
      if (existingProduct) {
        throw new Error('Product code already exists');
      }
    }

    // Verificar se código de barras já existe (se fornecido)
    if (productData.barcode) {
      const existingProduct = await this.findByBarcode(productData.barcode);
      if (existingProduct) {
        throw new Error('Barcode already exists');
      }
    }

    // Gerar código automático se não fornecido
    let code = productData.code;
    if (!code) {
      code = await this.generateProductCode();
    }

    // Inserir produto
    const result = await executeUpdate(
      `INSERT INTO products (
        code, barcode, name, description, category_id, price, cost, unit,
        stock_control, current_stock, min_stock, max_stock, preparation_time,
        kitchen_printer, ncm, cfop, tax_rate, is_active
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 1)`,
      [
        code,
        productData.barcode || null,
        productData.name,
        productData.description || null,
        productData.category_id || null,
        productData.price,
        productData.cost || 0,
        productData.unit || 'UN',
        productData.stock_control ? 1 : 0,
        productData.current_stock || 0,
        productData.min_stock || 0,
        productData.max_stock || 0,
        productData.preparation_time || 0,
        productData.kitchen_printer || null,
        productData.ncm || null,
        productData.cfop || '5102',
        productData.tax_rate || 0,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create product');
    }

    // Retornar produto criado
    const newProduct = await this.findById(result.lastID);
    if (!newProduct) {
      throw new Error('Failed to retrieve created product');
    }

    return newProduct;
  }

  // Atualizar produto
  static async update(id: number, productData: UpdateProductRequest): Promise<Product> {
    const product = await this.findById(id);
    if (!product) {
      throw new Error('Product not found');
    }

    // Verificar se novo código já existe (se estiver sendo alterado)
    if (productData.code && productData.code !== product.code) {
      const existingProduct = await this.findByCode(productData.code);
      if (existingProduct) {
        throw new Error('Product code already exists');
      }
    }

    // Verificar se novo código de barras já existe (se estiver sendo alterado)
    if (productData.barcode && productData.barcode !== product.barcode) {
      const existingProduct = await this.findByBarcode(productData.barcode);
      if (existingProduct) {
        throw new Error('Barcode already exists');
      }
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = [
      'code', 'barcode', 'name', 'description', 'category_id', 'price', 'cost',
      'unit', 'stock_control', 'current_stock', 'min_stock', 'max_stock',
      'is_active', 'image_url', 'preparation_time', 'kitchen_printer',
      'ncm', 'cfop', 'tax_rate'
    ];

    updatableFields.forEach(field => {
      if (productData[field as keyof UpdateProductRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = productData[field as keyof UpdateProductRequest];
        
        // Converter boolean para integer
        if (field === 'stock_control' || field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return product; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE products SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar produto atualizado
    const updatedProduct = await this.findById(id);
    if (!updatedProduct) {
      throw new Error('Failed to retrieve updated product');
    }

    return updatedProduct;
  }

  // Deletar produto (soft delete)
  static async delete(id: number): Promise<void> {
    const product = await this.findById(id);
    if (!product) {
      throw new Error('Product not found');
    }

    await executeUpdate(
      'UPDATE products SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Gerar código automático para produto
  static async generateProductCode(): Promise<string> {
    const result = await executeQuerySingle<{ max_id: number }>(
      'SELECT COALESCE(MAX(id), 0) + 1 as max_id FROM products'
    );
    
    const nextId = result?.max_id || 1;
    return `PROD${nextId.toString().padStart(4, '0')}`;
  }

  // Atualizar estoque
  static async updateStock(id: number, quantity: number, operation: 'add' | 'subtract' | 'set'): Promise<void> {
    const product = await this.findById(id);
    if (!product) {
      throw new Error('Product not found');
    }

    if (!product.stock_control) {
      throw new Error('Product does not have stock control enabled');
    }

    let newStock: number;
    
    switch (operation) {
      case 'add':
        newStock = product.current_stock + quantity;
        break;
      case 'subtract':
        newStock = product.current_stock - quantity;
        break;
      case 'set':
        newStock = quantity;
        break;
      default:
        throw new Error('Invalid stock operation');
    }

    // Verificar se permite estoque negativo
    if (newStock < 0) {
      throw new Error('Insufficient stock');
    }

    await executeUpdate(
      'UPDATE products SET current_stock = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [newStock, id]
    );
  }

  // Buscar produtos com estoque baixo
  static async findLowStock(): Promise<Product[]> {
    return await executeQuery<Product>(
      `SELECT p.*, c.name as category_name, c.color as category_color
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.stock_control = 1 
         AND p.current_stock <= p.min_stock 
         AND p.is_active = 1
       ORDER BY (p.current_stock - p.min_stock) ASC`
    );
  }

  // Buscar produtos sem estoque
  static async findOutOfStock(): Promise<Product[]> {
    return await executeQuery<Product>(
      `SELECT p.*, c.name as category_name, c.color as category_color
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.stock_control = 1 
         AND p.current_stock <= 0 
         AND p.is_active = 1
       ORDER BY p.name ASC`
    );
  }

  // Buscar produtos mais vendidos
  static async findTopSelling(limit: number = 10): Promise<Array<Product & { total_sold: number }>> {
    return await executeQuery<Product & { total_sold: number }>(
      `SELECT p.*, c.name as category_name, c.color as category_color,
              COALESCE(SUM(oi.quantity), 0) as total_sold
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       LEFT JOIN order_items oi ON p.id = oi.product_id
       LEFT JOIN orders o ON oi.order_id = o.id
       WHERE p.is_active = 1 
         AND (o.status != 'cancelled' OR o.status IS NULL)
       GROUP BY p.id
       ORDER BY total_sold DESC, p.name ASC
       LIMIT ?`,
      [limit]
    );
  }

  // Verificar disponibilidade para venda
  static async checkAvailability(id: number, quantity: number): Promise<boolean> {
    const product = await this.findById(id);
    if (!product) {
      return false;
    }

    if (!product.is_active) {
      return false;
    }

    if (!product.stock_control) {
      return true; // Produto sem controle de estoque sempre disponível
    }

    return product.current_stock >= quantity;
  }
}
