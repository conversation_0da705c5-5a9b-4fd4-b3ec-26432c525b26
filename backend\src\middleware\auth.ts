import { Request, Response, NextFunction } from 'express';
import { AuthService } from '../services/AuthService';
import { 
  AuthenticatedRequest, 
  AuthenticationError, 
  AuthorizationError,
  UserRole 
} from '../types';

// Middleware para verificar autenticação
export const authenticate = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader) {
      throw new AuthenticationError('No authorization header provided');
    }

    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.slice(7) 
      : authHeader;

    const user = await AuthService.authenticate(token);
    req.user = user;
    
    next();
  } catch (error) {
    if (error instanceof AuthenticationError) {
      res.status(401).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Authentication failed',
        timestamp: new Date().toISOString(),
      });
    }
  }
};

// Middleware para verificar permissões específicas
export const authorize = (resource: string, action: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      const hasPermission = AuthService.canAccess(req.user, resource, action);
      
      if (!hasPermission) {
        throw new AuthorizationError(
          `Access denied. Required permission: ${action} on ${resource}`
        );
      }

      next();
    } catch (error) {
      if (error instanceof AuthorizationError) {
        res.status(403).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      } else if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Authorization failed',
          timestamp: new Date().toISOString(),
        });
      }
    }
  };
};

// Middleware para verificar role específico
export const requireRole = (...roles: UserRole[]) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    try {
      if (!req.user) {
        throw new AuthenticationError('User not authenticated');
      }

      if (!roles.includes(req.user.role)) {
        throw new AuthorizationError(
          `Access denied. Required role: ${roles.join(' or ')}`
        );
      }

      next();
    } catch (error) {
      if (error instanceof AuthorizationError) {
        res.status(403).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      } else if (error instanceof AuthenticationError) {
        res.status(401).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        });
      } else {
        res.status(500).json({
          success: false,
          error: 'Role verification failed',
          timestamp: new Date().toISOString(),
        });
      }
    }
  };
};

// Middleware para verificar se é gerente
export const requireManager = requireRole('manager');

// Middleware para verificar se é caixa ou gerente
export const requireCashierOrManager = requireRole('cashier', 'manager');

// Middleware para verificar se é cozinha, caixa ou gerente
export const requireAnyRole = requireRole('kitchen', 'cashier', 'manager');

// Middleware opcional de autenticação (não falha se não autenticado)
export const optionalAuth = async (
  req: AuthenticatedRequest,
  res: Response,
  next: NextFunction
): Promise<void> => {
  try {
    const authHeader = req.headers.authorization;
    
    if (authHeader) {
      const token = authHeader.startsWith('Bearer ') 
        ? authHeader.slice(7) 
        : authHeader;

      try {
        const user = await AuthService.authenticate(token);
        req.user = user;
      } catch (error) {
        // Ignorar erros de autenticação no modo opcional
      }
    }
    
    next();
  } catch (error) {
    // Em caso de erro inesperado, continuar sem autenticação
    next();
  }
};

// Middleware para log de ações do usuário
export const logUserAction = (action: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    if (req.user) {
      console.log(`User ${req.user.username} (${req.user.role}) performed action: ${action}`);
    }
    next();
  };
};

// Middleware para verificar se usuário pode modificar outro usuário
export const canModifyUser = (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
  try {
    if (!req.user) {
      throw new AuthenticationError('User not authenticated');
    }

    const targetUserId = parseInt(req.params.id);
    const currentUserId = req.user.id;

    // Gerentes podem modificar qualquer usuário
    if (req.user.role === 'manager') {
      next();
      return;
    }

    // Usuários só podem modificar a si mesmos
    if (currentUserId === targetUserId) {
      next();
      return;
    }

    throw new AuthorizationError('You can only modify your own account');
  } catch (error) {
    if (error instanceof AuthorizationError) {
      res.status(403).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    } else if (error instanceof AuthenticationError) {
      res.status(401).json({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString(),
      });
    } else {
      res.status(500).json({
        success: false,
        error: 'Permission check failed',
        timestamp: new Date().toISOString(),
      });
    }
  }
};
