import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { ApiResponse, LoginRequest, LoginResponse, User } from '../types';

// Configuração base da API
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

class ApiService {
  private api: AxiosInstance;
  private token: string | null = null;

  constructor() {
    this.api = axios.create({
      baseURL: API_BASE_URL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Interceptor para adicionar token nas requisições
    this.api.interceptors.request.use(
      (config) => {
        if (this.token) {
          config.headers.Authorization = `Bearer ${this.token}`;
        }
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Interceptor para tratar respostas
    this.api.interceptors.response.use(
      (response: AxiosResponse<ApiResponse>) => {
        return response;
      },
      (error) => {
        // Tratar erros de autenticação
        if (error.response?.status === 401) {
          this.clearToken();
          // Redirecionar para login se necessário
          if (window.location.pathname !== '/login') {
            window.location.href = '/login';
          }
        }

        // Tratar erros de rede
        if (!error.response) {
          console.error('Network error:', error.message);
          throw new Error('Erro de conexão. Verifique sua internet.');
        }

        // Retornar erro da API
        const apiError = error.response.data?.error || 'Erro interno do servidor';
        throw new Error(apiError);
      }
    );

    // Carregar token do localStorage
    this.loadToken();
  }

  // Gerenciamento de token
  setToken(token: string): void {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  getToken(): string | null {
    return this.token;
  }

  clearToken(): void {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  private loadToken(): void {
    const savedToken = localStorage.getItem('auth_token');
    if (savedToken) {
      this.token = savedToken;
    }
  }

  // Métodos HTTP genéricos
  private async request<T>(config: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.api.request<ApiResponse<T>>(config);
      
      if (!response.data.success) {
        throw new Error(response.data.error || 'Erro na requisição');
      }

      return response.data.data as T;
    } catch (error) {
      console.error('API Request Error:', error);
      throw error;
    }
  }

  async get<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'GET', url });
  }

  async post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'POST', url, data });
  }

  async put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'PUT', url, data });
  }

  async delete<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
    return this.request<T>({ ...config, method: 'DELETE', url });
  }

  // Métodos de autenticação
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.post<LoginResponse>('/auth/login', credentials);
    this.setToken(response.token);
    return response;
  }

  async logout(): Promise<void> {
    try {
      await this.post('/auth/logout');
    } finally {
      this.clearToken();
    }
  }

  async refreshToken(): Promise<LoginResponse> {
    if (!this.token) {
      throw new Error('No token to refresh');
    }

    const response = await this.post<LoginResponse>('/auth/refresh', {
      token: this.token,
    });
    
    this.setToken(response.token);
    return response;
  }

  async getProfile(): Promise<User> {
    return this.get<User>('/auth/profile');
  }

  async updateProfile(data: Partial<User>): Promise<User> {
    return this.put<User>('/auth/profile', data);
  }

  async changePassword(currentPassword: string, newPassword: string): Promise<void> {
    await this.put('/auth/change-password', {
      currentPassword,
      newPassword,
    });
  }

  async getPermissions(): Promise<any> {
    return this.get('/auth/permissions');
  }

  async checkAuth(): Promise<{ authenticated: boolean; user: User }> {
    return this.get('/auth/check');
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.get('/health');
  }

  // Verificar se está online
  isOnline(): boolean {
    return navigator.onLine;
  }

  // Verificar se tem token válido
  hasValidToken(): boolean {
    return !!this.token;
  }
}

// Instância singleton da API
export const apiService = new ApiService();

// Hooks para usar com React Query ou SWR
export const apiEndpoints = {
  // Auth
  login: (credentials: LoginRequest) => apiService.login(credentials),
  logout: () => apiService.logout(),
  refreshToken: () => apiService.refreshToken(),
  getProfile: () => apiService.getProfile(),
  updateProfile: (data: Partial<User>) => apiService.updateProfile(data),
  changePassword: (current: string, newPass: string) => 
    apiService.changePassword(current, newPass),
  getPermissions: () => apiService.getPermissions(),
  checkAuth: () => apiService.checkAuth(),
  
  // Health
  healthCheck: () => apiService.healthCheck(),
};

export default apiService;
