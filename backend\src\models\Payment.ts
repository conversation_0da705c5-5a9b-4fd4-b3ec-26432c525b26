import { executeQuery, executeQ<PERSON>y<PERSON>ingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  Payment, 
  CreatePaymentRequest, 
  UpdatePaymentRequest, 
  PaymentFilters,
  PaymentValidation
} from '../types/sales';
import { OrderModel } from './Order';

export class PaymentModel {
  // Buscar pagamento por ID
  static async findById(id: number): Promise<Payment | null> {
    const payment = await executeQuerySingle<Payment>(
      'SELECT * FROM payments WHERE id = ?',
      [id]
    );
    return payment || null;
  }

  // Listar pagamentos com filtros
  static async findAll(filters: PaymentFilters = {}): Promise<Payment[]> {
    let query = 'SELECT * FROM payments WHERE 1=1';
    const params: any[] = [];

    if (filters.method) {
      query += ' AND method = ?';
      params.push(filters.method);
    }

    if (filters.status) {
      query += ' AND status = ?';
      params.push(filters.status);
    }

    if (filters.order_id) {
      query += ' AND order_id = ?';
      params.push(filters.order_id);
    }

    if (filters.date_from) {
      query += ' AND DATE(created_at) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(created_at) <= ?';
      params.push(filters.date_to);
    }

    query += ' ORDER BY created_at DESC';

    return await executeQuery<Payment>(query, params);
  }

  // Buscar pagamentos por pedido
  static async findByOrder(orderId: number): Promise<Payment[]> {
    return await executeQuery<Payment>(
      'SELECT * FROM payments WHERE order_id = ? ORDER BY created_at ASC',
      [orderId]
    );
  }

  // Criar pagamento
  static async create(paymentData: CreatePaymentRequest): Promise<Payment> {
    return await executeTransaction(async (db) => {
      // Verificar se pedido existe
      const order = await OrderModel.findById(paymentData.order_id);
      if (!order) {
        throw new Error('Order not found');
      }

      // Validar pagamento
      const validation = await this.validatePayment(paymentData);
      if (!validation.valid) {
        throw new Error(`Payment validation failed: ${validation.errors.join(', ')}`);
      }

      // Calcular troco
      const receivedAmount = paymentData.received_amount || paymentData.amount;
      const changeAmount = Math.max(0, receivedAmount - paymentData.amount);

      // Inserir pagamento
      const result = await executeUpdate(
        `INSERT INTO payments (
          order_id, method, amount, received_amount, change_amount, 
          installments, card_brand, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, 'pending')`,
        [
          paymentData.order_id,
          paymentData.method,
          paymentData.amount,
          receivedAmount,
          changeAmount,
          paymentData.installments || 1,
          paymentData.card_brand || null,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create payment');
      }

      // Verificar se pedido foi totalmente pago
      const totalPaid = await this.getTotalPaidAmount(paymentData.order_id);
      if (totalPaid >= order.total) {
        await executeUpdate(
          'UPDATE orders SET payment_status = "paid", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [paymentData.order_id]
        );
      }

      // Retornar pagamento criado
      const newPayment = await this.findById(result.lastID);
      if (!newPayment) {
        throw new Error('Failed to retrieve created payment');
      }

      return newPayment;
    });
  }

  // Atualizar pagamento
  static async update(id: number, paymentData: UpdatePaymentRequest): Promise<Payment> {
    const payment = await this.findById(id);
    if (!payment) {
      throw new Error('Payment not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['status', 'authorization_code', 'nsu', 'processed_at'];

    updatableFields.forEach(field => {
      if (paymentData[field as keyof UpdatePaymentRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        params.push(paymentData[field as keyof UpdatePaymentRequest]);
      }
    });

    if (updateFields.length === 0) {
      return payment; // Nada para atualizar
    }

    params.push(id);

    await executeUpdate(
      `UPDATE payments SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar pagamento atualizado
    const updatedPayment = await this.findById(id);
    if (!updatedPayment) {
      throw new Error('Failed to retrieve updated payment');
    }

    return updatedPayment;
  }

  // Aprovar pagamento
  static async approve(id: number, authorizationCode?: string, nsu?: string): Promise<Payment> {
    return await executeTransaction(async (db) => {
      const payment = await this.findById(id);
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status !== 'pending') {
        throw new Error('Payment is not pending');
      }

      // Atualizar status do pagamento
      await executeUpdate(
        `UPDATE payments SET 
           status = 'approved', 
           authorization_code = ?, 
           nsu = ?, 
           processed_at = CURRENT_TIMESTAMP 
         WHERE id = ?`,
        [authorizationCode || null, nsu || null, id]
      );

      // Verificar se pedido foi totalmente pago
      const totalPaid = await this.getTotalPaidAmount(payment.order_id);
      const order = await OrderModel.findById(payment.order_id);
      
      if (order && totalPaid >= order.total) {
        await executeUpdate(
          'UPDATE orders SET payment_status = "paid", updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [payment.order_id]
        );
      }

      // Retornar pagamento atualizado
      const updatedPayment = await this.findById(id);
      if (!updatedPayment) {
        throw new Error('Failed to retrieve updated payment');
      }

      return updatedPayment;
    });
  }

  // Cancelar pagamento
  static async cancel(id: number): Promise<Payment> {
    return await executeTransaction(async (db) => {
      const payment = await this.findById(id);
      if (!payment) {
        throw new Error('Payment not found');
      }

      if (payment.status === 'cancelled') {
        throw new Error('Payment is already cancelled');
      }

      // Atualizar status do pagamento
      await executeUpdate(
        'UPDATE payments SET status = "cancelled", processed_at = CURRENT_TIMESTAMP WHERE id = ?',
        [id]
      );

      // Atualizar status do pedido
      const totalPaid = await this.getTotalPaidAmount(payment.order_id);
      const order = await OrderModel.findById(payment.order_id);
      
      if (order) {
        const newPaymentStatus = totalPaid >= order.total ? 'paid' : 'pending';
        await executeUpdate(
          'UPDATE orders SET payment_status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [newPaymentStatus, payment.order_id]
        );
      }

      // Retornar pagamento atualizado
      const updatedPayment = await this.findById(id);
      if (!updatedPayment) {
        throw new Error('Failed to retrieve updated payment');
      }

      return updatedPayment;
    });
  }

  // Obter total pago de um pedido
  static async getTotalPaidAmount(orderId: number): Promise<number> {
    const result = await executeQuerySingle<{ total: number }>(
      'SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE order_id = ? AND status = "approved"',
      [orderId]
    );
    return result?.total || 0;
  }

  // Validar pagamento
  static async validatePayment(paymentData: CreatePaymentRequest): Promise<PaymentValidation> {
    const errors: string[] = [];

    // Verificar se pedido existe
    const order = await OrderModel.findById(paymentData.order_id);
    if (!order) {
      errors.push('Order not found');
      return {
        valid: false,
        errors,
        total_paid: 0,
        total_order: 0,
        remaining: 0,
        change: 0,
      };
    }

    // Verificar valor do pagamento
    if (paymentData.amount <= 0) {
      errors.push('Payment amount must be greater than 0');
    }

    // Verificar se não está pagando mais que o necessário
    const totalPaid = await this.getTotalPaidAmount(paymentData.order_id);
    const remaining = order.total - totalPaid;
    
    if (paymentData.amount > remaining) {
      errors.push(`Payment amount (${paymentData.amount}) exceeds remaining amount (${remaining})`);
    }

    // Validações específicas por método
    if (paymentData.method === 'credit_card' || paymentData.method === 'debit_card') {
      if (paymentData.installments && paymentData.installments > 12) {
        errors.push('Maximum 12 installments allowed');
      }
      
      if (paymentData.method === 'debit_card' && paymentData.installments && paymentData.installments > 1) {
        errors.push('Debit card payments cannot be installments');
      }
    }

    if (paymentData.method === 'cash') {
      const receivedAmount = paymentData.received_amount || paymentData.amount;
      if (receivedAmount < paymentData.amount) {
        errors.push('Received amount cannot be less than payment amount');
      }
    }

    const change = paymentData.method === 'cash' 
      ? Math.max(0, (paymentData.received_amount || paymentData.amount) - paymentData.amount)
      : 0;

    return {
      valid: errors.length === 0,
      errors,
      total_paid: totalPaid,
      total_order: order.total,
      remaining: remaining - paymentData.amount,
      change,
    };
  }

  // Obter estatísticas de pagamentos
  static async getStats(dateFrom?: string, dateTo?: string): Promise<{
    total_payments: number;
    total_amount: number;
    by_method: Array<{
      method: string;
      count: number;
      amount: number;
      percentage: number;
    }>;
    by_status: Array<{
      status: string;
      count: number;
      amount: number;
    }>;
  }> {
    let whereClause = 'WHERE 1=1';
    const params: any[] = [];

    if (dateFrom) {
      whereClause += ' AND DATE(created_at) >= ?';
      params.push(dateFrom);
    }

    if (dateTo) {
      whereClause += ' AND DATE(created_at) <= ?';
      params.push(dateTo);
    }

    // Estatísticas gerais
    const generalStats = await executeQuerySingle<{
      total_payments: number;
      total_amount: number;
    }>(
      `SELECT COUNT(*) as total_payments, COALESCE(SUM(amount), 0) as total_amount 
       FROM payments ${whereClause}`,
      params
    );

    // Por método
    const byMethod = await executeQuery<{
      method: string;
      count: number;
      amount: number;
    }>(
      `SELECT method, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount
       FROM payments ${whereClause}
       GROUP BY method
       ORDER BY amount DESC`,
      params
    );

    // Por status
    const byStatus = await executeQuery<{
      status: string;
      count: number;
      amount: number;
    }>(
      `SELECT status, COUNT(*) as count, COALESCE(SUM(amount), 0) as amount
       FROM payments ${whereClause}
       GROUP BY status
       ORDER BY amount DESC`,
      params
    );

    const totalAmount = generalStats?.total_amount || 0;

    // Calcular percentuais
    const byMethodWithPercentage = byMethod.map(item => ({
      ...item,
      percentage: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0,
    }));

    return {
      total_payments: generalStats?.total_payments || 0,
      total_amount: totalAmount,
      by_method: byMethodWithPercentage,
      by_status: byStatus,
    };
  }

  // Buscar pagamentos do dia
  static async findToday(): Promise<Payment[]> {
    const today = new Date().toISOString().slice(0, 10);
    return await this.findAll({
      date_from: today,
      date_to: today,
    });
  }

  // Buscar pagamentos aprovados
  static async findApproved(dateFrom?: string, dateTo?: string): Promise<Payment[]> {
    return await this.findAll({
      status: 'approved',
      date_from: dateFrom,
      date_to: dateTo,
    });
  }
}
