// Tipos para TEF (Transferência Eletrônica de Fundos)

export interface TefConfig {
  id: number;
  provider: TefProvider;
  provider_name: string;
  terminal_id: string;
  merchant_id: string;
  api_key?: string;
  api_secret?: string;
  endpoint_url?: string;
  pinpad_port?: string;
  pinpad_model?: string;
  timeout_seconds: number;
  auto_confirm: boolean;
  print_customer_receipt: boolean;
  print_merchant_receipt: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type TefProvider = 'cielo' | 'rede' | 'stone' | 'getnet' | 'pagseguro' | 'mercadopago' | 'simulator';

export interface CreateTefConfigRequest {
  provider: TefProvider;
  provider_name: string;
  terminal_id: string;
  merchant_id: string;
  api_key?: string;
  api_secret?: string;
  endpoint_url?: string;
  pinpad_port?: string;
  pinpad_model?: string;
  timeout_seconds?: number;
  auto_confirm?: boolean;
  print_customer_receipt?: boolean;
  print_merchant_receipt?: boolean;
}

export interface UpdateTefConfigRequest {
  provider_name?: string;
  terminal_id?: string;
  merchant_id?: string;
  api_key?: string;
  api_secret?: string;
  endpoint_url?: string;
  pinpad_port?: string;
  pinpad_model?: string;
  timeout_seconds?: number;
  auto_confirm?: boolean;
  print_customer_receipt?: boolean;
  print_merchant_receipt?: boolean;
  is_active?: boolean;
}

export interface TefTransaction {
  id: number;
  order_id?: number;
  cash_session_id?: number;
  type: TefTransactionType;
  operation: TefOperation;
  amount: number;
  installments: number;
  card_type: CardType;
  card_brand?: string;
  card_number_masked?: string;
  authorization_code?: string;
  nsu?: string;
  transaction_id?: string;
  status: TefTransactionStatus;
  provider_response?: string;
  error_message?: string;
  receipt_customer?: string;
  receipt_merchant?: string;
  processed_at?: string;
  confirmed_at?: string;
  cancelled_at?: string;
  cancellation_reason?: string;
  user_id: number;
  user_name?: string;
  created_at: string;
  updated_at: string;
}

export type TefTransactionType = 'sale' | 'cancellation' | 'administrative';
export type TefOperation = 'credit' | 'debit' | 'voucher' | 'cancel' | 'reprint' | 'administrative';
export type CardType = 'credit' | 'debit' | 'voucher' | 'unknown';
export type TefTransactionStatus = 
  | 'pending' 
  | 'processing' 
  | 'approved' 
  | 'denied' 
  | 'cancelled' 
  | 'error' 
  | 'timeout';

export interface CreateTefTransactionRequest {
  order_id?: number;
  cash_session_id?: number;
  type: TefTransactionType;
  operation: TefOperation;
  amount: number;
  installments?: number;
  card_type?: CardType;
}

export interface UpdateTefTransactionRequest {
  status?: TefTransactionStatus;
  card_brand?: string;
  card_number_masked?: string;
  authorization_code?: string;
  nsu?: string;
  transaction_id?: string;
  provider_response?: string;
  error_message?: string;
  receipt_customer?: string;
  receipt_merchant?: string;
  processed_at?: string;
  confirmed_at?: string;
}

export interface CancelTefTransactionRequest {
  reason: string;
  original_transaction_id?: string;
  authorization_code?: string;
}

// Filtros
export interface TefTransactionFilters {
  type?: TefTransactionType;
  operation?: TefOperation;
  status?: TefTransactionStatus;
  card_type?: CardType;
  order_id?: number;
  cash_session_id?: number;
  user_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

// Comunicação com PinPad
export interface PinPadRequest {
  command: PinPadCommand;
  amount?: number;
  installments?: number;
  operation?: TefOperation;
  transaction_id?: string;
  timeout?: number;
}

export interface PinPadResponse {
  success: boolean;
  command: PinPadCommand;
  status: PinPadStatus;
  data?: any;
  error?: string;
  card_type?: CardType;
  card_brand?: string;
  card_number_masked?: string;
  authorization_code?: string;
  nsu?: string;
  transaction_id?: string;
  receipt_customer?: string;
  receipt_merchant?: string;
  timestamp: string;
}

export type PinPadCommand = 
  | 'sale_credit'
  | 'sale_debit' 
  | 'cancel'
  | 'administrative'
  | 'reprint'
  | 'status'
  | 'initialize'
  | 'close';

export type PinPadStatus = 
  | 'ready'
  | 'processing'
  | 'waiting_card'
  | 'waiting_password'
  | 'approved'
  | 'denied'
  | 'cancelled'
  | 'error'
  | 'timeout'
  | 'offline';

// Relatórios TEF
export interface TefReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_transactions: number;
    total_approved: number;
    total_denied: number;
    total_cancelled: number;
    total_amount: number;
    approval_rate: number;
    average_amount: number;
  };
  by_operation: Array<{
    operation: TefOperation;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_card_type: Array<{
    card_type: CardType;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_card_brand: Array<{
    brand: string;
    count: number;
    amount: number;
    percentage: number;
  }>;
  by_status: Array<{
    status: TefTransactionStatus;
    count: number;
    amount: number;
  }>;
  by_hour: Array<{
    hour: number;
    transactions: number;
    amount: number;
  }>;
}

// Métricas TEF
export interface TefMetrics {
  transactions_today: number;
  transactions_pending: number;
  transactions_approved: number;
  transactions_denied: number;
  total_amount_today: number;
  approval_rate: number;
  average_transaction_time: number;
  pinpad_status: PinPadStatus;
  last_transaction_at?: string;
  provider_status: 'online' | 'offline' | 'unstable';
  daily_limit_used: number;
  daily_limit_total: number;
}

// Configurações de limite
export interface TefLimits {
  id: number;
  card_type: CardType;
  operation: TefOperation;
  min_amount: number;
  max_amount: number;
  max_installments: number;
  daily_limit: number;
  monthly_limit: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateTefLimitsRequest {
  card_type: CardType;
  operation: TefOperation;
  min_amount?: number;
  max_amount?: number;
  max_installments?: number;
  daily_limit?: number;
  monthly_limit?: number;
}

export interface UpdateTefLimitsRequest {
  min_amount?: number;
  max_amount?: number;
  max_installments?: number;
  daily_limit?: number;
  monthly_limit?: number;
  is_active?: boolean;
}

// Conciliação TEF
export interface TefReconciliation {
  id: number;
  date: string;
  provider: TefProvider;
  terminal_id: string;
  total_transactions: number;
  total_amount: number;
  total_fees: number;
  net_amount: number;
  status: ReconciliationStatus;
  file_path?: string;
  processed_at?: string;
  created_at: string;
  updated_at: string;
}

export type ReconciliationStatus = 'pending' | 'processing' | 'completed' | 'failed' | 'partial';

export interface CreateReconciliationRequest {
  date: string;
  provider: TefProvider;
  terminal_id: string;
}

// Auditoria TEF
export interface TefAudit {
  id: number;
  transaction_id?: number;
  action: TefAuditAction;
  description: string;
  user_id: number;
  user_name?: string;
  ip_address?: string;
  user_agent?: string;
  created_at: string;
}

export type TefAuditAction = 
  | 'transaction_created'
  | 'transaction_approved'
  | 'transaction_denied'
  | 'transaction_cancelled'
  | 'receipt_printed'
  | 'config_updated'
  | 'pinpad_initialized'
  | 'reconciliation_processed';

// Validações TEF
export interface TefValidation {
  valid: boolean;
  errors: string[];
  warnings: string[];
  pinpad_info?: {
    connected: boolean;
    model: string;
    version: string;
    status: PinPadStatus;
  };
  provider_info?: {
    online: boolean;
    response_time: number;
    last_check: string;
  };
  limits_info?: {
    within_limits: boolean;
    daily_used: number;
    daily_limit: number;
  };
}

// Simulador TEF (para testes)
export interface TefSimulatorConfig {
  enabled: boolean;
  approval_rate: number;
  processing_delay: number;
  simulate_errors: boolean;
  error_rate: number;
  simulate_timeout: boolean;
  timeout_rate: number;
}

export interface TefSimulatorRequest {
  action: 'approve' | 'deny' | 'timeout' | 'error';
  amount: number;
  card_type: CardType;
  installments?: number;
}

export interface TefSimulatorResponse {
  approved: boolean;
  authorization_code?: string;
  nsu?: string;
  error_message?: string;
  processing_time: number;
}

// Integração com adquirentes
export interface AcquirerRequest {
  provider: TefProvider;
  operation: TefOperation;
  amount: number;
  installments?: number;
  card_data?: {
    number?: string;
    holder_name?: string;
    expiry_date?: string;
    cvv?: string;
  };
  transaction_id?: string;
  merchant_id: string;
  terminal_id: string;
}

export interface AcquirerResponse {
  success: boolean;
  transaction_id: string;
  authorization_code?: string;
  nsu?: string;
  response_code: string;
  response_message: string;
  card_brand?: string;
  card_number_masked?: string;
  receipt_data?: {
    customer: string;
    merchant: string;
  };
  processing_time: number;
  timestamp: string;
}

// Configurações de impressão
export interface TefPrintConfig {
  id: number;
  receipt_type: ReceiptType;
  template: string;
  auto_print: boolean;
  copies: number;
  printer_name?: string;
  paper_width: number;
  font_size: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type ReceiptType = 'customer' | 'merchant' | 'both';

export interface CreateTefPrintConfigRequest {
  receipt_type: ReceiptType;
  template: string;
  auto_print?: boolean;
  copies?: number;
  printer_name?: string;
  paper_width?: number;
  font_size?: number;
}

export interface UpdateTefPrintConfigRequest {
  template?: string;
  auto_print?: boolean;
  copies?: number;
  printer_name?: string;
  paper_width?: number;
  font_size?: number;
  is_active?: boolean;
}
