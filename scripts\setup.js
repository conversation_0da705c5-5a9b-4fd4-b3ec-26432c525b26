#!/usr/bin/env node

/**
 * Script de configuração inicial do Sistema PDV Adib
 * Configura banco de dados, dependências e estrutura inicial
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏪 Configurando Sistema PDV Adib...\n');

// Cores para output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function execCommand(command, cwd = process.cwd()) {
  try {
    log(`Executando: ${command}`, 'cyan');
    execSync(command, { cwd, stdio: 'inherit' });
    return true;
  } catch (error) {
    log(`Erro ao executar: ${command}`, 'red');
    log(error.message, 'red');
    return false;
  }
}

function createDirectoryIfNotExists(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    log(`Diretório criado: ${dirPath}`, 'green');
  }
}

function copyFileIfNotExists(source, destination) {
  if (!fs.existsSync(destination)) {
    fs.copyFileSync(source, destination);
    log(`Arquivo copiado: ${destination}`, 'green');
  }
}

async function main() {
  try {
    // 1. Verificar Node.js e npm
    log('1. Verificando pré-requisitos...', 'bright');
    
    try {
      const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
      const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
      log(`Node.js: ${nodeVersion}`, 'green');
      log(`npm: ${npmVersion}`, 'green');
    } catch (error) {
      log('Node.js ou npm não encontrado!', 'red');
      process.exit(1);
    }

    // 2. Criar diretórios necessários
    log('\n2. Criando estrutura de diretórios...', 'bright');
    
    const directories = [
      'logs',
      'database/backups',
      'database/migrations',
      'database/seeds',
      'certificates',
      'uploads',
      'temp_files',
      'fiscal_docs/xml',
      'fiscal_docs/pdf',
      'frontend/src/components',
      'frontend/src/pages',
      'frontend/src/services',
      'frontend/src/utils',
      'frontend/src/types',
      'backend/src/controllers',
      'backend/src/models',
      'backend/src/services',
      'backend/src/routes',
      'backend/src/middleware',
      'backend/src/types',
      'backend/src/utils',
      'fiscal/src/nfce',
      'fiscal/src/nfe',
      'fiscal/src/utils'
    ];

    directories.forEach(createDirectoryIfNotExists);

    // 3. Copiar arquivos de configuração
    log('\n3. Configurando arquivos de ambiente...', 'bright');
    
    copyFileIfNotExists('backend/.env.example', 'backend/.env');

    // 4. Instalar dependências do frontend
    log('\n4. Instalando dependências do frontend...', 'bright');
    if (!execCommand('npm install', 'frontend')) {
      log('Falha ao instalar dependências do frontend', 'red');
      process.exit(1);
    }

    // 5. Instalar dependências do backend
    log('\n5. Instalando dependências do backend...', 'bright');
    if (!execCommand('npm install', 'backend')) {
      log('Falha ao instalar dependências do backend', 'red');
      process.exit(1);
    }

    // 6. Configurar ambiente Python para fiscal
    log('\n6. Configurando ambiente Python...', 'bright');
    
    // Verificar Python
    try {
      const pythonVersion = execSync('python --version', { encoding: 'utf8' }).trim();
      log(`Python: ${pythonVersion}`, 'green');
    } catch (error) {
      try {
        const python3Version = execSync('python3 --version', { encoding: 'utf8' }).trim();
        log(`Python3: ${python3Version}`, 'green');
      } catch (error) {
        log('Python não encontrado!', 'red');
        log('Instale Python 3.9+ para continuar', 'yellow');
        process.exit(1);
      }
    }

    // Instalar dependências Python
    if (!execCommand('pip install -r requirements.txt', 'fiscal')) {
      log('Falha ao instalar dependências Python', 'red');
      log('Tentando com pip3...', 'yellow');
      if (!execCommand('pip3 install -r requirements.txt', 'fiscal')) {
        log('Falha ao instalar dependências Python', 'red');
        process.exit(1);
      }
    }

    // 7. Configurar banco de dados
    log('\n7. Configurando banco de dados...', 'bright');
    
    // Criar banco SQLite
    const dbPath = path.join('database', 'adib.db');
    if (!fs.existsSync(dbPath)) {
      // Executar schema
      const sqlite3 = require('sqlite3').verbose();
      const db = new sqlite3.Database(dbPath);
      
      const schema = fs.readFileSync('database/schema.sql', 'utf8');
      db.exec(schema, (err) => {
        if (err) {
          log(`Erro ao criar banco: ${err.message}`, 'red');
        } else {
          log('Banco de dados criado com sucesso!', 'green');
        }
        db.close();
      });
    }

    // 8. Configurações finais
    log('\n8. Configurações finais...', 'bright');
    
    // Criar arquivo de configuração inicial
    const initialConfig = {
      setupCompleted: true,
      setupDate: new Date().toISOString(),
      version: '1.0.0'
    };
    
    fs.writeFileSync(
      'config.json',
      JSON.stringify(initialConfig, null, 2)
    );

    // 9. Sucesso!
    log('\n✅ Configuração concluída com sucesso!', 'green');
    log('\nPróximos passos:', 'bright');
    log('1. Configure o arquivo backend/.env com suas credenciais', 'yellow');
    log('2. Execute "npm run dev" para iniciar o desenvolvimento', 'yellow');
    log('3. Acesse http://localhost:3000 para ver a aplicação', 'yellow');
    log('\nDocumentação: https://docs.adibpdv.com', 'cyan');

  } catch (error) {
    log(`\n❌ Erro durante a configuração: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar setup
main();
