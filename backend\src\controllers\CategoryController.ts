import { Request, Response } from 'express';
import { CategoryModel } from '../models/Category';
import { 
  CreateCategoryRequest, 
  UpdateCategoryRequest, 
  CategoryFilters,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class CategoryController {
  // Listar categorias
  static async index(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: CategoryFilters = {
        is_active: req.query.is_active !== undefined ? req.query.is_active === 'true' : undefined,
        search: req.query.search as string,
      };

      const withProductCount = req.query.with_product_count === 'true';

      let categories;
      if (withProductCount) {
        categories = await CategoryModel.findAllWithProductCount();
      } else {
        categories = await CategoryModel.findAll(filters);
      }

      res.status(200).json({
        success: true,
        data: categories,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get categories error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get categories',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar categoria por ID
  static async show(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid category ID');
      }

      const category = await CategoryModel.findById(id);
      if (!category) {
        throw new NotFoundError('Category not found');
      }

      // Incluir estatísticas se solicitado
      if (req.query.with_stats === 'true') {
        const stats = await CategoryModel.getStats(id);
        res.status(200).json({
          success: true,
          data: { ...category, stats },
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(200).json({
          success: true,
          data: category,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }

    } catch (error) {
      console.error('Get category error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get category',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Criar categoria
  static async create(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const categoryData: CreateCategoryRequest = req.body;

      // Validações básicas
      if (!categoryData.name) {
        throw new ValidationError('Category name is required');
      }

      if (categoryData.name.length < 2 || categoryData.name.length > 100) {
        throw new ValidationError('Category name must be between 2 and 100 characters');
      }

      const category = await CategoryModel.create(categoryData);

      console.log(`Category ${category.name} created by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: category,
        message: 'Category created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create category error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create category',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar categoria
  static async update(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const categoryData: UpdateCategoryRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid category ID');
      }

      // Validações
      if (categoryData.name && (categoryData.name.length < 2 || categoryData.name.length > 100)) {
        throw new ValidationError('Category name must be between 2 and 100 characters');
      }

      const category = await CategoryModel.update(id, categoryData);

      console.log(`Category ${category.name} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: category,
        message: 'Category updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update category error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update category',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Deletar categoria
  static async delete(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid category ID');
      }

      await CategoryModel.delete(id);

      console.log(`Category ${id} deleted by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        message: 'Category deleted successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Delete category error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete category',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar categorias mais usadas
  static async mostUsed(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 5;
      const categories = await CategoryModel.findMostUsed(limit);

      res.status(200).json({
        success: true,
        data: categories,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get most used categories error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get most used categories',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Verificar se categoria pode ser deletada
  static async canDelete(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid category ID');
      }

      const canDelete = await CategoryModel.canDelete(id);
      const productCount = await CategoryModel.getProductCount(id);

      res.status(200).json({
        success: true,
        data: {
          can_delete: canDelete,
          product_count: productCount,
          message: canDelete 
            ? 'Category can be deleted' 
            : `Cannot delete category with ${productCount} active products`,
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Check can delete category error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to check if category can be deleted',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Obter estatísticas da categoria
  static async getStats(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid category ID');
      }

      const category = await CategoryModel.findById(id);
      if (!category) {
        throw new NotFoundError('Category not found');
      }

      const stats = await CategoryModel.getStats(id);

      res.status(200).json({
        success: true,
        data: {
          category,
          stats,
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get category stats error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get category stats',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
