import * as fs from 'fs';
import * as path from 'path';

export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

export interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  module?: string;
  userId?: number;
  username?: string;
  ip?: string;
  userAgent?: string;
  requestId?: string;
  duration?: number;
  statusCode?: number;
  method?: string;
  url?: string;
  data?: any;
  error?: {
    name: string;
    message: string;
    stack?: string;
  };
}

/**
 * Sistema de logs estruturados
 */
class Logger {
  private static instance: Logger;
  private logLevel: LogLevel = LogLevel.INFO;
  private logDir: string = './logs';
  private maxFileSize: number = 10 * 1024 * 1024; // 10MB
  private maxFiles: number = 10;

  private constructor() {
    this.ensureLogDirectory();
    this.setLogLevel();
  }

  static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDir)) {
      fs.mkdirSync(this.logDir, { recursive: true });
    }
  }

  private setLogLevel(): void {
    const envLevel = process.env.LOG_LEVEL?.toUpperCase();
    switch (envLevel) {
      case 'ERROR':
        this.logLevel = LogLevel.ERROR;
        break;
      case 'WARN':
        this.logLevel = LogLevel.WARN;
        break;
      case 'INFO':
        this.logLevel = LogLevel.INFO;
        break;
      case 'DEBUG':
        this.logLevel = LogLevel.DEBUG;
        break;
      default:
        this.logLevel = process.env.NODE_ENV === 'development' ? LogLevel.DEBUG : LogLevel.INFO;
    }
  }

  private shouldLog(level: LogLevel): boolean {
    return level <= this.logLevel;
  }

  private formatLogEntry(entry: LogEntry): string {
    return JSON.stringify(entry) + '\n';
  }

  private getLogFileName(type: string = 'app'): string {
    const date = new Date().toISOString().split('T')[0];
    return path.join(this.logDir, `${type}-${date}.log`);
  }

  private writeToFile(entry: LogEntry, type: string = 'app'): void {
    try {
      const fileName = this.getLogFileName(type);
      const logLine = this.formatLogEntry(entry);

      // Verificar tamanho do arquivo
      if (fs.existsSync(fileName)) {
        const stats = fs.statSync(fileName);
        if (stats.size > this.maxFileSize) {
          this.rotateLogFile(fileName);
        }
      }

      fs.appendFileSync(fileName, logLine);
    } catch (error) {
      console.error('Failed to write log:', error);
    }
  }

  private rotateLogFile(fileName: string): void {
    try {
      const baseName = fileName.replace('.log', '');
      
      // Mover arquivos existentes
      for (let i = this.maxFiles - 1; i > 0; i--) {
        const oldFile = `${baseName}.${i}.log`;
        const newFile = `${baseName}.${i + 1}.log`;
        
        if (fs.existsSync(oldFile)) {
          if (i === this.maxFiles - 1) {
            fs.unlinkSync(oldFile); // Deletar o mais antigo
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Renomear arquivo atual
      if (fs.existsSync(fileName)) {
        fs.renameSync(fileName, `${baseName}.1.log`);
      }
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  private createLogEntry(
    level: string,
    message: string,
    module?: string,
    metadata?: Partial<LogEntry>
  ): LogEntry {
    return {
      timestamp: new Date().toISOString(),
      level,
      message,
      module,
      ...metadata,
    };
  }

  // Métodos públicos de logging
  error(message: string, module?: string, metadata?: Partial<LogEntry>): void {
    if (!this.shouldLog(LogLevel.ERROR)) return;

    const entry = this.createLogEntry('ERROR', message, module, metadata);
    
    console.error(`[ERROR] ${entry.timestamp} ${module ? `[${module}] ` : ''}${message}`, metadata);
    this.writeToFile(entry, 'error');
    this.writeToFile(entry);
  }

  warn(message: string, module?: string, metadata?: Partial<LogEntry>): void {
    if (!this.shouldLog(LogLevel.WARN)) return;

    const entry = this.createLogEntry('WARN', message, module, metadata);
    
    console.warn(`[WARN] ${entry.timestamp} ${module ? `[${module}] ` : ''}${message}`, metadata);
    this.writeToFile(entry);
  }

  info(message: string, module?: string, metadata?: Partial<LogEntry>): void {
    if (!this.shouldLog(LogLevel.INFO)) return;

    const entry = this.createLogEntry('INFO', message, module, metadata);
    
    console.log(`[INFO] ${entry.timestamp} ${module ? `[${module}] ` : ''}${message}`, metadata);
    this.writeToFile(entry);
  }

  debug(message: string, module?: string, metadata?: Partial<LogEntry>): void {
    if (!this.shouldLog(LogLevel.DEBUG)) return;

    const entry = this.createLogEntry('DEBUG', message, module, metadata);
    
    console.log(`[DEBUG] ${entry.timestamp} ${module ? `[${module}] ` : ''}${message}`, metadata);
    this.writeToFile(entry);
  }

  // Logs específicos para diferentes tipos de eventos
  security(message: string, metadata?: Partial<LogEntry>): void {
    const entry = this.createLogEntry('SECURITY', message, 'security', metadata);
    
    console.warn(`[SECURITY] ${entry.timestamp} ${message}`, metadata);
    this.writeToFile(entry, 'security');
    this.writeToFile(entry);
  }

  audit(message: string, metadata?: Partial<LogEntry>): void {
    const entry = this.createLogEntry('AUDIT', message, 'audit', metadata);
    
    console.log(`[AUDIT] ${entry.timestamp} ${message}`, metadata);
    this.writeToFile(entry, 'audit');
    this.writeToFile(entry);
  }

  performance(message: string, duration: number, metadata?: Partial<LogEntry>): void {
    const entry = this.createLogEntry('PERFORMANCE', message, 'performance', {
      ...metadata,
      duration,
    });
    
    if (duration > 1000) { // Log apenas operações lentas (>1s)
      console.log(`[PERFORMANCE] ${entry.timestamp} ${message} (${duration}ms)`, metadata);
      this.writeToFile(entry, 'performance');
    }
  }

  // Método para logs de requisições HTTP
  request(
    method: string,
    url: string,
    statusCode: number,
    duration: number,
    userId?: number,
    username?: string,
    ip?: string,
    userAgent?: string,
    requestId?: string
  ): void {
    const entry = this.createLogEntry('REQUEST', `${method} ${url}`, 'http', {
      method,
      url,
      statusCode,
      duration,
      userId,
      username,
      ip,
      userAgent,
      requestId,
    });

    const statusColor = statusCode >= 400 ? '\x1b[31m' : statusCode >= 300 ? '\x1b[33m' : '\x1b[32m';
    const resetColor = '\x1b[0m';
    
    console.log(
      `[REQUEST] ${entry.timestamp} ${statusColor}${statusCode}${resetColor} ${method} ${url} ${duration}ms ${username ? `(${username})` : ''}`
    );
    
    this.writeToFile(entry, 'access');
    
    // Log também no arquivo principal se for erro
    if (statusCode >= 400) {
      this.writeToFile(entry);
    }
  }

  // Método para capturar erros não tratados
  captureException(error: Error, module?: string, metadata?: Partial<LogEntry>): void {
    const entry = this.createLogEntry('EXCEPTION', error.message, module, {
      ...metadata,
      error: {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
    });

    console.error(`[EXCEPTION] ${entry.timestamp} ${module ? `[${module}] ` : ''}${error.message}`);
    console.error(error.stack);
    
    this.writeToFile(entry, 'error');
    this.writeToFile(entry);
  }

  // Método para buscar logs
  async getLogs(
    type: string = 'app',
    date?: string,
    limit: number = 100
  ): Promise<LogEntry[]> {
    try {
      const fileName = date 
        ? path.join(this.logDir, `${type}-${date}.log`)
        : this.getLogFileName(type);

      if (!fs.existsSync(fileName)) {
        return [];
      }

      const content = fs.readFileSync(fileName, 'utf8');
      const lines = content.trim().split('\n').filter(line => line.length > 0);
      
      const logs = lines
        .slice(-limit) // Pegar as últimas N linhas
        .map(line => {
          try {
            return JSON.parse(line) as LogEntry;
          } catch {
            return null;
          }
        })
        .filter(log => log !== null) as LogEntry[];

      return logs.reverse(); // Mais recentes primeiro
    } catch (error) {
      console.error('Failed to read logs:', error);
      return [];
    }
  }

  // Método para obter estatísticas de logs
  async getLogStats(date?: string): Promise<any> {
    try {
      const types = ['app', 'error', 'security', 'audit', 'access', 'performance'];
      const stats: any = {};

      for (const type of types) {
        const logs = await this.getLogs(type, date, 1000);
        stats[type] = {
          total: logs.length,
          levels: logs.reduce((acc, log) => {
            acc[log.level] = (acc[log.level] || 0) + 1;
            return acc;
          }, {} as Record<string, number>),
        };
      }

      return stats;
    } catch (error) {
      console.error('Failed to get log stats:', error);
      return {};
    }
  }
}

// Instância singleton
export const logger = Logger.getInstance();

// Capturar erros não tratados
process.on('uncaughtException', (error) => {
  logger.captureException(error, 'uncaughtException');
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  const error = reason instanceof Error ? reason : new Error(String(reason));
  logger.captureException(error, 'unhandledRejection', {
    data: { promise: promise.toString() }
  });
});
