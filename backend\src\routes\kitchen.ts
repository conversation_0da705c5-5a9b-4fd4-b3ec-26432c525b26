import { Router } from 'express';
import { KitchenController } from '../controllers/KitchenController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para pedidos da cozinha
const createKitchenOrderValidation = [
  body('order_id')
    .isInt({ min: 1 })
    .withMessage('Order ID must be a positive integer'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('estimated_time')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Estimated time must be 0 or greater'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
];

const updateKitchenOrderValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Kitchen order ID must be a positive integer'),
  body('status')
    .optional()
    .isIn(['pending', 'preparing', 'ready', 'delivered', 'cancelled'])
    .withMessage('Invalid kitchen order status'),
  body('priority')
    .optional()
    .isIn(['low', 'normal', 'high', 'urgent'])
    .withMessage('Invalid priority'),
  body('estimated_time')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Estimated time must be 0 or greater'),
  body('actual_time')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Actual time must be 0 or greater'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
];

const updateKitchenItemValidation = [
  param('itemId')
    .isInt({ min: 1 })
    .withMessage('Kitchen order item ID must be a positive integer'),
  body('status')
    .optional()
    .isIn(['pending', 'preparing', 'ready', 'delivered'])
    .withMessage('Invalid kitchen item status'),
  body('station_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Station ID must be a positive integer'),
  body('notes')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Notes must be at most 200 characters'),
];

// Validações para estações
const createStationValidation = [
  body('name')
    .notEmpty()
    .withMessage('Station name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Station name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be at most 500 characters'),
  body('printer_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Printer name must be at most 100 characters'),
  body('order_priority')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Order priority must be a positive integer'),
];

const updateStationValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Station ID must be a positive integer'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Station name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be at most 500 characters'),
  body('printer_name')
    .optional()
    .isLength({ max: 100 })
    .withMessage('Printer name must be at most 100 characters'),
  body('order_priority')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Order priority must be a positive integer'),
];

// ===== ROTAS DE PEDIDOS DA COZINHA =====

/**
 * @route GET /api/kitchen/orders
 * @desc Listar pedidos da cozinha
 * @access Private (read kitchen)
 */
router.get('/orders',
  authenticate,
  authorize('kitchen', 'read'),
  KitchenController.getOrders
);

/**
 * @route GET /api/kitchen/orders/active
 * @desc Buscar pedidos ativos da cozinha
 * @access Private (read kitchen)
 */
router.get('/orders/active',
  authenticate,
  authorize('kitchen', 'read'),
  KitchenController.getActiveOrders
);

/**
 * @route GET /api/kitchen/orders/ready
 * @desc Buscar pedidos prontos da cozinha
 * @access Private (read kitchen)
 */
router.get('/orders/ready',
  authenticate,
  authorize('kitchen', 'read'),
  KitchenController.getReadyOrders
);

/**
 * @route GET /api/kitchen/metrics
 * @desc Obter métricas da cozinha
 * @access Private (read kitchen)
 */
router.get('/metrics',
  authenticate,
  authorize('kitchen', 'read'),
  KitchenController.getMetrics
);

/**
 * @route GET /api/kitchen/orders/:id
 * @desc Buscar pedido da cozinha por ID
 * @access Private (read kitchen)
 */
router.get('/orders/:id',
  authenticate,
  authorize('kitchen', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  KitchenController.getOrder
);

/**
 * @route POST /api/kitchen/orders
 * @desc Criar pedido da cozinha
 * @access Private (create kitchen)
 */
router.post('/orders',
  authenticate,
  authorize('kitchen', 'create'),
  createKitchenOrderValidation,
  validateRequest,
  logUserAction('create_kitchen_order'),
  KitchenController.createOrder
);

/**
 * @route PUT /api/kitchen/orders/:id
 * @desc Atualizar pedido da cozinha
 * @access Private (update kitchen)
 */
router.put('/orders/:id',
  authenticate,
  authorize('kitchen', 'update'),
  updateKitchenOrderValidation,
  validateRequest,
  logUserAction('update_kitchen_order'),
  KitchenController.updateOrder
);

/**
 * @route GET /api/kitchen/orders/:id/items
 * @desc Buscar itens do pedido da cozinha
 * @access Private (read kitchen)
 */
router.get('/orders/:id/items',
  authenticate,
  authorize('kitchen', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  KitchenController.getOrderItems
);

/**
 * @route PUT /api/kitchen/orders/:id/items/:itemId
 * @desc Atualizar item do pedido da cozinha
 * @access Private (update kitchen)
 */
router.put('/orders/:id/items/:itemId',
  authenticate,
  authorize('kitchen', 'update'),
  updateKitchenItemValidation,
  validateRequest,
  logUserAction('update_kitchen_item'),
  KitchenController.updateOrderItem
);

// ===== ROTAS DE ESTAÇÕES DA COZINHA =====

/**
 * @route GET /api/kitchen/stations
 * @desc Listar estações da cozinha
 * @access Private (read kitchen)
 */
router.get('/stations',
  authenticate,
  authorize('kitchen', 'read'),
  KitchenController.getStations
);

/**
 * @route GET /api/kitchen/stations/:id
 * @desc Buscar estação da cozinha por ID
 * @access Private (read kitchen)
 */
router.get('/stations/:id',
  authenticate,
  authorize('kitchen', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  KitchenController.getStation
);

export default router;
