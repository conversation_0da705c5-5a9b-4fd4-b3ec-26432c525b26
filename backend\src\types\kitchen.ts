// Tipos para cozinha e produção

export interface KitchenStation {
  id: number;
  name: string;
  description?: string;
  printer_name?: string;
  is_active: boolean;
  order_priority: number;
  created_at: string;
  updated_at: string;
}

export interface CreateKitchenStationRequest {
  name: string;
  description?: string;
  printer_name?: string;
  order_priority?: number;
}

export interface UpdateKitchenStationRequest {
  name?: string;
  description?: string;
  printer_name?: string;
  is_active?: boolean;
  order_priority?: number;
}

export interface KitchenOrder {
  id: number;
  order_id: number;
  order_number: string;
  order_type: 'counter' | 'table' | 'delivery';
  table_number?: number;
  customer_name?: string;
  status: KitchenOrderStatus;
  priority: KitchenOrderPriority;
  estimated_time: number;
  actual_time?: number;
  started_at?: string;
  finished_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  items?: KitchenOrderItem[];
}

export type KitchenOrderStatus = 'pending' | 'preparing' | 'ready' | 'delivered' | 'cancelled';
export type KitchenOrderPriority = 'low' | 'normal' | 'high' | 'urgent';

export interface KitchenOrderItem {
  id: number;
  kitchen_order_id: number;
  order_item_id: number;
  product_id: number;
  product_name: string;
  quantity: number;
  status: KitchenItemStatus;
  station_id?: number;
  station_name?: string;
  preparation_time: number;
  started_at?: string;
  finished_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type KitchenItemStatus = 'pending' | 'preparing' | 'ready' | 'delivered';

export interface CreateKitchenOrderRequest {
  order_id: number;
  priority?: KitchenOrderPriority;
  estimated_time?: number;
  notes?: string;
}

export interface UpdateKitchenOrderRequest {
  status?: KitchenOrderStatus;
  priority?: KitchenOrderPriority;
  estimated_time?: number;
  actual_time?: number;
  notes?: string;
}

export interface UpdateKitchenItemRequest {
  status?: KitchenItemStatus;
  station_id?: number;
  notes?: string;
}

export interface ProductionQueue {
  id: number;
  product_id: number;
  product_name: string;
  quantity_needed: number;
  quantity_produced: number;
  quantity_remaining: number;
  priority: ProductionPriority;
  estimated_time: number;
  status: ProductionStatus;
  assigned_user_id?: number;
  assigned_user_name?: string;
  started_at?: string;
  finished_at?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export type ProductionPriority = 'low' | 'normal' | 'high' | 'urgent';
export type ProductionStatus = 'pending' | 'in_progress' | 'completed' | 'cancelled';

export interface CreateProductionQueueRequest {
  product_id: number;
  quantity_needed: number;
  priority?: ProductionPriority;
  estimated_time?: number;
  notes?: string;
}

export interface UpdateProductionQueueRequest {
  quantity_produced?: number;
  priority?: ProductionPriority;
  status?: ProductionStatus;
  assigned_user_id?: number;
  estimated_time?: number;
  notes?: string;
}

export interface KitchenDisplay {
  id: number;
  name: string;
  station_id?: number;
  display_type: KitchenDisplayType;
  layout: KitchenDisplayLayout;
  auto_refresh_interval: number;
  show_completed_time: number;
  max_items_per_page: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export type KitchenDisplayType = 'orders' | 'items' | 'production' | 'summary';
export type KitchenDisplayLayout = 'grid' | 'list' | 'kanban';

export interface CreateKitchenDisplayRequest {
  name: string;
  station_id?: number;
  display_type: KitchenDisplayType;
  layout?: KitchenDisplayLayout;
  auto_refresh_interval?: number;
  show_completed_time?: number;
  max_items_per_page?: number;
}

export interface UpdateKitchenDisplayRequest {
  name?: string;
  station_id?: number;
  display_type?: KitchenDisplayType;
  layout?: KitchenDisplayLayout;
  auto_refresh_interval?: number;
  show_completed_time?: number;
  max_items_per_page?: number;
  is_active?: boolean;
}

// Filtros
export interface KitchenOrderFilters {
  status?: KitchenOrderStatus;
  priority?: KitchenOrderPriority;
  order_type?: 'counter' | 'table' | 'delivery';
  station_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface ProductionQueueFilters {
  status?: ProductionStatus;
  priority?: ProductionPriority;
  product_id?: number;
  assigned_user_id?: number;
  date_from?: string;
  date_to?: string;
}

// Relatórios
export interface KitchenPerformanceReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_orders: number;
    completed_orders: number;
    cancelled_orders: number;
    average_preparation_time: number;
    on_time_percentage: number;
  };
  by_station: Array<{
    station_id: number;
    station_name: string;
    orders_processed: number;
    average_time: number;
    efficiency_score: number;
  }>;
  by_product: Array<{
    product_id: number;
    product_name: string;
    quantity_produced: number;
    average_time: number;
    total_time: number;
  }>;
  peak_hours: Array<{
    hour: number;
    orders_count: number;
    average_time: number;
  }>;
}

export interface ProductionReport {
  period: {
    start: string;
    end: string;
  };
  summary: {
    total_items_produced: number;
    total_production_time: number;
    average_time_per_item: number;
    efficiency_percentage: number;
  };
  by_product: Array<{
    product_id: number;
    product_name: string;
    quantity_produced: number;
    total_time: number;
    average_time: number;
    waste_percentage: number;
  }>;
  by_user: Array<{
    user_id: number;
    user_name: string;
    items_produced: number;
    total_time: number;
    efficiency_score: number;
  }>;
}

// Configurações de cozinha
export interface KitchenSettings {
  auto_start_orders: boolean;
  default_preparation_time: number;
  max_concurrent_orders: number;
  priority_boost_minutes: number;
  auto_complete_items: boolean;
  sound_notifications: boolean;
  display_customer_names: boolean;
  show_order_notes: boolean;
  group_by_station: boolean;
  color_code_priority: boolean;
}

// Notificações de cozinha
export interface KitchenNotification {
  id: string;
  type: KitchenNotificationType;
  title: string;
  message: string;
  order_id?: number;
  item_id?: number;
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: string;
  read: boolean;
  auto_dismiss_after?: number;
}

export type KitchenNotificationType = 
  | 'new_order' 
  | 'order_ready' 
  | 'order_delayed' 
  | 'station_alert' 
  | 'production_complete'
  | 'inventory_low';

// Métricas em tempo real
export interface KitchenMetrics {
  active_orders: number;
  pending_items: number;
  preparing_items: number;
  ready_items: number;
  average_wait_time: number;
  longest_wait_time: number;
  orders_per_hour: number;
  efficiency_score: number;
  station_utilization: Array<{
    station_id: number;
    station_name: string;
    utilization_percentage: number;
    active_items: number;
  }>;
}

// Tempos de preparo
export interface PreparationTime {
  id: number;
  product_id: number;
  station_id?: number;
  base_time: number;
  complexity_factor: number;
  quantity_factor: number;
  rush_hour_factor: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreatePreparationTimeRequest {
  product_id: number;
  station_id?: number;
  base_time: number;
  complexity_factor?: number;
  quantity_factor?: number;
  rush_hour_factor?: number;
}

export interface UpdatePreparationTimeRequest {
  base_time?: number;
  complexity_factor?: number;
  quantity_factor?: number;
  rush_hour_factor?: number;
  is_active?: boolean;
}
