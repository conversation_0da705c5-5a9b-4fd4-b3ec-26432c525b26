#!/usr/bin/env node

/**
 * Script para testar as APIs de cozinha
 * Testa CRUD de pedidos da cozinha e estações
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar estações da cozinha
async function testKitchenStations() {
  log('\n🏪 Testando Estações da Cozinha...', 'blue');

  try {
    // Listar estações
    log('Listando estações...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/kitchen/stations`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} estações encontradas`, 'green');

    // Listar estações com carga de trabalho
    log('Listando estações com carga de trabalho...', 'cyan');
    const workloadResponse = await axios.get(`${API_BASE_URL}/kitchen/stations?with_workload=true`, {
      headers: getHeaders()
    });
    log(`✅ Estações com carga de trabalho carregadas`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de estações: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar pedidos da cozinha
async function testKitchenOrders() {
  log('\n🍳 Testando Pedidos da Cozinha...', 'blue');

  try {
    // Listar pedidos da cozinha
    log('Listando pedidos da cozinha...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/kitchen/orders`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} pedidos da cozinha encontrados`, 'green');

    // Buscar pedidos ativos
    log('Buscando pedidos ativos...', 'cyan');
    const activeResponse = await axios.get(`${API_BASE_URL}/kitchen/orders/active`, {
      headers: getHeaders()
    });
    log(`✅ ${activeResponse.data.data.length} pedidos ativos`, 'green');

    // Buscar pedidos prontos
    log('Buscando pedidos prontos...', 'cyan');
    const readyResponse = await axios.get(`${API_BASE_URL}/kitchen/orders/ready`, {
      headers: getHeaders()
    });
    log(`✅ ${readyResponse.data.data.length} pedidos prontos`, 'green');

    // Obter métricas da cozinha
    log('Obtendo métricas da cozinha...', 'cyan');
    const metricsResponse = await axios.get(`${API_BASE_URL}/kitchen/metrics`, {
      headers: getHeaders()
    });
    const metrics = metricsResponse.data.data;
    log(`✅ Métricas obtidas:`, 'green');
    log(`   - Pedidos ativos: ${metrics.active_orders}`, 'cyan');
    log(`   - Itens pendentes: ${metrics.pending_items}`, 'cyan');
    log(`   - Itens preparando: ${metrics.preparing_items}`, 'cyan');
    log(`   - Itens prontos: ${metrics.ready_items}`, 'cyan');
    log(`   - Tempo médio: ${metrics.average_wait_time.toFixed(1)}min`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de pedidos da cozinha: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar filtros de pedidos da cozinha
async function testKitchenOrderFilters() {
  log('\n🔍 Testando Filtros de Pedidos da Cozinha...', 'blue');

  try {
    // Filtrar por status
    log('Filtrando pedidos por status...', 'cyan');
    const statusFilterResponse = await axios.get(`${API_BASE_URL}/kitchen/orders?status=pending`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por status funcionando`, 'green');

    // Filtrar por prioridade
    log('Filtrando pedidos por prioridade...', 'cyan');
    const priorityFilterResponse = await axios.get(`${API_BASE_URL}/kitchen/orders?priority=high`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por prioridade funcionando`, 'green');

    // Filtrar por tipo de pedido
    log('Filtrando pedidos por tipo...', 'cyan');
    const typeFilterResponse = await axios.get(`${API_BASE_URL}/kitchen/orders?order_type=delivery`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por tipo funcionando`, 'green');

    // Filtrar por data
    log('Filtrando pedidos por data...', 'cyan');
    const today = new Date().toISOString().slice(0, 10);
    const dateFilterResponse = await axios.get(`${API_BASE_URL}/kitchen/orders?date_from=${today}&date_to=${today}`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por data funcionando`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de filtros: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar fluxo completo de pedido da cozinha
async function testKitchenOrderFlow() {
  log('\n🔄 Testando Fluxo Completo de Pedido da Cozinha...', 'blue');

  try {
    // Primeiro, criar um pedido normal
    log('Criando pedido normal...', 'cyan');
    const orderData = {
      type: 'counter',
      items: [
        {
          product_id: 1,
          quantity: 1,
          unit_price: 15.90
        }
      ],
      notes: 'Teste de fluxo da cozinha'
    };

    const createOrderResponse = await axios.post(`${API_BASE_URL}/sales/orders`, orderData, {
      headers: getHeaders()
    });
    
    const orderId = createOrderResponse.data.data.id;
    log(`✅ Pedido criado com ID: ${orderId}`, 'green');

    // Criar pedido da cozinha
    log('Criando pedido da cozinha...', 'cyan');
    const kitchenOrderData = {
      order_id: orderId,
      priority: 'high',
      estimated_time: 15,
      notes: 'Teste de pedido da cozinha'
    };

    const createKitchenOrderResponse = await axios.post(`${API_BASE_URL}/kitchen/orders`, kitchenOrderData, {
      headers: getHeaders()
    });
    
    const kitchenOrderId = createKitchenOrderResponse.data.data.id;
    log(`✅ Pedido da cozinha criado com ID: ${kitchenOrderId}`, 'green');

    // Buscar pedido da cozinha por ID
    log('Buscando pedido da cozinha por ID...', 'cyan');
    const getKitchenOrderResponse = await axios.get(`${API_BASE_URL}/kitchen/orders/${kitchenOrderId}?include_items=true`, {
      headers: getHeaders()
    });
    log(`✅ Pedido da cozinha encontrado`, 'green');

    // Buscar itens do pedido da cozinha
    log('Buscando itens do pedido da cozinha...', 'cyan');
    const itemsResponse = await axios.get(`${API_BASE_URL}/kitchen/orders/${kitchenOrderId}/items`, {
      headers: getHeaders()
    });
    const items = itemsResponse.data.data;
    log(`✅ ${items.length} itens encontrados`, 'green');

    // Atualizar status do pedido para "preparando"
    log('Iniciando preparo do pedido...', 'cyan');
    await axios.put(`${API_BASE_URL}/kitchen/orders/${kitchenOrderId}`, {
      status: 'preparing'
    }, {
      headers: getHeaders()
    });
    log('✅ Pedido marcado como "preparando"', 'green');

    // Atualizar item para "pronto"
    if (items.length > 0) {
      log('Marcando item como pronto...', 'cyan');
      await axios.put(`${API_BASE_URL}/kitchen/orders/${kitchenOrderId}/items/${items[0].id}`, {
        status: 'ready'
      }, {
        headers: getHeaders()
      });
      log('✅ Item marcado como pronto', 'green');
    }

    // Atualizar status do pedido para "pronto"
    log('Marcando pedido como pronto...', 'cyan');
    await axios.put(`${API_BASE_URL}/kitchen/orders/${kitchenOrderId}`, {
      status: 'ready',
      actual_time: 12
    }, {
      headers: getHeaders()
    });
    log('✅ Pedido marcado como pronto', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro no fluxo de pedido da cozinha: ${error.response?.data?.error || error.message}`, 'red');
    if (error.response?.data?.details) {
      console.log('Detalhes:', error.response.data.details);
    }
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar criar pedido da cozinha sem order_id
    log('Testando pedido da cozinha sem order_id...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/kitchen/orders`, {
        priority: 'normal'
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de order_id funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar criar pedido da cozinha com order_id inexistente
    log('Testando order_id inexistente...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/kitchen/orders`, {
        order_id: 99999,
        priority: 'normal'
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 500) {
        log('✅ Validação de order_id existente funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar atualizar com status inválido
    log('Testando status inválido...', 'cyan');
    try {
      await axios.put(`${API_BASE_URL}/kitchen/orders/1`, {
        status: 'invalid_status'
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de status funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs de Cozinha - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      stations: await testKitchenStations(),
      orders: await testKitchenOrders(),
      filters: await testKitchenOrderFilters(),
      flow: await testKitchenOrderFlow(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs de cozinha funcionando corretamente.', 'green');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
