const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expor APIs seguras para o renderer process
contextBridge.exposeInMainWorld('electronAPI', {
  // Informações da aplicação
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),
  
  // Diálogos do sistema
  showSaveDialog: () => ipcRenderer.invoke('show-save-dialog'),
  showOpenDialog: () => ipcRenderer.invoke('show-open-dialog'),
  
  // Eventos do menu
  onMenuAction: (callback) => {
    ipcRenderer.on('menu-action', (event, action) => callback(action));
  },
  
  // Remover listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },
  
  // Notificações do sistema
  showNotification: (title, body) => {
    new Notification(title, { body });
  },
  
  // Impressão
  print: () => {
    window.print();
  },
  
  // Abrir links externos
  openExternal: (url) => {
    ipcRenderer.invoke('open-external', url);
  }
});

// Expor APIs para desenvolvimento (apenas em modo dev)
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('electronDev', {
    openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
    reloadApp: () => ipcRenderer.invoke('reload-app')
  });
}
