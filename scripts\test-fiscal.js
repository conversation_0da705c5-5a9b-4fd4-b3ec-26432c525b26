#!/usr/bin/env node

/**
 * Script para testar as APIs fiscais
 * Testa CRUD de documentos fiscais e configurações
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar configurações fiscais
async function testFiscalConfigs() {
  log('\n⚙️ Testando Configurações Fiscais...', 'blue');

  try {
    // Listar configurações
    log('Listando configurações fiscais...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/fiscal/configs`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} configurações encontradas`, 'green');

    // Buscar configuração ativa
    log('Buscando configuração ativa...', 'cyan');
    const activeResponse = await axios.get(`${API_BASE_URL}/fiscal/configs/active`, {
      headers: getHeaders()
    });
    
    if (activeResponse.data.data) {
      log(`✅ Configuração ativa encontrada: ${activeResponse.data.data.company_name}`, 'green');
    } else {
      log(`✅ Nenhuma configuração ativa (esperado)`, 'green');
    }

    // Validar configuração
    log('Validando configuração fiscal...', 'cyan');
    const validateResponse = await axios.get(`${API_BASE_URL}/fiscal/configs/validate`, {
      headers: getHeaders()
    });
    const validation = validateResponse.data.data;
    log(`✅ Validação concluída:`, 'green');
    log(`   - Válida: ${validation.valid}`, 'cyan');
    log(`   - Erros: ${validation.errors.length}`, 'cyan');
    log(`   - Avisos: ${validation.warnings.length}`, 'cyan');

    // Testar conexão SEFAZ
    log('Testando conexão com SEFAZ...', 'cyan');
    const sefazResponse = await axios.get(`${API_BASE_URL}/fiscal/configs/test-sefaz`, {
      headers: getHeaders()
    });
    const sefazTest = sefazResponse.data.data;
    log(`✅ Teste SEFAZ:`, 'green');
    log(`   - Sucesso: ${sefazTest.success}`, 'cyan');
    log(`   - Tempo: ${sefazTest.response_time}s`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de configurações: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar documentos fiscais
async function testFiscalDocuments() {
  log('\n📄 Testando Documentos Fiscais...', 'blue');

  try {
    // Listar documentos
    log('Listando documentos fiscais...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/fiscal/documents`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} documentos encontrados`, 'green');

    // Obter métricas
    log('Obtendo métricas fiscais...', 'cyan');
    const metricsResponse = await axios.get(`${API_BASE_URL}/fiscal/metrics`, {
      headers: getHeaders()
    });
    const metrics = metricsResponse.data.data;
    log(`✅ Métricas obtidas:`, 'green');
    log(`   - Documentos hoje: ${metrics.documents_today}`, 'cyan');
    log(`   - Pendentes: ${metrics.documents_pending}`, 'cyan');
    log(`   - Taxa autorização: ${metrics.authorization_rate.toFixed(1)}%`, 'cyan');
    log(`   - Status SEFAZ: ${metrics.sefaz_status}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de documentos: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar fluxo completo de documento fiscal
async function testFiscalDocumentFlow() {
  log('\n🔄 Testando Fluxo Completo de Documento Fiscal...', 'blue');

  try {
    // Criar documento fiscal
    log('Criando documento fiscal...', 'cyan');
    const documentData = {
      type: 'nfce',
      order_id: 1,
      items: [
        {
          product_id: 1,
          quantity: 2,
          unit_price: 15.90,
          cfop: '5102',
          tax_rate: 18.0
        },
        {
          product_id: 2,
          quantity: 1,
          unit_price: 8.50,
          cfop: '5102',
          tax_rate: 18.0
        }
      ],
      notes: 'Teste de emissão de NFC-e'
    };

    const createResponse = await axios.post(`${API_BASE_URL}/fiscal/documents`, documentData, {
      headers: getHeaders()
    });
    
    const documentId = createResponse.data.data.id;
    const accessKey = createResponse.data.data.access_key;
    log(`✅ Documento fiscal criado com ID: ${documentId}`, 'green');
    log(`   - Chave de acesso: ${accessKey}`, 'cyan');

    // Buscar documento por ID
    log('Buscando documento por ID...', 'cyan');
    const getDocumentResponse = await axios.get(`${API_BASE_URL}/fiscal/documents/${documentId}?include_items=true`, {
      headers: getHeaders()
    });
    log(`✅ Documento encontrado: ${getDocumentResponse.data.data.access_key}`, 'green');

    // Buscar itens do documento
    log('Buscando itens do documento...', 'cyan');
    const itemsResponse = await axios.get(`${API_BASE_URL}/fiscal/documents/${documentId}/items`, {
      headers: getHeaders()
    });
    const items = itemsResponse.data.data;
    log(`✅ ${items.length} itens encontrados`, 'green');

    // Atualizar status do documento para "autorizado"
    log('Autorizando documento...', 'cyan');
    await axios.put(`${API_BASE_URL}/fiscal/documents/${documentId}`, {
      status: 'authorized',
      protocol: '135240000000123',
      xml_content: '<xml>Conteúdo XML simulado</xml>',
      qr_code: 'https://www.fazenda.sp.gov.br/nfce/qrcode?p=...'
    }, {
      headers: getHeaders()
    });
    log('✅ Documento autorizado', 'green');

    // Cancelar documento
    log('Cancelando documento...', 'cyan');
    await axios.delete(`${API_BASE_URL}/fiscal/documents/${documentId}`, {
      data: {
        reason: 'Teste de cancelamento - documento criado para testes'
      },
      headers: getHeaders()
    });
    log('✅ Documento cancelado', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro no fluxo de documento fiscal: ${error.response?.data?.error || error.message}`, 'red');
    if (error.response?.data?.details) {
      console.log('Detalhes:', error.response.data.details);
    }
    return false;
  }
}

// Testar filtros de documentos
async function testDocumentFilters() {
  log('\n🔍 Testando Filtros de Documentos...', 'blue');

  try {
    // Filtrar por tipo
    log('Filtrando documentos por tipo...', 'cyan');
    const typeFilterResponse = await axios.get(`${API_BASE_URL}/fiscal/documents?type=nfce`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por tipo funcionando`, 'green');

    // Filtrar por status
    log('Filtrando documentos por status...', 'cyan');
    const statusFilterResponse = await axios.get(`${API_BASE_URL}/fiscal/documents?status=authorized`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por status funcionando`, 'green');

    // Filtrar por data
    log('Filtrando documentos por data...', 'cyan');
    const today = new Date().toISOString().slice(0, 10);
    const dateFilterResponse = await axios.get(`${API_BASE_URL}/fiscal/documents?date_from=${today}&date_to=${today}`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por data funcionando`, 'green');

    // Buscar por texto
    log('Buscando documentos por texto...', 'cyan');
    const searchResponse = await axios.get(`${API_BASE_URL}/fiscal/documents?search=teste`, {
      headers: getHeaders()
    });
    log(`✅ Busca por texto funcionando`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de filtros: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar criar documento sem tipo
    log('Testando documento sem tipo...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/fiscal/documents`, {
        items: [
          {
            product_id: 1,
            quantity: 1,
            unit_price: 10.00
          }
        ]
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de tipo funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar criar documento sem itens
    log('Testando documento sem itens...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/fiscal/documents`, {
        type: 'nfce',
        items: []
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de itens funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar cancelar com motivo muito curto
    log('Testando cancelamento com motivo inválido...', 'cyan');
    try {
      await axios.delete(`${API_BASE_URL}/fiscal/documents/1`, {
        data: {
          reason: 'abc'
        },
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de motivo de cancelamento funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs Fiscais - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      configs: await testFiscalConfigs(),
      documents: await testFiscalDocuments(),
      flow: await testFiscalDocumentFlow(),
      filters: await testDocumentFilters(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs fiscais funcionando corretamente.', 'green');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
