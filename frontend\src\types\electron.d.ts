// Tipos para integração com Electron

export interface ElectronAPI {
  // Informações da aplicação
  getAppVersion: () => Promise<string>;
  
  // Diálogos do sistema
  showSaveDialog: () => Promise<{
    canceled: boolean;
    filePath?: string;
  }>;
  showOpenDialog: () => Promise<{
    canceled: boolean;
    filePaths: string[];
  }>;
  
  // Eventos do menu
  onMenuAction: (callback: (action: string) => void) => void;
  
  // Remover listeners
  removeAllListeners: (channel: string) => void;
  
  // Notificações do sistema
  showNotification: (title: string, body: string) => void;
  
  // Impressão
  print: () => void;
  
  // Abrir links externos
  openExternal: (url: string) => void;
}

export interface ElectronDev {
  openDevTools: () => Promise<void>;
  reloadApp: () => Promise<void>;
}

declare global {
  interface Window {
    electronAPI?: ElectronAPI;
    electronDev?: ElectronDev;
  }
}
