import { Request, Response } from 'express';
import { ProductModel } from '../models/Product';
import { CategoryModel } from '../models/Category';
import { RecipeModel } from '../models/Recipe';
import { StockMovementModel } from '../models/StockMovement';
import { 
  CreateProductRequest, 
  UpdateProductRequest, 
  ProductFilters,
  AuthenticatedRequest,
  ValidationError,
  NotFoundError,
  ApiResponse 
} from '../types';

export class ProductController {
  // Listar produtos
  static async index(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: ProductFilters = {
        category_id: req.query.category_id ? parseInt(req.query.category_id as string) : undefined,
        is_active: req.query.is_active !== undefined ? req.query.is_active === 'true' : undefined,
        stock_control: req.query.stock_control !== undefined ? req.query.stock_control === 'true' : undefined,
        low_stock: req.query.low_stock === 'true',
        search: req.query.search as string,
      };

      const products = await ProductModel.findAll(filters);

      res.status(200).json({
        success: true,
        data: products,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get products error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get products',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar produto por ID
  static async show(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      
      if (isNaN(id)) {
        throw new ValidationError('Invalid product ID');
      }

      const product = await ProductModel.findById(id);
      if (!product) {
        throw new NotFoundError('Product not found');
      }

      res.status(200).json({
        success: true,
        data: product,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get product error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get product',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Criar produto
  static async create(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const productData: CreateProductRequest = req.body;

      // Validações básicas
      if (!productData.name) {
        throw new ValidationError('Product name is required');
      }

      if (!productData.price || productData.price <= 0) {
        throw new ValidationError('Product price must be greater than 0');
      }

      // Verificar se categoria existe (se fornecida)
      if (productData.category_id) {
        const category = await CategoryModel.findById(productData.category_id);
        if (!category) {
          throw new ValidationError('Category not found');
        }
      }

      const product = await ProductModel.create(productData);

      console.log(`Product ${product.name} created by user ${req.user?.username}`);

      res.status(201).json({
        success: true,
        data: product,
        message: 'Product created successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Create product error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to create product',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Atualizar produto
  static async update(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const productData: UpdateProductRequest = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid product ID');
      }

      // Verificar se categoria existe (se fornecida)
      if (productData.category_id) {
        const category = await CategoryModel.findById(productData.category_id);
        if (!category) {
          throw new ValidationError('Category not found');
        }
      }

      const product = await ProductModel.update(id, productData);

      console.log(`Product ${product.name} updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: product,
        message: 'Product updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update product error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update product',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Deletar produto
  static async delete(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid product ID');
      }

      await ProductModel.delete(id);

      console.log(`Product ${id} deleted by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        message: 'Product deleted successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Delete product error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to delete product',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar produtos com estoque baixo
  static async lowStock(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const products = await ProductModel.findLowStock();

      res.status(200).json({
        success: true,
        data: products,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get low stock products error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get low stock products',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Buscar produtos mais vendidos
  static async topSelling(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const limit = parseInt(req.query.limit as string) || 10;
      const products = await ProductModel.findTopSelling(limit);

      res.status(200).json({
        success: true,
        data: products,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get top selling products error:', error);
      res.status(500).json({
        success: false,
        error: 'Failed to get top selling products',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    }
  }

  // Atualizar estoque
  static async updateStock(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);
      const { quantity, operation, reason } = req.body;

      if (isNaN(id)) {
        throw new ValidationError('Invalid product ID');
      }

      if (!quantity || quantity <= 0) {
        throw new ValidationError('Quantity must be greater than 0');
      }

      if (!['add', 'subtract', 'set'].includes(operation)) {
        throw new ValidationError('Invalid operation. Must be add, subtract, or set');
      }

      // Atualizar estoque
      await ProductModel.updateStock(id, quantity, operation);

      // Registrar movimentação
      const movementType = operation === 'add' ? 'in' : operation === 'subtract' ? 'out' : 'adjustment';
      await StockMovementModel.create({
        type: 'product',
        item_id: id,
        movement_type: movementType,
        quantity: operation === 'set' ? quantity : quantity,
        reason: reason || `Stock ${operation} via API`,
      }, req.user!.id);

      const product = await ProductModel.findById(id);

      console.log(`Product ${id} stock updated by user ${req.user?.username}`);

      res.status(200).json({
        success: true,
        data: product,
        message: 'Stock updated successfully',
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Update stock error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: error instanceof Error ? error.message : 'Failed to update stock',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }

  // Buscar produto com receita
  static async getWithRecipe(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const id = parseInt(req.params.id);

      if (isNaN(id)) {
        throw new ValidationError('Invalid product ID');
      }

      const productWithRecipe = await RecipeModel.getProductWithRecipe(id);
      if (!productWithRecipe) {
        throw new NotFoundError('Product not found');
      }

      res.status(200).json({
        success: true,
        data: productWithRecipe,
        timestamp: new Date().toISOString(),
      } as ApiResponse);

    } catch (error) {
      console.error('Get product with recipe error:', error);

      if (error instanceof ValidationError) {
        res.status(400).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else if (error instanceof NotFoundError) {
        res.status(404).json({
          success: false,
          error: error.message,
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      } else {
        res.status(500).json({
          success: false,
          error: 'Failed to get product with recipe',
          timestamp: new Date().toISOString(),
        } as ApiResponse);
      }
    }
  }
}
