/**
 * Sistema de Cache Inteligente
 * Gerencia cache com TTL, invalidação automática e estratégias de cache
 */

interface CacheEntry<T> {
  data: T;
  timestamp: number;
  ttl: number;
  accessCount: number;
  lastAccess: number;
  tags: string[];
  priority: number;
}

interface CacheOptions {
  ttl?: number; // Time to live em ms
  tags?: string[]; // Tags para invalidação em grupo
  priority?: number; // Prioridade (1-10, maior = mais importante)
  strategy?: 'lru' | 'lfu' | 'fifo'; // Estratégia de remoção
}

interface CacheStats {
  hits: number;
  misses: number;
  size: number;
  maxSize: number;
  hitRate: number;
  memoryUsage: number;
}

class SmartCache {
  private cache = new Map<string, CacheEntry<any>>();
  private maxSize: number;
  private defaultTTL: number;
  private stats: CacheStats;
  private cleanupInterval: NodeJS.Timeout;

  constructor(maxSize: number = 1000, defaultTTL: number = 5 * 60 * 1000) {
    this.maxSize = maxSize;
    this.defaultTTL = defaultTTL;
    this.stats = {
      hits: 0,
      misses: 0,
      size: 0,
      maxSize,
      hitRate: 0,
      memoryUsage: 0,
    };

    // Limpeza automática a cada 5 minutos
    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, 5 * 60 * 1000);
  }

  // ===== OPERAÇÕES BÁSICAS =====

  set<T>(key: string, data: T, options: CacheOptions = {}): void {
    const now = Date.now();
    const ttl = options.ttl || this.defaultTTL;
    
    const entry: CacheEntry<T> = {
      data,
      timestamp: now,
      ttl,
      accessCount: 0,
      lastAccess: now,
      tags: options.tags || [],
      priority: options.priority || 5,
    };

    // Verificar se precisa fazer espaço
    if (this.cache.size >= this.maxSize && !this.cache.has(key)) {
      this.evict(options.strategy || 'lru');
    }

    this.cache.set(key, entry);
    this.updateStats();
  }

  get<T>(key: string): T | null {
    const entry = this.cache.get(key);
    
    if (!entry) {
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Verificar se expirou
    if (this.isExpired(entry)) {
      this.cache.delete(key);
      this.stats.misses++;
      this.updateStats();
      return null;
    }

    // Atualizar estatísticas de acesso
    entry.accessCount++;
    entry.lastAccess = Date.now();
    
    this.stats.hits++;
    this.updateStats();
    
    return entry.data;
  }

  has(key: string): boolean {
    const entry = this.cache.get(key);
    return entry !== undefined && !this.isExpired(entry);
  }

  delete(key: string): boolean {
    const deleted = this.cache.delete(key);
    if (deleted) {
      this.updateStats();
    }
    return deleted;
  }

  clear(): void {
    this.cache.clear();
    this.stats.hits = 0;
    this.stats.misses = 0;
    this.updateStats();
  }

  // ===== OPERAÇÕES AVANÇADAS =====

  // Invalidar por tags
  invalidateByTag(tag: string): number {
    let count = 0;
    for (const [key, entry] of this.cache.entries()) {
      if (entry.tags.includes(tag)) {
        this.cache.delete(key);
        count++;
      }
    }
    this.updateStats();
    return count;
  }

  // Invalidar por padrão de chave
  invalidateByPattern(pattern: RegExp): number {
    let count = 0;
    for (const key of this.cache.keys()) {
      if (pattern.test(key)) {
        this.cache.delete(key);
        count++;
      }
    }
    this.updateStats();
    return count;
  }

  // Buscar com fallback
  async getOrSet<T>(
    key: string,
    factory: () => Promise<T> | T,
    options: CacheOptions = {}
  ): Promise<T> {
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    const data = await factory();
    this.set(key, data, options);
    return data;
  }

  // Atualizar TTL
  touch(key: string, newTTL?: number): boolean {
    const entry = this.cache.get(key);
    if (!entry || this.isExpired(entry)) {
      return false;
    }

    if (newTTL !== undefined) {
      entry.ttl = newTTL;
    }
    entry.timestamp = Date.now();
    return true;
  }

  // ===== ESTRATÉGIAS DE REMOÇÃO =====

  private evict(strategy: 'lru' | 'lfu' | 'fifo'): void {
    if (this.cache.size === 0) return;

    let keyToRemove: string;

    switch (strategy) {
      case 'lru': // Least Recently Used
        keyToRemove = this.findLRU();
        break;
      case 'lfu': // Least Frequently Used
        keyToRemove = this.findLFU();
        break;
      case 'fifo': // First In, First Out
        keyToRemove = this.findFIFO();
        break;
      default:
        keyToRemove = this.findLRU();
    }

    this.cache.delete(keyToRemove);
  }

  private findLRU(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.lastAccess < oldestTime) {
        oldestTime = entry.lastAccess;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  private findLFU(): string {
    let leastUsedKey = '';
    let leastCount = Infinity;

    for (const [key, entry] of this.cache.entries()) {
      // Considerar prioridade na decisão
      const adjustedCount = entry.accessCount * entry.priority;
      if (adjustedCount < leastCount) {
        leastCount = adjustedCount;
        leastUsedKey = key;
      }
    }

    return leastUsedKey;
  }

  private findFIFO(): string {
    let oldestKey = '';
    let oldestTime = Date.now();

    for (const [key, entry] of this.cache.entries()) {
      if (entry.timestamp < oldestTime) {
        oldestTime = entry.timestamp;
        oldestKey = key;
      }
    }

    return oldestKey;
  }

  // ===== UTILITÁRIOS =====

  private isExpired(entry: CacheEntry<any>): boolean {
    return Date.now() - entry.timestamp > entry.ttl;
  }

  private cleanup(): void {
    const now = Date.now();
    const keysToDelete: string[] = [];

    for (const [key, entry] of this.cache.entries()) {
      if (this.isExpired(entry)) {
        keysToDelete.push(key);
      }
    }

    keysToDelete.forEach(key => this.cache.delete(key));
    
    if (keysToDelete.length > 0) {
      this.updateStats();
      console.log(`Cache cleanup: removed ${keysToDelete.length} expired entries`);
    }
  }

  private updateStats(): void {
    this.stats.size = this.cache.size;
    this.stats.hitRate = this.stats.hits + this.stats.misses > 0 
      ? (this.stats.hits / (this.stats.hits + this.stats.misses)) * 100 
      : 0;
    
    // Estimar uso de memória (aproximado)
    this.stats.memoryUsage = this.cache.size * 1024; // 1KB por entrada (estimativa)
  }

  // ===== MÉTODOS PÚBLICOS DE INFORMAÇÃO =====

  getStats(): CacheStats {
    return { ...this.stats };
  }

  getKeys(): string[] {
    return Array.from(this.cache.keys());
  }

  getSize(): number {
    return this.cache.size;
  }

  getEntryInfo(key: string): Partial<CacheEntry<any>> | null {
    const entry = this.cache.get(key);
    if (!entry) return null;

    return {
      timestamp: entry.timestamp,
      ttl: entry.ttl,
      accessCount: entry.accessCount,
      lastAccess: entry.lastAccess,
      tags: entry.tags,
      priority: entry.priority,
    };
  }

  // Exportar cache para backup
  export(): Record<string, any> {
    const exported: Record<string, any> = {};
    for (const [key, entry] of this.cache.entries()) {
      if (!this.isExpired(entry)) {
        exported[key] = {
          data: entry.data,
          timestamp: entry.timestamp,
          ttl: entry.ttl,
          tags: entry.tags,
          priority: entry.priority,
        };
      }
    }
    return exported;
  }

  // Importar cache de backup
  import(data: Record<string, any>): void {
    this.clear();
    const now = Date.now();

    for (const [key, entryData] of Object.entries(data)) {
      // Verificar se ainda é válido
      if (now - entryData.timestamp < entryData.ttl) {
        this.set(key, entryData.data, {
          ttl: entryData.ttl - (now - entryData.timestamp),
          tags: entryData.tags,
          priority: entryData.priority,
        });
      }
    }
  }

  // Destruir cache
  destroy(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
    this.clear();
  }
}

// Instância global
export const smartCache = new SmartCache();

// Hook para usar cache em componentes React
export const useSmartCache = () => {
  return {
    cache: smartCache,
    get: smartCache.get.bind(smartCache),
    set: smartCache.set.bind(smartCache),
    getOrSet: smartCache.getOrSet.bind(smartCache),
    invalidateByTag: smartCache.invalidateByTag.bind(smartCache),
    invalidateByPattern: smartCache.invalidateByPattern.bind(smartCache),
    getStats: smartCache.getStats.bind(smartCache),
  };
};
