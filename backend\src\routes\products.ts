import { Router } from 'express';
import { ProductController } from '../controllers/ProductController';
import { CategoryController } from '../controllers/CategoryController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para produtos
const createProductValidation = [
  body('name')
    .notEmpty()
    .withMessage('Product name is required')
    .isLength({ min: 2, max: 200 })
    .withMessage('Product name must be between 2 and 200 characters'),
  body('price')
    .isFloat({ min: 0.01 })
    .withMessage('Price must be greater than 0'),
  body('cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Cost must be 0 or greater'),
  body('category_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Category ID must be a positive integer'),
  body('code')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Product code must be at most 50 characters'),
  body('barcode')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Barcode must be at most 50 characters'),
  body('unit')
    .optional()
    .isIn(['UN', 'KG', 'G', 'L', 'ML', 'M', 'CM'])
    .withMessage('Invalid unit'),
  body('preparation_time')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Preparation time must be 0 or greater'),
];

const updateProductValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 200 })
    .withMessage('Product name must be between 2 and 200 characters'),
  body('price')
    .optional()
    .isFloat({ min: 0.01 })
    .withMessage('Price must be greater than 0'),
  body('cost')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Cost must be 0 or greater'),
];

const updateStockValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  body('quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('operation')
    .isIn(['add', 'subtract', 'set'])
    .withMessage('Operation must be add, subtract, or set'),
  body('reason')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Reason must be at most 200 characters'),
];

// Validações para categorias
const createCategoryValidation = [
  body('name')
    .notEmpty()
    .withMessage('Category name is required')
    .isLength({ min: 2, max: 100 })
    .withMessage('Category name must be between 2 and 100 characters'),
  body('description')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Description must be at most 500 characters'),
  body('color')
    .optional()
    .matches(/^#[0-9A-F]{6}$/i)
    .withMessage('Color must be a valid hex color'),
  body('icon')
    .optional()
    .isLength({ max: 50 })
    .withMessage('Icon must be at most 50 characters'),
];

const updateCategoryValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Category ID must be a positive integer'),
  body('name')
    .optional()
    .isLength({ min: 2, max: 100 })
    .withMessage('Category name must be between 2 and 100 characters'),
];

// ===== ROTAS DE CATEGORIAS =====

/**
 * @route GET /api/products/categories
 * @desc Listar categorias
 * @access Private (read categories)
 */
router.get('/categories',
  authenticate,
  authorize('categories', 'read'),
  CategoryController.index
);

/**
 * @route GET /api/products/categories/most-used
 * @desc Buscar categorias mais usadas
 * @access Private (read categories)
 */
router.get('/categories/most-used',
  authenticate,
  authorize('categories', 'read'),
  CategoryController.mostUsed
);

/**
 * @route GET /api/products/categories/:id
 * @desc Buscar categoria por ID
 * @access Private (read categories)
 */
router.get('/categories/:id',
  authenticate,
  authorize('categories', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  CategoryController.show
);

/**
 * @route POST /api/products/categories
 * @desc Criar categoria
 * @access Private (create categories)
 */
router.post('/categories',
  authenticate,
  authorize('categories', 'create'),
  createCategoryValidation,
  validateRequest,
  logUserAction('create_category'),
  CategoryController.create
);

/**
 * @route PUT /api/products/categories/:id
 * @desc Atualizar categoria
 * @access Private (update categories)
 */
router.put('/categories/:id',
  authenticate,
  authorize('categories', 'update'),
  updateCategoryValidation,
  validateRequest,
  logUserAction('update_category'),
  CategoryController.update
);

/**
 * @route DELETE /api/products/categories/:id
 * @desc Deletar categoria
 * @access Private (delete categories)
 */
router.delete('/categories/:id',
  authenticate,
  authorize('categories', 'delete'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  logUserAction('delete_category'),
  CategoryController.delete
);

/**
 * @route GET /api/products/categories/:id/can-delete
 * @desc Verificar se categoria pode ser deletada
 * @access Private (read categories)
 */
router.get('/categories/:id/can-delete',
  authenticate,
  authorize('categories', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  CategoryController.canDelete
);

/**
 * @route GET /api/products/categories/:id/stats
 * @desc Obter estatísticas da categoria
 * @access Private (read categories)
 */
router.get('/categories/:id/stats',
  authenticate,
  authorize('categories', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  CategoryController.getStats
);

// ===== ROTAS DE PRODUTOS =====

/**
 * @route GET /api/products
 * @desc Listar produtos
 * @access Private (read products)
 */
router.get('/',
  authenticate,
  authorize('products', 'read'),
  ProductController.index
);

/**
 * @route GET /api/products/low-stock
 * @desc Buscar produtos com estoque baixo
 * @access Private (read products)
 */
router.get('/low-stock',
  authenticate,
  authorize('products', 'read'),
  ProductController.lowStock
);

/**
 * @route GET /api/products/top-selling
 * @desc Buscar produtos mais vendidos
 * @access Private (read products)
 */
router.get('/top-selling',
  authenticate,
  authorize('products', 'read'),
  ProductController.topSelling
);

/**
 * @route GET /api/products/:id
 * @desc Buscar produto por ID
 * @access Private (read products)
 */
router.get('/:id',
  authenticate,
  authorize('products', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  ProductController.show
);

/**
 * @route GET /api/products/:id/with-recipe
 * @desc Buscar produto com receita
 * @access Private (read products)
 */
router.get('/:id/with-recipe',
  authenticate,
  authorize('products', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  ProductController.getWithRecipe
);

/**
 * @route POST /api/products
 * @desc Criar produto
 * @access Private (create products)
 */
router.post('/',
  authenticate,
  authorize('products', 'create'),
  createProductValidation,
  validateRequest,
  logUserAction('create_product'),
  ProductController.create
);

/**
 * @route PUT /api/products/:id
 * @desc Atualizar produto
 * @access Private (update products)
 */
router.put('/:id',
  authenticate,
  authorize('products', 'update'),
  updateProductValidation,
  validateRequest,
  logUserAction('update_product'),
  ProductController.update
);

/**
 * @route DELETE /api/products/:id
 * @desc Deletar produto
 * @access Private (delete products)
 */
router.delete('/:id',
  authenticate,
  authorize('products', 'delete'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  logUserAction('delete_product'),
  ProductController.delete
);

/**
 * @route PUT /api/products/:id/stock
 * @desc Atualizar estoque do produto
 * @access Private (update products)
 */
router.put('/:id/stock',
  authenticate,
  authorize('products', 'update'),
  updateStockValidation,
  validateRequest,
  logUserAction('update_product_stock'),
  ProductController.updateStock
);

export default router;
