import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  Recipe, 
  CreateRecipeRequest, 
  UpdateRecipeRequest,
  ProductWithRecipe,
  StockValidation 
} from '../types/products';
import { IngredientModel } from './Ingredient';

export class RecipeModel {
  // Buscar receita por ID
  static async findById(id: number): Promise<Recipe | null> {
    const recipe = await executeQuerySingle<Recipe>(
      `SELECT r.*, i.name as ingredient_name, i.unit as ingredient_unit
       FROM recipes r
       LEFT JOIN ingredients i ON r.ingredient_id = i.id
       WHERE r.id = ?`,
      [id]
    );
    return recipe || null;
  }

  // Buscar receitas por produto
  static async findByProduct(productId: number): Promise<Recipe[]> {
    return await executeQuery<Recipe>(
      `SELECT r.*, i.name as ingredient_name, i.unit as ingredient_unit, i.cost_per_unit
       FROM recipes r
       LEFT JOIN ingredients i ON r.ingredient_id = i.id
       WHERE r.product_id = ?
       ORDER BY i.name ASC`,
      [productId]
    );
  }

  // Buscar receitas por ingrediente
  static async findByIngredient(ingredientId: number): Promise<Recipe[]> {
    return await executeQuery<Recipe>(
      `SELECT r.*, p.name as product_name
       FROM recipes r
       LEFT JOIN products p ON r.product_id = p.id
       WHERE r.ingredient_id = ?
       ORDER BY p.name ASC`,
      [ingredientId]
    );
  }

  // Criar item de receita
  static async create(recipeData: CreateRecipeRequest): Promise<Recipe> {
    // Verificar se produto existe
    const productExists = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM products WHERE id = ? AND is_active = 1',
      [recipeData.product_id]
    );

    if (!productExists || productExists.count === 0) {
      throw new Error('Product not found');
    }

    // Verificar se ingrediente existe
    const ingredient = await IngredientModel.findById(recipeData.ingredient_id);
    if (!ingredient) {
      throw new Error('Ingredient not found');
    }

    // Verificar se já existe este ingrediente na receita do produto
    const existingRecipe = await executeQuerySingle<Recipe>(
      'SELECT * FROM recipes WHERE product_id = ? AND ingredient_id = ?',
      [recipeData.product_id, recipeData.ingredient_id]
    );

    if (existingRecipe) {
      throw new Error('Ingredient already exists in this product recipe');
    }

    // Calcular custo se não fornecido
    const cost = recipeData.cost || (recipeData.quantity * ingredient.cost_per_unit);

    // Inserir item de receita
    const result = await executeUpdate(
      `INSERT INTO recipes (product_id, ingredient_id, quantity, unit, cost)
       VALUES (?, ?, ?, ?, ?)`,
      [
        recipeData.product_id,
        recipeData.ingredient_id,
        recipeData.quantity,
        recipeData.unit,
        cost,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create recipe item');
    }

    // Retornar item criado
    const newRecipe = await this.findById(result.lastID);
    if (!newRecipe) {
      throw new Error('Failed to retrieve created recipe item');
    }

    return newRecipe;
  }

  // Atualizar item de receita
  static async update(id: number, recipeData: UpdateRecipeRequest): Promise<Recipe> {
    const recipe = await this.findById(id);
    if (!recipe) {
      throw new Error('Recipe item not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    if (recipeData.quantity !== undefined) {
      updateFields.push('quantity = ?');
      params.push(recipeData.quantity);
    }

    if (recipeData.unit !== undefined) {
      updateFields.push('unit = ?');
      params.push(recipeData.unit);
    }

    if (recipeData.cost !== undefined) {
      updateFields.push('cost = ?');
      params.push(recipeData.cost);
    }

    if (updateFields.length === 0) {
      return recipe; // Nada para atualizar
    }

    params.push(id);

    await executeUpdate(
      `UPDATE recipes SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar item atualizado
    const updatedRecipe = await this.findById(id);
    if (!updatedRecipe) {
      throw new Error('Failed to retrieve updated recipe item');
    }

    return updatedRecipe;
  }

  // Deletar item de receita
  static async delete(id: number): Promise<void> {
    const recipe = await this.findById(id);
    if (!recipe) {
      throw new Error('Recipe item not found');
    }

    await executeUpdate('DELETE FROM recipes WHERE id = ?', [id]);
  }

  // Deletar toda a receita de um produto
  static async deleteByProduct(productId: number): Promise<void> {
    await executeUpdate('DELETE FROM recipes WHERE product_id = ?', [productId]);
  }

  // Obter produto com receita completa
  static async getProductWithRecipe(productId: number): Promise<ProductWithRecipe | null> {
    // Buscar produto
    const product = await executeQuerySingle<any>(
      `SELECT p.*, c.name as category_name, c.color as category_color
       FROM products p
       LEFT JOIN categories c ON p.category_id = c.id
       WHERE p.id = ?`,
      [productId]
    );

    if (!product) {
      return null;
    }

    // Buscar receita
    const recipe = await this.findByProduct(productId);

    // Calcular custo total da receita
    const totalRecipeCost = recipe.reduce((total, item) => total + item.cost, 0);

    // Calcular margem de lucro
    const profitMargin = product.price > 0 ? ((product.price - totalRecipeCost) / product.price) * 100 : 0;

    return {
      ...product,
      recipe,
      total_recipe_cost: totalRecipeCost,
      profit_margin: profitMargin,
    };
  }

  // Validar disponibilidade de estoque para produção
  static async validateStockForProduction(productId: number, quantity: number = 1): Promise<StockValidation> {
    const recipe = await this.findByProduct(productId);
    
    if (recipe.length === 0) {
      return {
        available: true,
        current_stock: 0,
        required_stock: 0,
      };
    }

    const missingIngredients: StockValidation['missing_ingredients'] = [];
    let allAvailable = true;

    for (const item of recipe) {
      const ingredient = await IngredientModel.findById(item.ingredient_id);
      if (!ingredient) {
        continue;
      }

      const requiredQuantity = item.quantity * quantity;
      const available = ingredient.current_stock;

      if (available < requiredQuantity) {
        allAvailable = false;
        missingIngredients?.push({
          ingredient_id: ingredient.id,
          ingredient_name: ingredient.name,
          required: requiredQuantity,
          available: available,
          missing: requiredQuantity - available,
        });
      }
    }

    return {
      available: allAvailable,
      current_stock: 0, // Não aplicável para receitas
      required_stock: 0, // Não aplicável para receitas
      missing_ingredients: missingIngredients?.length > 0 ? missingIngredients : undefined,
    };
  }

  // Consumir ingredientes da receita (para produção)
  static async consumeIngredients(productId: number, quantity: number = 1, userId: number): Promise<void> {
    const recipe = await this.findByProduct(productId);
    
    if (recipe.length === 0) {
      return; // Produto sem receita
    }

    // Validar disponibilidade antes de consumir
    const validation = await this.validateStockForProduction(productId, quantity);
    if (!validation.available) {
      throw new Error('Insufficient ingredients for production');
    }

    // Consumir ingredientes em transação
    await executeTransaction(async (db) => {
      for (const item of recipe) {
        const consumeQuantity = item.quantity * quantity;
        
        // Atualizar estoque do ingrediente
        await IngredientModel.updateStock(item.ingredient_id, consumeQuantity, 'subtract');
        
        // Registrar movimentação de estoque
        await executeUpdate(
          `INSERT INTO stock_movements (type, item_id, movement_type, quantity, reason, user_id)
           VALUES ('ingredient', ?, 'out', ?, ?, ?)`,
          [item.ingredient_id, consumeQuantity, `Production of ${quantity} unit(s) of product ${productId}`, userId]
        );
      }
    });
  }

  // Calcular custo de produção
  static async calculateProductionCost(productId: number, quantity: number = 1): Promise<number> {
    const recipe = await this.findByProduct(productId);
    
    let totalCost = 0;
    
    for (const item of recipe) {
      totalCost += item.cost * quantity;
    }
    
    return totalCost;
  }

  // Atualizar custos da receita baseado nos preços atuais dos ingredientes
  static async updateRecipeCosts(productId: number): Promise<void> {
    const recipe = await this.findByProduct(productId);
    
    for (const item of recipe) {
      const ingredient = await IngredientModel.findById(item.ingredient_id);
      if (ingredient) {
        const newCost = item.quantity * ingredient.cost_per_unit;
        
        await executeUpdate(
          'UPDATE recipes SET cost = ? WHERE id = ?',
          [newCost, item.id]
        );
      }
    }
  }

  // Buscar produtos que usam um ingrediente específico
  static async findProductsUsingIngredient(ingredientId: number): Promise<Array<{ product_id: number; product_name: string; quantity_used: number }>> {
    return await executeQuery<{ product_id: number; product_name: string; quantity_used: number }>(
      `SELECT r.product_id, p.name as product_name, r.quantity as quantity_used
       FROM recipes r
       LEFT JOIN products p ON r.product_id = p.id
       WHERE r.ingredient_id = ? AND p.is_active = 1
       ORDER BY p.name ASC`,
      [ingredientId]
    );
  }

  // Obter receitas com maior custo
  static async findMostExpensiveRecipes(limit: number = 10): Promise<Array<{ product_id: number; product_name: string; total_cost: number }>> {
    return await executeQuery<{ product_id: number; product_name: string; total_cost: number }>(
      `SELECT r.product_id, p.name as product_name, SUM(r.cost) as total_cost
       FROM recipes r
       LEFT JOIN products p ON r.product_id = p.id
       WHERE p.is_active = 1
       GROUP BY r.product_id
       ORDER BY total_cost DESC
       LIMIT ?`,
      [limit]
    );
  }
}
