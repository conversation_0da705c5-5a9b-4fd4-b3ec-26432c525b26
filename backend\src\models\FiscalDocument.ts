import { executeQuery, executeQuerySingle, executeUpdate, executeTransaction } from '../utils/database';
import { 
  FiscalDocument, 
  CreateFiscalDocumentRequest, 
  UpdateFiscalDocumentRequest, 
  FiscalDocumentFilters,
  FiscalDocumentItem,
  FiscalMetrics
} from '../types/fiscal';
import { OrderModel } from './Order';
import { ProductModel } from './Product';

export class FiscalDocumentModel {
  // Buscar documento fiscal por ID
  static async findById(id: number): Promise<FiscalDocument | null> {
    const document = await executeQuerySingle<FiscalDocument>(
      `SELECT fd.*, c.name as customer_name, o.order_number
       FROM fiscal_documents fd
       LEFT JOIN customers c ON fd.customer_id = c.id
       LEFT JOIN orders o ON fd.order_id = o.id
       WHERE fd.id = ?`,
      [id]
    );
    return document || null;
  }

  // Buscar documento por chave de acesso
  static async findByAccessKey(accessKey: string): Promise<FiscalDocument | null> {
    const document = await executeQuerySingle<FiscalDocument>(
      'SELECT * FROM fiscal_documents WHERE access_key = ?',
      [accessKey]
    );
    return document || null;
  }

  // Buscar documento por pedido
  static async findByOrder(orderId: number): Promise<FiscalDocument | null> {
    const document = await executeQuerySingle<FiscalDocument>(
      'SELECT * FROM fiscal_documents WHERE order_id = ? ORDER BY created_at DESC LIMIT 1',
      [orderId]
    );
    return document || null;
  }

  // Listar documentos com filtros
  static async findAll(filters: FiscalDocumentFilters = {}): Promise<FiscalDocument[]> {
    let query = `
      SELECT fd.*, c.name as customer_name, o.order_number
      FROM fiscal_documents fd
      LEFT JOIN customers c ON fd.customer_id = c.id
      LEFT JOIN orders o ON fd.order_id = o.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.type) {
      query += ' AND fd.type = ?';
      params.push(filters.type);
    }

    if (filters.status) {
      query += ' AND fd.status = ?';
      params.push(filters.status);
    }

    if (filters.order_id) {
      query += ' AND fd.order_id = ?';
      params.push(filters.order_id);
    }

    if (filters.customer_id) {
      query += ' AND fd.customer_id = ?';
      params.push(filters.customer_id);
    }

    if (filters.date_from) {
      query += ' AND DATE(fd.issue_date) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(fd.issue_date) <= ?';
      params.push(filters.date_to);
    }

    if (filters.search) {
      query += ' AND (fd.access_key LIKE ? OR fd.number LIKE ? OR c.name LIKE ? OR o.order_number LIKE ?)';
      const searchTerm = `%${filters.search}%`;
      params.push(searchTerm, searchTerm, searchTerm, searchTerm);
    }

    query += ' ORDER BY fd.created_at DESC';

    return await executeQuery<FiscalDocument>(query, params);
  }

  // Criar documento fiscal
  static async create(documentData: CreateFiscalDocumentRequest): Promise<FiscalDocument> {
    return await executeTransaction(async (db) => {
      // Verificar se pedido existe (se fornecido)
      if (documentData.order_id) {
        const order = await OrderModel.findById(documentData.order_id);
        if (!order) {
          throw new Error('Order not found');
        }
      }

      // Obter próximo número da série
      const nextNumber = await this.getNextNumber(documentData.type);
      const series = documentData.type === 'nfce' ? 1 : 1; // TODO: Buscar da configuração

      // Gerar chave de acesso (simplificada para demonstração)
      const accessKey = this.generateAccessKey(series, nextNumber);

      // Calcular totais
      let totalAmount = 0;
      let totalTaxes = 0;

      for (const item of documentData.items) {
        const itemTotal = item.quantity * item.unit_price;
        const taxRate = item.tax_rate || 0;
        const taxAmount = itemTotal * (taxRate / 100);
        
        totalAmount += itemTotal;
        totalTaxes += taxAmount;
      }

      // Inserir documento fiscal
      const result = await executeUpdate(
        `INSERT INTO fiscal_documents (
          type, order_id, customer_id, series, number, access_key, 
          status, issue_date, total_amount
        ) VALUES (?, ?, ?, ?, ?, ?, 'pending', CURRENT_TIMESTAMP, ?)`,
        [
          documentData.type,
          documentData.order_id || null,
          documentData.customer_id || null,
          series,
          nextNumber,
          accessKey,
          totalAmount,
        ]
      );

      if (!result.lastID) {
        throw new Error('Failed to create fiscal document');
      }

      const documentId = result.lastID;

      // Inserir itens do documento
      for (const item of documentData.items) {
        const product = await ProductModel.findById(item.product_id);
        if (!product) {
          throw new Error(`Product ${item.product_id} not found`);
        }

        const itemTotal = item.quantity * item.unit_price;
        const taxRate = item.tax_rate || 0;
        const taxAmount = itemTotal * (taxRate / 100);

        await executeUpdate(
          `INSERT INTO fiscal_document_items (
            fiscal_document_id, product_id, product_code, product_name, 
            ncm_code, cfop, unit, quantity, unit_price, total_price, 
            tax_rate, tax_amount
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            documentId,
            item.product_id,
            product.code,
            product.name,
            product.ncm_code || '00000000',
            item.cfop || product.cfop || '5102',
            product.unit,
            item.quantity,
            item.unit_price,
            itemTotal,
            taxRate,
            taxAmount,
          ]
        );
      }

      // Retornar documento criado
      const newDocument = await this.findById(documentId);
      if (!newDocument) {
        throw new Error('Failed to retrieve created fiscal document');
      }

      return newDocument;
    });
  }

  // Atualizar documento fiscal
  static async update(id: number, documentData: UpdateFiscalDocumentRequest): Promise<FiscalDocument> {
    const document = await this.findById(id);
    if (!document) {
      throw new Error('Fiscal document not found');
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['status', 'protocol', 'rejection_reason', 'xml_content', 'pdf_content', 'qr_code'];

    updatableFields.forEach(field => {
      if (documentData[field as keyof UpdateFiscalDocumentRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        params.push(documentData[field as keyof UpdateFiscalDocumentRequest]);
      }
    });

    // Atualizar timestamp de cancelamento se status for cancelled
    if (documentData.status === 'cancelled') {
      updateFields.push('cancelled_at = CURRENT_TIMESTAMP');
    }

    if (updateFields.length === 0) {
      return document; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE fiscal_documents SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar documento atualizado
    const updatedDocument = await this.findById(id);
    if (!updatedDocument) {
      throw new Error('Failed to retrieve updated fiscal document');
    }

    return updatedDocument;
  }

  // Buscar itens do documento
  static async getDocumentItems(documentId: number): Promise<FiscalDocumentItem[]> {
    return await executeQuery<FiscalDocumentItem>(
      'SELECT * FROM fiscal_document_items WHERE fiscal_document_id = ? ORDER BY id ASC',
      [documentId]
    );
  }

  // Cancelar documento
  static async cancel(id: number, reason: string): Promise<FiscalDocument> {
    const document = await this.findById(id);
    if (!document) {
      throw new Error('Fiscal document not found');
    }

    if (document.status !== 'authorized') {
      throw new Error('Only authorized documents can be cancelled');
    }

    await executeUpdate(
      `UPDATE fiscal_documents SET 
         status = 'cancelled', 
         cancellation_reason = ?, 
         cancelled_at = CURRENT_TIMESTAMP,
         updated_at = CURRENT_TIMESTAMP
       WHERE id = ?`,
      [reason, id]
    );

    // Retornar documento atualizado
    const updatedDocument = await this.findById(id);
    if (!updatedDocument) {
      throw new Error('Failed to retrieve updated fiscal document');
    }

    return updatedDocument;
  }

  // Obter próximo número da série
  static async getNextNumber(type: string): Promise<number> {
    const lastDocument = await executeQuerySingle<{ number: number }>(
      'SELECT MAX(number) as number FROM fiscal_documents WHERE type = ?',
      [type]
    );

    return (lastDocument?.number || 0) + 1;
  }

  // Gerar chave de acesso (simplificada)
  static generateAccessKey(series: number, number: number): string {
    const now = new Date();
    const year = now.getFullYear().toString().slice(-2);
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const cnpj = '12345678000195'; // TODO: Buscar da configuração
    const model = '65'; // NFC-e
    const seriesStr = series.toString().padStart(3, '0');
    const numberStr = number.toString().padStart(9, '0');
    const emission = '1'; // Normal
    const random = Math.floor(Math.random() * 100000000).toString().padStart(8, '0');

    const baseKey = `${cnpj}${model}${seriesStr}${numberStr}${year}${month}${emission}${random}`;
    
    // Calcular dígito verificador (algoritmo simplificado)
    const dv = this.calculateCheckDigit(baseKey);
    
    return baseKey + dv;
  }

  // Calcular dígito verificador
  static calculateCheckDigit(key: string): string {
    const weights = [2, 3, 4, 5, 6, 7, 8, 9];
    let sum = 0;
    let weightIndex = 0;

    for (let i = key.length - 1; i >= 0; i--) {
      sum += parseInt(key[i]) * weights[weightIndex];
      weightIndex = (weightIndex + 1) % weights.length;
    }

    const remainder = sum % 11;
    return remainder < 2 ? '0' : (11 - remainder).toString();
  }

  // Obter métricas fiscais
  static async getMetrics(): Promise<FiscalMetrics> {
    // Documentos do dia
    const todayDocs = await executeQuerySingle<{
      count: number;
      amount: number;
      pending: number;
      rejected: number;
    }>(
      `SELECT 
         COUNT(*) as count,
         COALESCE(SUM(total_amount), 0) as amount,
         SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending,
         SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected
       FROM fiscal_documents 
       WHERE DATE(issue_date) = DATE('now')`
    );

    // Taxa de autorização
    const authStats = await executeQuerySingle<{
      total: number;
      authorized: number;
    }>(
      `SELECT 
         COUNT(*) as total,
         SUM(CASE WHEN status = 'authorized' THEN 1 ELSE 0 END) as authorized
       FROM fiscal_documents 
       WHERE DATE(issue_date) = DATE('now')`
    );

    // Último documento
    const lastDoc = await executeQuerySingle<{ created_at: string }>(
      'SELECT created_at FROM fiscal_documents ORDER BY created_at DESC LIMIT 1'
    );

    // Contingência ativa
    const activeContingency = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM fiscal_contingencies WHERE is_active = 1'
    );

    const totalDocs = authStats?.total || 0;
    const authorizedDocs = authStats?.authorized || 0;
    const authorizationRate = totalDocs > 0 ? (authorizedDocs / totalDocs) * 100 : 100;

    return {
      documents_today: todayDocs?.count || 0,
      documents_pending: todayDocs?.pending || 0,
      documents_rejected: todayDocs?.rejected || 0,
      total_amount_today: todayDocs?.amount || 0,
      total_taxes_today: 0, // TODO: Calcular impostos
      authorization_rate: authorizationRate,
      average_processing_time: 2.5, // TODO: Calcular tempo real
      contingency_active: (activeContingency?.count || 0) > 0,
      certificate_expires_in: 90, // TODO: Verificar certificado
      sefaz_status: 'online' as const, // TODO: Verificar status real
      last_document_at: lastDoc?.created_at,
    };
  }

  // Buscar documentos pendentes
  static async findPending(): Promise<FiscalDocument[]> {
    return await this.findAll({ status: 'pending' });
  }

  // Buscar documentos do dia
  static async findToday(): Promise<FiscalDocument[]> {
    const today = new Date().toISOString().slice(0, 10);
    return await this.findAll({
      date_from: today,
      date_to: today,
    });
  }

  // Buscar documentos rejeitados
  static async findRejected(): Promise<FiscalDocument[]> {
    return await this.findAll({ status: 'rejected' });
  }

  // Verificar se documento pode ser cancelado
  static async canCancel(id: number): Promise<{
    can_cancel: boolean;
    reason?: string;
  }> {
    const document = await this.findById(id);
    if (!document) {
      return {
        can_cancel: false,
        reason: 'Document not found',
      };
    }

    if (document.status !== 'authorized') {
      return {
        can_cancel: false,
        reason: 'Only authorized documents can be cancelled',
      };
    }

    // Verificar prazo de cancelamento (24 horas)
    const issueDate = new Date(document.issue_date);
    const now = new Date();
    const hoursDiff = (now.getTime() - issueDate.getTime()) / (1000 * 60 * 60);

    if (hoursDiff > 24) {
      return {
        can_cancel: false,
        reason: 'Cancellation period expired (24 hours)',
      };
    }

    return { can_cancel: true };
  }
}
