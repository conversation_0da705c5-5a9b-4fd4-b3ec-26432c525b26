import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// Configurar variáveis de ambiente padrão
if (!process.env.REACT_APP_API_URL) {
  process.env.REACT_APP_API_URL = 'http://localhost:3001/api';
}

// Configurar console para Electron
if (window.electronAPI) {
  console.log('Running in Electron environment');
  
  // Escutar ações do menu
  window.electronAPI.onMenuAction((action: string) => {
    console.log('Menu action received:', action);
    
    // Aqui você pode implementar as ações do menu
    switch (action) {
      case 'new-order':
        // Navegar para nova venda
        break;
      case 'open-cash':
        // Abrir caixa
        break;
      case 'settings':
        // Abrir configurações
        break;
      // ... outras ações
    }
  });
}

// Configurar tratamento de erros globais
window.addEventListener('error', (event) => {
  console.error('Global error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

// Configurar notificações do sistema
if ('Notification' in window && Notification.permission === 'default') {
  Notification.requestPermission();
}

// Verificar se está online/offline
window.addEventListener('online', () => {
  console.log('Application is online');
});

window.addEventListener('offline', () => {
  console.log('Application is offline');
});

// Criar root e renderizar aplicação
const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// Remover loading screen quando React carregar
setTimeout(() => {
  const loadingScreen = document.getElementById('loading-screen');
  if (loadingScreen) {
    loadingScreen.style.opacity = '0';
    setTimeout(() => {
      loadingScreen.remove();
    }, 500);
  }
}, 100);
