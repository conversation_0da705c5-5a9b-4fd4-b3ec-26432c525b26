import { Router } from 'express';
import { OrderController } from '../controllers/OrderController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para pedidos
const createOrderValidation = [
  body('type')
    .isIn(['counter', 'table', 'delivery'])
    .withMessage('Order type must be counter, table, or delivery'),
  body('customer_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Customer ID must be a positive integer'),
  body('table_id')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Table ID must be a positive integer'),
  body('items')
    .isArray({ min: 1 })
    .withMessage('Order must have at least one item'),
  body('items.*.product_id')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  body('items.*.quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('items.*.unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit price must be 0 or greater'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
  body('delivery_address')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Delivery address must be at most 500 characters'),
  body('estimated_time')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Estimated time must be 0 or greater'),
];

const updateOrderValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Order ID must be a positive integer'),
  body('status')
    .optional()
    .isIn(['pending', 'preparing', 'ready', 'delivered', 'cancelled'])
    .withMessage('Invalid order status'),
  body('payment_status')
    .optional()
    .isIn(['pending', 'paid', 'cancelled'])
    .withMessage('Invalid payment status'),
  body('notes')
    .optional()
    .isLength({ max: 500 })
    .withMessage('Notes must be at most 500 characters'),
  body('estimated_time')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Estimated time must be 0 or greater'),
  body('discount')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Discount must be 0 or greater'),
];

const addItemValidation = [
  param('id')
    .isInt({ min: 1 })
    .withMessage('Order ID must be a positive integer'),
  body('product_id')
    .isInt({ min: 1 })
    .withMessage('Product ID must be a positive integer'),
  body('quantity')
    .isFloat({ min: 0.001 })
    .withMessage('Quantity must be greater than 0'),
  body('unit_price')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Unit price must be 0 or greater'),
  body('notes')
    .optional()
    .isLength({ max: 200 })
    .withMessage('Notes must be at most 200 characters'),
];

// ===== ROTAS DE PEDIDOS =====

/**
 * @route GET /api/sales/orders
 * @desc Listar pedidos
 * @access Private (read orders)
 */
router.get('/orders',
  authenticate,
  authorize('orders', 'read'),
  OrderController.index
);

/**
 * @route GET /api/sales/orders/open
 * @desc Buscar pedidos em aberto
 * @access Private (read orders)
 */
router.get('/orders/open',
  authenticate,
  authorize('orders', 'read'),
  OrderController.getOpen
);

/**
 * @route GET /api/sales/orders/today
 * @desc Buscar pedidos do dia
 * @access Private (read orders)
 */
router.get('/orders/today',
  authenticate,
  authorize('orders', 'read'),
  OrderController.getToday
);

/**
 * @route POST /api/sales/orders/validate
 * @desc Validar pedido antes de criar
 * @access Private (create orders)
 */
router.post('/orders/validate',
  authenticate,
  authorize('orders', 'create'),
  createOrderValidation,
  validateRequest,
  OrderController.validate
);

/**
 * @route GET /api/sales/orders/:id
 * @desc Buscar pedido por ID
 * @access Private (read orders)
 */
router.get('/orders/:id',
  authenticate,
  authorize('orders', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  OrderController.show
);

/**
 * @route POST /api/sales/orders
 * @desc Criar pedido
 * @access Private (create orders)
 */
router.post('/orders',
  authenticate,
  authorize('orders', 'create'),
  createOrderValidation,
  validateRequest,
  logUserAction('create_order'),
  OrderController.create
);

/**
 * @route PUT /api/sales/orders/:id
 * @desc Atualizar pedido
 * @access Private (update orders)
 */
router.put('/orders/:id',
  authenticate,
  authorize('orders', 'update'),
  updateOrderValidation,
  validateRequest,
  logUserAction('update_order'),
  OrderController.update
);

/**
 * @route DELETE /api/sales/orders/:id
 * @desc Cancelar pedido
 * @access Private (delete orders)
 */
router.delete('/orders/:id',
  authenticate,
  authorize('orders', 'delete'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  logUserAction('cancel_order'),
  OrderController.cancel
);

/**
 * @route GET /api/sales/orders/:id/items
 * @desc Buscar itens do pedido
 * @access Private (read orders)
 */
router.get('/orders/:id/items',
  authenticate,
  authorize('orders', 'read'),
  param('id').isInt({ min: 1 }),
  validateRequest,
  OrderController.getItems
);

/**
 * @route POST /api/sales/orders/:id/items
 * @desc Adicionar item ao pedido
 * @access Private (update orders)
 */
router.post('/orders/:id/items',
  authenticate,
  authorize('orders', 'update'),
  addItemValidation,
  validateRequest,
  logUserAction('add_order_item'),
  OrderController.addItem
);

export default router;
