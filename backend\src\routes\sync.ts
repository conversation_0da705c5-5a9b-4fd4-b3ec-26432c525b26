import { Router } from 'express';
import { SyncController } from '../controllers/SyncController';
import { authenticate, authorize, logUserAction } from '../middleware/auth';
import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

const router = Router();

// Middleware para validar entrada
const validateRequest = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      success: false,
      error: 'Validation failed',
      details: errors.array(),
      timestamp: new Date().toISOString(),
    });
  }
  next();
};

// Validações para sincronização
const startSyncValidation = [
  body('entity_types')
    .optional()
    .isArray()
    .withMessage('Entity types must be an array'),
  body('entity_types.*')
    .optional()
    .isIn([
      'products', 'categories', 'orders', 'order_items', 'payments',
      'users', 'cash_sessions', 'tef_transactions', 'fiscal_documents',
      'kitchen_orders', 'inventory_movements'
    ])
    .withMessage('Invalid entity type'),
  body('direction')
    .optional()
    .isIn(['up', 'down', 'bidirectional'])
    .withMessage('Invalid sync direction'),
  body('force_sync')
    .optional()
    .isBoolean()
    .withMessage('Force sync must be a boolean'),
  body('resolve_conflicts')
    .optional()
    .isBoolean()
    .withMessage('Resolve conflicts must be a boolean'),
  body('since_timestamp')
    .optional()
    .isISO8601()
    .withMessage('Invalid timestamp format'),
];

// Validações para dados offline
const saveOfflineDataValidation = [
  body('entity_type')
    .notEmpty()
    .withMessage('Entity type is required')
    .isIn([
      'products', 'categories', 'orders', 'order_items', 'payments',
      'users', 'cash_sessions', 'tef_transactions', 'fiscal_documents',
      'kitchen_orders', 'inventory_movements'
    ])
    .withMessage('Invalid entity type'),
  body('entity_id')
    .isInt({ min: 1 })
    .withMessage('Entity ID must be a positive integer'),
  body('data')
    .notEmpty()
    .withMessage('Data is required'),
];

// Validações para resolução de conflitos
const resolveConflictValidation = [
  param('conflictId')
    .notEmpty()
    .withMessage('Conflict ID is required'),
  body('resolution')
    .isIn(['server_wins', 'client_wins', 'manual', 'merge'])
    .withMessage('Invalid resolution type'),
  body('resolved_data')
    .optional()
    .custom((value, { req }) => {
      if (req.body.resolution === 'manual' && !value) {
        throw new Error('Resolved data is required for manual resolution');
      }
      return true;
    }),
];

// Validações para parâmetros de entidade
const entityParamsValidation = [
  param('entityType')
    .isIn([
      'products', 'categories', 'orders', 'order_items', 'payments',
      'users', 'cash_sessions', 'tef_transactions', 'fiscal_documents',
      'kitchen_orders', 'inventory_movements'
    ])
    .withMessage('Invalid entity type'),
  param('entityId')
    .optional()
    .isInt({ min: 1 })
    .withMessage('Entity ID must be a positive integer'),
];

// ===== ROTAS DE SINCRONIZAÇÃO =====

/**
 * @route POST /api/sync/start
 * @desc Iniciar sincronização
 * @access Private (sync permission)
 */
router.post('/start',
  authenticate,
  authorize('sync', 'create'),
  startSyncValidation,
  validateRequest,
  logUserAction('start_sync'),
  SyncController.startSync
);

/**
 * @route GET /api/sync/status/:queueId
 * @desc Obter status da sincronização
 * @access Private (sync permission)
 */
router.get('/status/:queueId',
  authenticate,
  authorize('sync', 'read'),
  param('queueId').notEmpty().withMessage('Queue ID is required'),
  validateRequest,
  SyncController.getSyncStatus
);

/**
 * @route GET /api/sync/metrics
 * @desc Obter métricas de sincronização
 * @access Private (sync permission)
 */
router.get('/metrics',
  authenticate,
  authorize('sync', 'read'),
  SyncController.getSyncMetrics
);

// ===== ROTAS DE DADOS OFFLINE =====

/**
 * @route POST /api/sync/offline
 * @desc Salvar dados offline
 * @access Private (sync permission)
 */
router.post('/offline',
  authenticate,
  authorize('sync', 'create'),
  saveOfflineDataValidation,
  validateRequest,
  logUserAction('save_offline_data'),
  SyncController.saveOfflineData
);

/**
 * @route GET /api/sync/offline/:entityType/:entityId?
 * @desc Obter dados offline
 * @access Private (sync permission)
 */
router.get('/offline/:entityType/:entityId?',
  authenticate,
  authorize('sync', 'read'),
  entityParamsValidation,
  validateRequest,
  SyncController.getOfflineData
);

// ===== ROTAS DE RESOLUÇÃO DE CONFLITOS =====

/**
 * @route POST /api/sync/conflicts/:conflictId/resolve
 * @desc Resolver conflito de sincronização
 * @access Private (sync permission)
 */
router.post('/conflicts/:conflictId/resolve',
  authenticate,
  authorize('sync', 'update'),
  resolveConflictValidation,
  validateRequest,
  logUserAction('resolve_sync_conflict'),
  SyncController.resolveConflict
);

// ===== ROTAS DE CONECTIVIDADE =====

/**
 * @route GET /api/sync/connectivity
 * @desc Obter status de conectividade
 * @access Private (sync permission)
 */
router.get('/connectivity',
  authenticate,
  authorize('sync', 'read'),
  SyncController.getConnectivityStatus
);

// ===== ROTAS DE SINCRONIZAÇÃO FORÇADA =====

/**
 * @route POST /api/sync/force/:entityType/:entityId
 * @desc Forçar sincronização de entidade específica
 * @access Private (sync permission)
 */
router.post('/force/:entityType/:entityId',
  authenticate,
  authorize('sync', 'create'),
  entityParamsValidation,
  validateRequest,
  logUserAction('force_sync_entity'),
  SyncController.forceSyncEntity
);

// ===== ROTAS DE MANUTENÇÃO =====

/**
 * @route DELETE /api/sync/cleanup
 * @desc Limpar dados de sincronização antigos
 * @access Private (admin only)
 */
router.delete('/cleanup',
  authenticate,
  authorize('admin', 'delete'),
  query('days')
    .optional()
    .isInt({ min: 1, max: 365 })
    .withMessage('Days must be between 1 and 365'),
  validateRequest,
  logUserAction('cleanup_sync_data'),
  SyncController.cleanupSyncData
);

export default router;
