#!/usr/bin/env node

/**
 * Script para testar as APIs de produtos
 * Testa CRUD de categorias e produtos
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar categorias
async function testCategories() {
  log('\n📂 Testando Categorias...', 'blue');

  try {
    // Listar categorias
    log('Listando categorias...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/products/categories`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} categorias encontradas`, 'green');

    // Criar categoria
    log('Criando nova categoria...', 'cyan');
    const createResponse = await axios.post(`${API_BASE_URL}/products/categories`, {
      name: 'Categoria Teste',
      description: 'Categoria criada pelo teste',
      color: '#FF9800',
      icon: 'test'
    }, {
      headers: getHeaders()
    });
    
    const categoryId = createResponse.data.data.id;
    log(`✅ Categoria criada com ID: ${categoryId}`, 'green');

    // Buscar categoria por ID
    log('Buscando categoria por ID...', 'cyan');
    const getResponse = await axios.get(`${API_BASE_URL}/products/categories/${categoryId}`, {
      headers: getHeaders()
    });
    log(`✅ Categoria encontrada: ${getResponse.data.data.name}`, 'green');

    // Atualizar categoria
    log('Atualizando categoria...', 'cyan');
    await axios.put(`${API_BASE_URL}/products/categories/${categoryId}`, {
      name: 'Categoria Teste Atualizada',
      description: 'Categoria atualizada pelo teste'
    }, {
      headers: getHeaders()
    });
    log('✅ Categoria atualizada', 'green');

    // Deletar categoria
    log('Deletando categoria...', 'cyan');
    await axios.delete(`${API_BASE_URL}/products/categories/${categoryId}`, {
      headers: getHeaders()
    });
    log('✅ Categoria deletada', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de categoria: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar produtos
async function testProducts() {
  log('\n🛍️ Testando Produtos...', 'blue');

  try {
    // Listar produtos
    log('Listando produtos...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/products`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} produtos encontrados`, 'green');

    // Criar produto
    log('Criando novo produto...', 'cyan');
    const createResponse = await axios.post(`${API_BASE_URL}/products`, {
      name: 'Produto Teste',
      description: 'Produto criado pelo teste',
      price: 19.99,
      cost: 10.50,
      unit: 'UN',
      stock_control: true,
      current_stock: 100,
      min_stock: 10,
      max_stock: 500,
      preparation_time: 5
    }, {
      headers: getHeaders()
    });
    
    const productId = createResponse.data.data.id;
    log(`✅ Produto criado com ID: ${productId}`, 'green');

    // Buscar produto por ID
    log('Buscando produto por ID...', 'cyan');
    const getResponse = await axios.get(`${API_BASE_URL}/products/${productId}`, {
      headers: getHeaders()
    });
    log(`✅ Produto encontrado: ${getResponse.data.data.name}`, 'green');

    // Atualizar produto
    log('Atualizando produto...', 'cyan');
    await axios.put(`${API_BASE_URL}/products/${productId}`, {
      name: 'Produto Teste Atualizado',
      price: 24.99
    }, {
      headers: getHeaders()
    });
    log('✅ Produto atualizado', 'green');

    // Atualizar estoque
    log('Atualizando estoque...', 'cyan');
    await axios.put(`${API_BASE_URL}/products/${productId}/stock`, {
      quantity: 50,
      operation: 'add',
      reason: 'Teste de entrada de estoque'
    }, {
      headers: getHeaders()
    });
    log('✅ Estoque atualizado', 'green');

    // Buscar produtos com estoque baixo
    log('Buscando produtos com estoque baixo...', 'cyan');
    const lowStockResponse = await axios.get(`${API_BASE_URL}/products/low-stock`, {
      headers: getHeaders()
    });
    log(`✅ ${lowStockResponse.data.data.length} produtos com estoque baixo`, 'green');

    // Buscar produtos mais vendidos
    log('Buscando produtos mais vendidos...', 'cyan');
    const topSellingResponse = await axios.get(`${API_BASE_URL}/products/top-selling?limit=5`, {
      headers: getHeaders()
    });
    log(`✅ ${topSellingResponse.data.data.length} produtos mais vendidos`, 'green');

    // Deletar produto
    log('Deletando produto...', 'cyan');
    await axios.delete(`${API_BASE_URL}/products/${productId}`, {
      headers: getHeaders()
    });
    log('✅ Produto deletado', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de produto: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar filtros
async function testFilters() {
  log('\n🔍 Testando Filtros...', 'blue');

  try {
    // Filtrar por categoria
    log('Filtrando produtos por categoria...', 'cyan');
    const categoryFilterResponse = await axios.get(`${API_BASE_URL}/products?category_id=1`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por categoria funcionando`, 'green');

    // Filtrar por busca
    log('Filtrando produtos por busca...', 'cyan');
    const searchFilterResponse = await axios.get(`${API_BASE_URL}/products?search=hambur`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por busca funcionando`, 'green');

    // Filtrar produtos ativos
    log('Filtrando produtos ativos...', 'cyan');
    const activeFilterResponse = await axios.get(`${API_BASE_URL}/products?is_active=true`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por status funcionando`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de filtros: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs de Produtos - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      categories: await testCategories(),
      products: await testProducts(),
      filters: await testFilters(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs de produtos funcionando corretamente.', 'green');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
