import { executeQuery, executeQuery<PERSON>ingle, executeUpdate } from '../utils/database';
import { 
  StockMovement, 
  CreateStockMovementRequest, 
  StockMovementFilters 
} from '../types/products';
import { ProductModel } from './Product';
import { IngredientModel } from './Ingredient';

export class StockMovementModel {
  // Buscar movimentação por ID
  static async findById(id: number): Promise<StockMovement | null> {
    const movement = await executeQuerySingle<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       WHERE sm.id = ?`,
      [id]
    );
    return movement || null;
  }

  // Listar movimentações com filtros
  static async findAll(filters: StockMovementFilters = {}): Promise<StockMovement[]> {
    let query = `
      SELECT sm.*, u.username, u.full_name as user_name
      FROM stock_movements sm
      LEFT JOIN users u ON sm.user_id = u.id
      WHERE 1=1
    `;
    const params: any[] = [];

    if (filters.type) {
      query += ' AND sm.type = ?';
      params.push(filters.type);
    }

    if (filters.item_id) {
      query += ' AND sm.item_id = ?';
      params.push(filters.item_id);
    }

    if (filters.movement_type) {
      query += ' AND sm.movement_type = ?';
      params.push(filters.movement_type);
    }

    if (filters.user_id) {
      query += ' AND sm.user_id = ?';
      params.push(filters.user_id);
    }

    if (filters.date_from) {
      query += ' AND DATE(sm.created_at) >= ?';
      params.push(filters.date_from);
    }

    if (filters.date_to) {
      query += ' AND DATE(sm.created_at) <= ?';
      params.push(filters.date_to);
    }

    query += ' ORDER BY sm.created_at DESC';

    return await executeQuery<StockMovement>(query, params);
  }

  // Criar movimentação de estoque
  static async create(movementData: CreateStockMovementRequest, userId: number): Promise<StockMovement> {
    // Verificar se item existe
    if (movementData.type === 'product') {
      const product = await ProductModel.findById(movementData.item_id);
      if (!product) {
        throw new Error('Product not found');
      }
      if (!product.stock_control) {
        throw new Error('Product does not have stock control enabled');
      }
    } else {
      const ingredient = await IngredientModel.findById(movementData.item_id);
      if (!ingredient) {
        throw new Error('Ingredient not found');
      }
    }

    // Calcular custo total
    const totalCost = movementData.unit_cost ? movementData.unit_cost * movementData.quantity : null;

    // Inserir movimentação
    const result = await executeUpdate(
      `INSERT INTO stock_movements (
        type, item_id, movement_type, quantity, unit_cost, total_cost, 
        reason, order_id, user_id
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        movementData.type,
        movementData.item_id,
        movementData.movement_type,
        movementData.quantity,
        movementData.unit_cost || null,
        totalCost,
        movementData.reason || null,
        movementData.order_id || null,
        userId,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create stock movement');
    }

    // Atualizar estoque do item
    await this.updateItemStock(movementData);

    // Retornar movimentação criada
    const newMovement = await this.findById(result.lastID);
    if (!newMovement) {
      throw new Error('Failed to retrieve created stock movement');
    }

    return newMovement;
  }

  // Atualizar estoque do item baseado na movimentação
  private static async updateItemStock(movementData: CreateStockMovementRequest): Promise<void> {
    const quantity = movementData.quantity;
    let operation: 'add' | 'subtract' | 'set';

    switch (movementData.movement_type) {
      case 'in':
        operation = 'add';
        break;
      case 'out':
        operation = 'subtract';
        break;
      case 'adjustment':
        operation = 'set';
        break;
      default:
        throw new Error('Invalid movement type');
    }

    if (movementData.type === 'product') {
      await ProductModel.updateStock(movementData.item_id, quantity, operation);
    } else {
      await IngredientModel.updateStock(movementData.item_id, quantity, operation);
    }
  }

  // Buscar movimentações por produto
  static async findByProduct(productId: number): Promise<StockMovement[]> {
    return await executeQuery<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name, p.name as item_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       LEFT JOIN products p ON sm.item_id = p.id
       WHERE sm.type = 'product' AND sm.item_id = ?
       ORDER BY sm.created_at DESC`,
      [productId]
    );
  }

  // Buscar movimentações por ingrediente
  static async findByIngredient(ingredientId: number): Promise<StockMovement[]> {
    return await executeQuery<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name, i.name as item_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       LEFT JOIN ingredients i ON sm.item_id = i.id
       WHERE sm.type = 'ingredient' AND sm.item_id = ?
       ORDER BY sm.created_at DESC`,
      [ingredientId]
    );
  }

  // Buscar movimentações por pedido
  static async findByOrder(orderId: number): Promise<StockMovement[]> {
    return await executeQuery<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       WHERE sm.order_id = ?
       ORDER BY sm.created_at DESC`,
      [orderId]
    );
  }

  // Buscar movimentações por usuário
  static async findByUser(userId: number): Promise<StockMovement[]> {
    return await executeQuery<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       WHERE sm.user_id = ?
       ORDER BY sm.created_at DESC`,
      [userId]
    );
  }

  // Obter movimentações do dia
  static async findToday(): Promise<StockMovement[]> {
    return await executeQuery<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       WHERE DATE(sm.created_at) = DATE('now')
       ORDER BY sm.created_at DESC`
    );
  }

  // Obter estatísticas de movimentações
  static async getStats(dateFrom?: string, dateTo?: string): Promise<{
    total_movements: number;
    total_in: number;
    total_out: number;
    total_adjustments: number;
    total_value_in: number;
    total_value_out: number;
  }> {
    let query = `
      SELECT 
        COUNT(*) as total_movements,
        SUM(CASE WHEN movement_type = 'in' THEN 1 ELSE 0 END) as total_in,
        SUM(CASE WHEN movement_type = 'out' THEN 1 ELSE 0 END) as total_out,
        SUM(CASE WHEN movement_type = 'adjustment' THEN 1 ELSE 0 END) as total_adjustments,
        COALESCE(SUM(CASE WHEN movement_type = 'in' THEN total_cost ELSE 0 END), 0) as total_value_in,
        COALESCE(SUM(CASE WHEN movement_type = 'out' THEN total_cost ELSE 0 END), 0) as total_value_out
      FROM stock_movements
      WHERE 1=1
    `;
    const params: any[] = [];

    if (dateFrom) {
      query += ' AND DATE(created_at) >= ?';
      params.push(dateFrom);
    }

    if (dateTo) {
      query += ' AND DATE(created_at) <= ?';
      params.push(dateTo);
    }

    const stats = await executeQuerySingle<{
      total_movements: number;
      total_in: number;
      total_out: number;
      total_adjustments: number;
      total_value_in: number;
      total_value_out: number;
    }>(query, params);

    return stats || {
      total_movements: 0,
      total_in: 0,
      total_out: 0,
      total_adjustments: 0,
      total_value_in: 0,
      total_value_out: 0,
    };
  }

  // Obter movimentações mais recentes
  static async findRecent(limit: number = 10): Promise<StockMovement[]> {
    return await executeQuery<StockMovement>(
      `SELECT sm.*, u.username, u.full_name as user_name,
              CASE 
                WHEN sm.type = 'product' THEN p.name
                WHEN sm.type = 'ingredient' THEN i.name
              END as item_name
       FROM stock_movements sm
       LEFT JOIN users u ON sm.user_id = u.id
       LEFT JOIN products p ON sm.type = 'product' AND sm.item_id = p.id
       LEFT JOIN ingredients i ON sm.type = 'ingredient' AND sm.item_id = i.id
       ORDER BY sm.created_at DESC
       LIMIT ?`,
      [limit]
    );
  }

  // Registrar entrada de estoque
  static async registerStockIn(
    type: 'product' | 'ingredient',
    itemId: number,
    quantity: number,
    unitCost: number,
    reason: string,
    userId: number
  ): Promise<StockMovement> {
    return await this.create({
      type,
      item_id: itemId,
      movement_type: 'in',
      quantity,
      unit_cost: unitCost,
      reason,
    }, userId);
  }

  // Registrar saída de estoque
  static async registerStockOut(
    type: 'product' | 'ingredient',
    itemId: number,
    quantity: number,
    reason: string,
    userId: number,
    orderId?: number
  ): Promise<StockMovement> {
    return await this.create({
      type,
      item_id: itemId,
      movement_type: 'out',
      quantity,
      reason,
      order_id: orderId,
    }, userId);
  }

  // Registrar ajuste de estoque
  static async registerStockAdjustment(
    type: 'product' | 'ingredient',
    itemId: number,
    newQuantity: number,
    reason: string,
    userId: number
  ): Promise<StockMovement> {
    return await this.create({
      type,
      item_id: itemId,
      movement_type: 'adjustment',
      quantity: newQuantity,
      reason,
    }, userId);
  }

  // Obter histórico de preços (baseado nas movimentações de entrada)
  static async getPriceHistory(type: 'product' | 'ingredient', itemId: number): Promise<Array<{
    date: string;
    unit_cost: number;
    quantity: number;
  }>> {
    return await executeQuery<{
      date: string;
      unit_cost: number;
      quantity: number;
    }>(
      `SELECT DATE(created_at) as date, unit_cost, quantity
       FROM stock_movements
       WHERE type = ? AND item_id = ? AND movement_type = 'in' AND unit_cost IS NOT NULL
       ORDER BY created_at DESC`,
      [type, itemId]
    );
  }
}
