import { useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../services/auth';

interface KeyboardShortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: () => void;
  description: string;
  scope?: string[];
}

/**
 * Hook para gerenciar atalhos de teclado globais
 */
export const useKeyboardShortcuts = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuthStore();

  // Definir atalhos de teclado
  const shortcuts: KeyboardShortcut[] = [
    // Navegação geral
    {
      key: 'h',
      ctrlKey: true,
      action: () => navigate('/dashboard'),
      description: 'Ir para Dashboard',
    },
    {
      key: 'p',
      ctrlKey: true,
      action: () => navigate('/pos'),
      description: 'Abrir PDV',
      scope: ['manager', 'cashier'],
    },
    {
      key: 'k',
      ctrlKey: true,
      action: () => navigate('/kitchen'),
      description: '<PERSON><PERSON><PERSON>',
      scope: ['manager', 'kitchen'],
    },
    {
      key: 'c',
      ctrlKey: true,
      action: () => navigate('/cashier'),
      description: 'Abrir Caixa',
      scope: ['manager', 'cashier'],
    },
    {
      key: 'r',
      ctrlKey: true,
      action: () => navigate('/reports'),
      description: 'Abrir Relatórios',
      scope: ['manager'],
    },
    {
      key: 's',
      ctrlKey: true,
      action: () => navigate('/sync'),
      description: 'Abrir Sincronização',
      scope: ['manager'],
    },

    // Ações rápidas
    {
      key: 'n',
      ctrlKey: true,
      shiftKey: true,
      action: () => {
        if (window.location.pathname === '/pos') {
          // Novo pedido no PDV
          window.dispatchEvent(new CustomEvent('pdv:new-order'));
        } else if (window.location.pathname === '/products') {
          // Novo produto
          window.dispatchEvent(new CustomEvent('products:new'));
        }
      },
      description: 'Novo item (contexto atual)',
    },
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        // Focar na busca
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="buscar"], input[placeholder*="Buscar"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
          searchInput.select();
        }
      },
      description: 'Focar na busca',
    },
    {
      key: 'Escape',
      action: () => {
        // Fechar modais/dialogs
        const closeButtons = document.querySelectorAll('[aria-label="close"], [data-testid="close-button"]');
        if (closeButtons.length > 0) {
          (closeButtons[closeButtons.length - 1] as HTMLElement).click();
        }
      },
      description: 'Fechar modal/dialog',
    },

    // PDV específico
    {
      key: 'F1',
      action: () => {
        if (window.location.pathname === '/pos') {
          window.dispatchEvent(new CustomEvent('pdv:payment'));
        }
      },
      description: 'Finalizar pedido (PDV)',
      scope: ['manager', 'cashier'],
    },
    {
      key: 'F2',
      action: () => {
        if (window.location.pathname === '/pos') {
          window.dispatchEvent(new CustomEvent('pdv:cancel'));
        }
      },
      description: 'Cancelar pedido (PDV)',
      scope: ['manager', 'cashier'],
    },
    {
      key: 'F3',
      action: () => {
        if (window.location.pathname === '/pos') {
          window.dispatchEvent(new CustomEvent('pdv:hold'));
        }
      },
      description: 'Suspender pedido (PDV)',
      scope: ['manager', 'cashier'],
    },

    // Cozinha específico
    {
      key: 'F5',
      action: () => {
        if (window.location.pathname === '/kitchen') {
          window.dispatchEvent(new CustomEvent('kitchen:refresh'));
        }
      },
      description: 'Atualizar pedidos (Cozinha)',
      scope: ['manager', 'kitchen'],
    },
    {
      key: 'F6',
      action: () => {
        if (window.location.pathname === '/kitchen') {
          window.dispatchEvent(new CustomEvent('kitchen:ready-all'));
        }
      },
      description: 'Marcar todos como prontos (Cozinha)',
      scope: ['manager', 'kitchen'],
    },

    // Sistema
    {
      key: 'F9',
      action: () => {
        // Toggle modo escuro (quando implementado)
        window.dispatchEvent(new CustomEvent('theme:toggle'));
      },
      description: 'Alternar tema escuro/claro',
    },
    {
      key: 'F11',
      action: () => {
        // Toggle fullscreen
        if (!document.fullscreenElement) {
          document.documentElement.requestFullscreen();
        } else {
          document.exitFullscreen();
        }
      },
      description: 'Tela cheia',
    },
    {
      key: 'F12',
      ctrlKey: true,
      action: () => {
        // Logout
        logout();
        navigate('/login');
      },
      description: 'Logout',
    },

    // Ajuda
    {
      key: '?',
      shiftKey: true,
      action: () => {
        window.dispatchEvent(new CustomEvent('help:show-shortcuts'));
      },
      description: 'Mostrar atalhos',
    },
  ];

  // Verificar se o usuário tem permissão para o atalho
  const hasPermission = useCallback((shortcut: KeyboardShortcut): boolean => {
    if (!shortcut.scope || !user) return true;
    return shortcut.scope.includes(user.role);
  }, [user]);

  // Handler de eventos de teclado
  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Ignorar se estiver digitando em um input
    const target = event.target as HTMLElement;
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.isContentEditable) {
      // Permitir apenas alguns atalhos específicos em inputs
      const allowedInInputs = ['Escape', 'F1', 'F2', 'F3', 'F5', 'F6', 'F9', 'F11'];
      if (!allowedInInputs.includes(event.key)) {
        return;
      }
    }

    // Procurar atalho correspondente
    const shortcut = shortcuts.find(s => 
      s.key === event.key &&
      !!s.ctrlKey === event.ctrlKey &&
      !!s.altKey === event.altKey &&
      !!s.shiftKey === event.shiftKey &&
      hasPermission(s)
    );

    if (shortcut) {
      event.preventDefault();
      event.stopPropagation();
      shortcut.action();
    }
  }, [shortcuts, hasPermission]);

  // Registrar/desregistrar event listeners
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);

  // Retornar atalhos disponíveis para o usuário atual
  const getAvailableShortcuts = useCallback(() => {
    return shortcuts.filter(hasPermission);
  }, [shortcuts, hasPermission]);

  return {
    shortcuts: getAvailableShortcuts(),
  };
};

/**
 * Hook para mostrar modal de ajuda com atalhos
 */
export const useShortcutsHelp = () => {
  const { shortcuts } = useKeyboardShortcuts();

  useEffect(() => {
    const handleShowShortcuts = () => {
      // Criar modal de atalhos
      const modal = document.createElement('div');
      modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      `;

      const content = document.createElement('div');
      content.style.cssText = `
        background: white;
        border-radius: 8px;
        padding: 24px;
        max-width: 600px;
        max-height: 80vh;
        overflow-y: auto;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
      `;

      content.innerHTML = `
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
          <h2 style="margin: 0; color: #1976d2;">Atalhos de Teclado</h2>
          <button id="close-shortcuts" style="background: none; border: none; font-size: 24px; cursor: pointer; color: #666;">&times;</button>
        </div>
        <div style="display: grid; gap: 12px;">
          ${shortcuts.map(shortcut => {
            const keys = [];
            if (shortcut.ctrlKey) keys.push('Ctrl');
            if (shortcut.altKey) keys.push('Alt');
            if (shortcut.shiftKey) keys.push('Shift');
            keys.push(shortcut.key);
            
            return `
              <div style="display: flex; justify-content: space-between; align-items: center; padding: 8px 0; border-bottom: 1px solid #eee;">
                <span style="color: #333;">${shortcut.description}</span>
                <code style="background: #f5f5f5; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
                  ${keys.join(' + ')}
                </code>
              </div>
            `;
          }).join('')}
        </div>
        <div style="margin-top: 20px; text-align: center; color: #666; font-size: 14px;">
          Pressione <code style="background: #f5f5f5; padding: 2px 4px; border-radius: 2px;">Esc</code> para fechar
        </div>
      `;

      modal.appendChild(content);
      document.body.appendChild(modal);

      // Fechar modal
      const closeModal = () => {
        document.body.removeChild(modal);
      };

      // Event listeners
      modal.addEventListener('click', (e) => {
        if (e.target === modal) closeModal();
      });
      
      content.querySelector('#close-shortcuts')?.addEventListener('click', closeModal);
      
      const handleEscape = (e: KeyboardEvent) => {
        if (e.key === 'Escape') {
          closeModal();
          document.removeEventListener('keydown', handleEscape);
        }
      };
      
      document.addEventListener('keydown', handleEscape);
    };

    document.addEventListener('help:show-shortcuts', handleShowShortcuts);
    
    return () => {
      document.removeEventListener('help:show-shortcuts', handleShowShortcuts);
    };
  }, [shortcuts]);
};
