import { 
  AnalyticsRequest, 
  AnalyticsResponse, 
  AnalyticsInsight, 
  InsightType, 
  InsightCategory,
  ReportFilter 
} from '../types/reports';
import { ReportsModel } from '../models/ReportsModel';

/**
 * Serviço de Analytics com IA
 * Integra com GPT-4OMINI para gerar insights automáticos
 */
export class AnalyticsService {
  private static instance: AnalyticsService;
  private apiKey: string;
  private baseUrl: string = 'https://api.openai.com/v1';

  private constructor() {
    this.apiKey = process.env.OPENAI_API_KEY || '';
  }

  static getInstance(): AnalyticsService {
    if (!AnalyticsService.instance) {
      AnalyticsService.instance = new AnalyticsService();
    }
    return AnalyticsService.instance;
  }

  // Gerar análise completa
  async generateAnalytics(request: AnalyticsRequest): Promise<AnalyticsResponse> {
    try {
      // Coletar dados dos relatórios
      const reportFilter: ReportFilter = {
        date_from: request.period.start,
        date_to: request.period.end,
      };

      const [salesReport, productReport, financialReport] = await Promise.all([
        ReportsModel.generateSalesReport(reportFilter),
        ReportsModel.generateProductReport(reportFilter),
        ReportsModel.generateFinancialReport(reportFilter),
      ]);

      // Gerar insights com IA
      const insights = await this.generateInsights({
        sales: salesReport,
        products: productReport,
        financial: financialReport,
      }, request.focus_areas);

      // Gerar predições se solicitado
      const predictions = request.include_predictions 
        ? await this.generatePredictions(salesReport, financialReport)
        : [];

      // Gerar recomendações
      const recommendations = await this.generateRecommendations(insights);

      return {
        period: request.period,
        insights,
        summary: {
          total_insights: insights.length,
          high_impact_count: insights.filter(i => i.impact === 'high').length,
          categories_analyzed: request.focus_areas,
          confidence_average: insights.reduce((sum, i) => sum + i.confidence, 0) / insights.length,
        },
        predictions,
        recommendations,
      };
    } catch (error) {
      console.error('Analytics generation error:', error);
      throw new Error('Failed to generate analytics');
    }
  }

  // Gerar insights com IA
  private async generateInsights(data: any, focusAreas: InsightCategory[]): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = [];

    // Análise de vendas
    if (focusAreas.includes('sales')) {
      insights.push(...await this.analyzeSalesData(data.sales));
    }

    // Análise de produtos
    if (focusAreas.includes('products')) {
      insights.push(...await this.analyzeProductData(data.products));
    }

    // Análise financeira
    if (focusAreas.includes('finance')) {
      insights.push(...await this.analyzeFinancialData(data.financial));
    }

    // Análise de operações
    if (focusAreas.includes('operations')) {
      insights.push(...await this.analyzeOperationalData(data));
    }

    return insights;
  }

  // Análise de dados de vendas
  private async analyzeSalesData(salesData: any): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = [];

    // Análise de tendência de vendas
    if (salesData.by_period && salesData.by_period.length > 1) {
      const trend = this.calculateTrend(salesData.by_period.map((p: any) => p.amount));
      
      if (Math.abs(trend) > 10) {
        insights.push({
          id: `sales_trend_${Date.now()}`,
          type: 'trend_analysis',
          title: trend > 0 ? 'Tendência de Crescimento nas Vendas' : 'Tendência de Queda nas Vendas',
          description: `As vendas ${trend > 0 ? 'cresceram' : 'diminuíram'} ${Math.abs(trend).toFixed(1)}% no período analisado.`,
          impact: Math.abs(trend) > 20 ? 'high' : 'medium',
          category: 'sales',
          data: { trend_percentage: trend, period_data: salesData.by_period },
          recommendations: trend > 0 
            ? ['Manter estratégias atuais', 'Considerar expansão de horários', 'Investir em marketing']
            : ['Revisar estratégia de preços', 'Analisar concorrência', 'Melhorar experiência do cliente'],
          confidence: 0.85,
          created_at: new Date().toISOString(),
        });
      }
    }

    // Análise de picos de vendas por hora
    if (salesData.by_hour) {
      const peakHour = salesData.by_hour.reduce((max: any, hour: any) => 
        hour.amount > max.amount ? hour : max, salesData.by_hour[0]);
      
      if (peakHour && peakHour.amount > 0) {
        insights.push({
          id: `peak_hour_${Date.now()}`,
          type: 'performance_optimization',
          title: 'Pico de Vendas Identificado',
          description: `O horário de maior movimento é às ${peakHour.hour}h, representando o pico de vendas do dia.`,
          impact: 'medium',
          category: 'operations',
          data: { peak_hour: peakHour.hour, peak_amount: peakHour.amount },
          recommendations: [
            'Garantir equipe completa no horário de pico',
            'Preparar ingredientes antecipadamente',
            'Considerar promoções em horários de menor movimento'
          ],
          confidence: 0.9,
          created_at: new Date().toISOString(),
        });
      }
    }

    // Análise de ticket médio
    if (salesData.summary.average_ticket) {
      const avgTicket = salesData.summary.average_ticket;
      const industryAverage = 45; // Valor de referência do setor
      
      if (avgTicket < industryAverage * 0.8) {
        insights.push({
          id: `low_ticket_${Date.now()}`,
          type: 'revenue_opportunity',
          title: 'Oportunidade de Aumento do Ticket Médio',
          description: `O ticket médio atual (R$ ${avgTicket.toFixed(2)}) está abaixo da média do setor (R$ ${industryAverage}).`,
          impact: 'high',
          category: 'sales',
          data: { current_ticket: avgTicket, industry_average: industryAverage },
          recommendations: [
            'Implementar estratégias de upselling',
            'Criar combos e promoções',
            'Treinar equipe em técnicas de venda',
            'Revisar cardápio e preços'
          ],
          confidence: 0.8,
          created_at: new Date().toISOString(),
        });
      }
    }

    return insights;
  }

  // Análise de dados de produtos
  private async analyzeProductData(productData: any): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = [];

    // Análise de produtos com baixo estoque
    if (productData.low_stock && productData.low_stock.length > 0) {
      const criticalItems = productData.low_stock.filter((item: any) => item.status === 'critical');
      
      if (criticalItems.length > 0) {
        insights.push({
          id: `critical_stock_${Date.now()}`,
          type: 'inventory_optimization',
          title: 'Produtos com Estoque Crítico',
          description: `${criticalItems.length} produto(s) estão com estoque zerado ou crítico.`,
          impact: 'high',
          category: 'products',
          data: { critical_items: criticalItems },
          recommendations: [
            'Reabastecer produtos críticos imediatamente',
            'Revisar política de estoque mínimo',
            'Implementar alertas automáticos',
            'Considerar fornecedores alternativos'
          ],
          confidence: 0.95,
          created_at: new Date().toISOString(),
        });
      }
    }

    // Análise de produtos mais vendidos
    if (productData.by_product && productData.by_product.length > 0) {
      const topProduct = productData.by_product[0];
      const totalRevenue = productData.summary.total_amount;
      
      if (topProduct && totalRevenue > 0) {
        const productShare = (topProduct.amount / totalRevenue) * 100;
        
        if (productShare > 30) {
          insights.push({
            id: `product_concentration_${Date.now()}`,
            type: 'revenue_opportunity',
            title: 'Alta Concentração em Produto Único',
            description: `O produto "${topProduct.product_name}" representa ${productShare.toFixed(1)}% das vendas.`,
            impact: 'medium',
            category: 'products',
            data: { top_product: topProduct, share_percentage: productShare },
            recommendations: [
              'Diversificar cardápio para reduzir dependência',
              'Criar variações do produto principal',
              'Promover outros produtos',
              'Analisar margem de lucro do produto principal'
            ],
            confidence: 0.8,
            created_at: new Date().toISOString(),
          });
        }
      }
    }

    // Análise de margem de lucro
    if (productData.by_product) {
      const lowMarginProducts = productData.by_product.filter((p: any) => p.margin < 20);
      
      if (lowMarginProducts.length > 0) {
        insights.push({
          id: `low_margin_${Date.now()}`,
          type: 'cost_reduction',
          title: 'Produtos com Margem Baixa',
          description: `${lowMarginProducts.length} produto(s) têm margem de lucro abaixo de 20%.`,
          impact: 'medium',
          category: 'finance',
          data: { low_margin_products: lowMarginProducts },
          recommendations: [
            'Revisar preços dos produtos com baixa margem',
            'Negociar melhores preços com fornecedores',
            'Considerar substituir ingredientes',
            'Avaliar descontinuação de produtos não rentáveis'
          ],
          confidence: 0.85,
          created_at: new Date().toISOString(),
        });
      }
    }

    return insights;
  }

  // Análise de dados financeiros
  private async analyzeFinancialData(financialData: any): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = [];

    // Análise de margem de lucro
    if (financialData.summary.margin) {
      const margin = financialData.summary.margin;
      const industryAverage = 25; // Margem média do setor
      
      if (margin < industryAverage * 0.8) {
        insights.push({
          id: `low_profit_margin_${Date.now()}`,
          type: 'cost_reduction',
          title: 'Margem de Lucro Abaixo da Média',
          description: `A margem de lucro atual (${margin.toFixed(1)}%) está abaixo da média do setor (${industryAverage}%).`,
          impact: 'high',
          category: 'finance',
          data: { current_margin: margin, industry_average: industryAverage },
          recommendations: [
            'Revisar estrutura de custos',
            'Otimizar processos operacionais',
            'Renegociar contratos com fornecedores',
            'Implementar controle rigoroso de desperdícios'
          ],
          confidence: 0.9,
          created_at: new Date().toISOString(),
        });
      }
    }

    // Análise de métodos de pagamento
    if (financialData.by_payment_method) {
      const cardPayments = financialData.by_payment_method.find((p: any) => p.method === 'card');
      
      if (cardPayments && cardPayments.percentage > 70) {
        insights.push({
          id: `high_card_usage_${Date.now()}`,
          type: 'cost_reduction',
          title: 'Alto Uso de Pagamentos com Cartão',
          description: `${cardPayments.percentage.toFixed(1)}% dos pagamentos são feitos com cartão, gerando taxas elevadas.`,
          impact: 'medium',
          category: 'finance',
          data: { card_percentage: cardPayments.percentage, fees: cardPayments.fees },
          recommendations: [
            'Incentivar pagamentos em dinheiro com desconto',
            'Negociar taxas menores com adquirentes',
            'Implementar PIX como alternativa',
            'Estabelecer valor mínimo para cartão'
          ],
          confidence: 0.8,
          created_at: new Date().toISOString(),
        });
      }
    }

    return insights;
  }

  // Análise de dados operacionais
  private async analyzeOperationalData(data: any): Promise<AnalyticsInsight[]> {
    const insights: AnalyticsInsight[] = [];

    // Análise baseada em padrões de vendas
    if (data.sales.by_hour) {
      const hourlyData = data.sales.by_hour;
      const lowActivityHours = hourlyData.filter((h: any) => h.orders < 2);
      
      if (lowActivityHours.length > 0) {
        insights.push({
          id: `low_activity_hours_${Date.now()}`,
          type: 'performance_optimization',
          title: 'Horários de Baixa Atividade',
          description: `${lowActivityHours.length} horário(s) têm menos de 2 pedidos por hora.`,
          impact: 'medium',
          category: 'operations',
          data: { low_activity_hours: lowActivityHours },
          recommendations: [
            'Criar promoções para horários de baixo movimento',
            'Ajustar escala de funcionários',
            'Implementar happy hour',
            'Considerar delivery em horários específicos'
          ],
          confidence: 0.75,
          created_at: new Date().toISOString(),
        });
      }
    }

    return insights;
  }

  // Gerar predições
  private async generatePredictions(salesData: any, financialData: any): Promise<any[]> {
    const predictions = [];

    // Predição de receita
    if (salesData.by_period && salesData.by_period.length > 1) {
      const trend = this.calculateTrend(salesData.by_period.map((p: any) => p.amount));
      const lastAmount = salesData.by_period[salesData.by_period.length - 1].amount;
      const predictedAmount = lastAmount * (1 + trend / 100);

      predictions.push({
        metric: 'Receita Diária',
        current_value: lastAmount,
        predicted_value: predictedAmount,
        trend: trend > 0 ? 'up' : trend < 0 ? 'down' : 'stable',
        confidence: 0.7,
      });
    }

    // Predição de margem
    if (financialData.summary.margin) {
      predictions.push({
        metric: 'Margem de Lucro',
        current_value: financialData.summary.margin,
        predicted_value: financialData.summary.margin * 1.05, // Simulado: 5% de melhoria
        trend: 'up',
        confidence: 0.6,
      });
    }

    return predictions;
  }

  // Gerar recomendações
  private async generateRecommendations(insights: AnalyticsInsight[]): Promise<any[]> {
    const recommendations = [];

    // Agrupar recomendações por prioridade
    const highImpactInsights = insights.filter(i => i.impact === 'high');
    
    for (const insight of highImpactInsights) {
      for (const rec of insight.recommendations) {
        recommendations.push({
          priority: 'high',
          category: insight.category,
          action: rec,
          expected_impact: this.getExpectedImpact(insight.type),
          implementation_effort: this.getImplementationEffort(rec),
        });
      }
    }

    return recommendations.slice(0, 10); // Limitar a 10 recomendações
  }

  // Utilitários
  private calculateTrend(values: number[]): number {
    if (values.length < 2) return 0;
    
    const firstHalf = values.slice(0, Math.floor(values.length / 2));
    const secondHalf = values.slice(Math.floor(values.length / 2));
    
    const firstAvg = firstHalf.reduce((sum, val) => sum + val, 0) / firstHalf.length;
    const secondAvg = secondHalf.reduce((sum, val) => sum + val, 0) / secondHalf.length;
    
    return firstAvg > 0 ? ((secondAvg - firstAvg) / firstAvg) * 100 : 0;
  }

  private getExpectedImpact(type: InsightType): string {
    const impacts = {
      'trend_analysis': 'Melhoria na compreensão de padrões',
      'anomaly_detection': 'Prevenção de problemas',
      'performance_optimization': 'Aumento de 10-15% na eficiência',
      'revenue_opportunity': 'Aumento de 5-20% na receita',
      'cost_reduction': 'Redução de 5-15% nos custos',
      'customer_behavior': 'Melhoria na satisfação do cliente',
      'inventory_optimization': 'Redução de 20% no desperdício',
      'staff_productivity': 'Aumento de 10% na produtividade',
    };
    
    return impacts[type] || 'Impacto positivo esperado';
  }

  private getImplementationEffort(recommendation: string): 'low' | 'medium' | 'high' {
    const lowEffort = ['alertas', 'treinar', 'promover', 'incentivar'];
    const highEffort = ['implementar', 'expandir', 'contratar', 'substituir'];
    
    const lowerRec = recommendation.toLowerCase();
    
    if (lowEffort.some(word => lowerRec.includes(word))) return 'low';
    if (highEffort.some(word => lowerRec.includes(word))) return 'high';
    
    return 'medium';
  }
}

// Instância singleton
export const analyticsService = AnalyticsService.getInstance();
