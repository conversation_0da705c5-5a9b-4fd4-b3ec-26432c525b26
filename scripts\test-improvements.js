#!/usr/bin/env node

/**
 * <PERSON>ript para testar as me<PERSON><PERSON><PERSON> da <PERSON>ase 11
 * Testa performance, logging, cache e outras otimizações
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m',
  magenta: '\x1b[35m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar sistema de logging
async function testLogging() {
  log('\n📝 Testando Sistema de Logging...', 'blue');

  try {
    // Fazer várias requisições para gerar logs
    log('Gerando logs de requisições...', 'cyan');
    
    const requests = [
      axios.get(`${API_BASE_URL}/health`),
      axios.get(`${API_BASE_URL}/products`, { headers: getHeaders() }),
      axios.get(`${API_BASE_URL}/orders`, { headers: getHeaders() }),
      axios.get(`${API_BASE_URL}/sync/metrics`, { headers: getHeaders() }),
    ];

    await Promise.all(requests);
    log('✅ Logs de requisições gerados', 'green');

    // Verificar se arquivos de log foram criados
    const logDir = path.join(__dirname, '..', 'backend', 'logs');
    if (fs.existsSync(logDir)) {
      const logFiles = fs.readdirSync(logDir);
      log(`✅ Arquivos de log encontrados: ${logFiles.length}`, 'green');
      logFiles.forEach(file => {
        log(`   - ${file}`, 'cyan');
      });
    } else {
      log('⚠️ Diretório de logs não encontrado', 'yellow');
    }

    return true;
  } catch (error) {
    log(`❌ Erro no teste de logging: ${error.message}`, 'red');
    return false;
  }
}

// Testar performance da API
async function testPerformance() {
  log('\n⚡ Testando Performance da API...', 'blue');

  try {
    const endpoints = [
      '/health',
      '/products',
      '/orders',
      '/sync/metrics',
      '/reports/sales-summary',
    ];

    const results = [];

    for (const endpoint of endpoints) {
      log(`Testando ${endpoint}...`, 'cyan');
      
      const times = [];
      const iterations = 5;

      for (let i = 0; i < iterations; i++) {
        const start = Date.now();
        try {
          await axios.get(`${API_BASE_URL}${endpoint}`, { 
            headers: getHeaders(),
            timeout: 10000 
          });
          const duration = Date.now() - start;
          times.push(duration);
        } catch (error) {
          if (error.response?.status !== 404) {
            log(`   ⚠️ Erro em ${endpoint}: ${error.message}`, 'yellow');
          }
          times.push(10000); // Timeout como penalidade
        }
      }

      const avgTime = times.reduce((a, b) => a + b, 0) / times.length;
      const minTime = Math.min(...times);
      const maxTime = Math.max(...times);

      results.push({
        endpoint,
        avgTime: avgTime.toFixed(2),
        minTime,
        maxTime,
        status: avgTime < 1000 ? 'Bom' : avgTime < 3000 ? 'Aceitável' : 'Lento'
      });

      const statusColor = avgTime < 1000 ? 'green' : avgTime < 3000 ? 'yellow' : 'red';
      log(`   ✅ ${endpoint}: ${avgTime.toFixed(2)}ms (${minTime}-${maxTime}ms)`, statusColor);
    }

    // Resumo de performance
    log('\n📊 Resumo de Performance:', 'magenta');
    results.forEach(result => {
      const color = result.status === 'Bom' ? 'green' : result.status === 'Aceitável' ? 'yellow' : 'red';
      log(`   ${result.endpoint}: ${result.avgTime}ms - ${result.status}`, color);
    });

    return true;
  } catch (error) {
    log(`❌ Erro no teste de performance: ${error.message}`, 'red');
    return false;
  }
}

// Testar rate limiting
async function testRateLimit() {
  log('\n🚦 Testando Rate Limiting...', 'blue');

  try {
    log('Fazendo múltiplas requisições rapidamente...', 'cyan');
    
    const requests = [];
    const numRequests = 20;

    for (let i = 0; i < numRequests; i++) {
      requests.push(
        axios.get(`${API_BASE_URL}/health`).catch(error => ({
          status: error.response?.status,
          error: error.response?.data?.error
        }))
      );
    }

    const responses = await Promise.all(requests);
    
    const successful = responses.filter(r => r.status !== 429).length;
    const rateLimited = responses.filter(r => r.status === 429).length;

    log(`✅ Requisições bem-sucedidas: ${successful}`, 'green');
    log(`🚦 Requisições limitadas: ${rateLimited}`, 'yellow');

    if (rateLimited > 0) {
      log('✅ Rate limiting funcionando corretamente', 'green');
    } else {
      log('⚠️ Rate limiting pode não estar ativo', 'yellow');
    }

    return true;
  } catch (error) {
    log(`❌ Erro no teste de rate limiting: ${error.message}`, 'red');
    return false;
  }
}

// Testar compressão
async function testCompression() {
  log('\n🗜️ Testando Compressão...', 'blue');

  try {
    log('Testando compressão de resposta...', 'cyan');
    
    const response = await axios.get(`${API_BASE_URL}/products`, {
      headers: {
        ...getHeaders(),
        'Accept-Encoding': 'gzip, deflate'
      }
    });

    const contentEncoding = response.headers['content-encoding'];
    const contentLength = response.headers['content-length'];

    if (contentEncoding) {
      log(`✅ Compressão ativa: ${contentEncoding}`, 'green');
      log(`   Tamanho da resposta: ${contentLength} bytes`, 'cyan');
    } else {
      log('⚠️ Compressão não detectada', 'yellow');
    }

    return true;
  } catch (error) {
    log(`❌ Erro no teste de compressão: ${error.message}`, 'red');
    return false;
  }
}

// Testar headers de segurança
async function testSecurityHeaders() {
  log('\n🔒 Testando Headers de Segurança...', 'blue');

  try {
    log('Verificando headers de segurança...', 'cyan');
    
    const response = await axios.get(`${API_BASE_URL}/health`);
    const headers = response.headers;

    const securityHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'strict-transport-security',
      'content-security-policy'
    ];

    let foundHeaders = 0;
    securityHeaders.forEach(header => {
      if (headers[header]) {
        log(`   ✅ ${header}: ${headers[header]}`, 'green');
        foundHeaders++;
      } else {
        log(`   ❌ ${header}: não encontrado`, 'red');
      }
    });

    log(`\n📊 Headers de segurança encontrados: ${foundHeaders}/${securityHeaders.length}`, 
         foundHeaders >= 3 ? 'green' : 'yellow');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de headers de segurança: ${error.message}`, 'red');
    return false;
  }
}

// Testar graceful shutdown
async function testGracefulShutdown() {
  log('\n🔄 Testando Graceful Shutdown...', 'blue');

  try {
    log('Verificando se o servidor responde a sinais...', 'cyan');
    
    // Fazer uma requisição para verificar se o servidor está rodando
    await axios.get(`${API_BASE_URL}/health`);
    log('✅ Servidor está respondendo', 'green');

    log('ℹ️ Para testar graceful shutdown, envie SIGTERM ou SIGINT para o processo do servidor', 'cyan');
    log('   O servidor deve fechar conexões graciosamente e limpar recursos', 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro no teste de graceful shutdown: ${error.message}`, 'red');
    return false;
  }
}

// Testar error handling
async function testErrorHandling() {
  log('\n🚨 Testando Error Handling...', 'blue');

  try {
    const errorTests = [
      {
        name: 'Endpoint não encontrado',
        request: () => axios.get(`${API_BASE_URL}/nonexistent`),
        expectedStatus: 404
      },
      {
        name: 'Dados inválidos',
        request: () => axios.post(`${API_BASE_URL}/auth/login`, { invalid: 'data' }),
        expectedStatus: 400
      },
      {
        name: 'Token inválido',
        request: () => axios.get(`${API_BASE_URL}/products`, {
          headers: { 'Authorization': 'Bearer invalid-token' }
        }),
        expectedStatus: 401
      }
    ];

    for (const test of errorTests) {
      log(`Testando: ${test.name}...`, 'cyan');
      
      try {
        await test.request();
        log(`   ❌ Deveria ter falhado com status ${test.expectedStatus}`, 'red');
      } catch (error) {
        if (error.response?.status === test.expectedStatus) {
          log(`   ✅ Status ${test.expectedStatus} retornado corretamente`, 'green');
          
          // Verificar se a resposta tem estrutura padrão
          const response = error.response.data;
          if (response.success === false && response.error && response.timestamp) {
            log(`   ✅ Estrutura de erro padrão encontrada`, 'green');
          } else {
            log(`   ⚠️ Estrutura de erro não padrão`, 'yellow');
          }
        } else {
          log(`   ❌ Status inesperado: ${error.response?.status}`, 'red');
        }
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro no teste de error handling: ${error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando Melhorias da Fase 11 - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      logging: await testLogging(),
      performance: await testPerformance(),
      rateLimit: await testRateLimit(),
      compression: await testCompression(),
      securityHeaders: await testSecurityHeaders(),
      gracefulShutdown: await testGracefulShutdown(),
      errorHandling: await testErrorHandling(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase().replace(/([A-Z])/g, ' $1').trim()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! Melhorias da Fase 11 funcionando corretamente.', 'green');
      log('\n🚀 Sistema otimizado e pronto para produção!', 'cyan');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

    // Estatísticas finais
    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.values(results).length;
    log(`\n📊 Estatísticas: ${passedCount}/${totalCount} testes passaram (${((passedCount/totalCount)*100).toFixed(1)}%)`, 'magenta');

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
