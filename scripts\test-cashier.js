#!/usr/bin/env node

/**
 * Script para testar as APIs de caixa
 * Testa CRUD de sessões de caixa e movimentações
 */

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001/api';

// Cores para output
const colors = {
  reset: '\x1b[0m',
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  cyan: '\x1b[36m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

let authToken = null;

// Fazer login para obter token
async function login() {
  try {
    log('🔐 Fazendo login...', 'blue');
    
    const response = await axios.post(`${API_BASE_URL}/auth/login`, {
      username: 'admin',
      password: 'password'
    });

    authToken = response.data.data.token;
    log('✅ Login realizado com sucesso', 'green');
    return true;
  } catch (error) {
    log(`❌ Erro no login: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Configurar headers com token
function getHeaders() {
  return {
    'Authorization': `Bearer ${authToken}`,
    'Content-Type': 'application/json'
  };
}

// Testar caixas registradoras
async function testCashRegisters() {
  log('\n💰 Testando Caixas Registradoras...', 'blue');

  try {
    // Listar caixas
    log('Listando caixas registradoras...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/cashier/registers`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} caixas encontrados`, 'green');

    // Listar caixas com status
    log('Listando caixas com status...', 'cyan');
    const statusResponse = await axios.get(`${API_BASE_URL}/cashier/registers?with_status=true`, {
      headers: getHeaders()
    });
    log(`✅ Caixas com status carregados`, 'green');

    // Listar caixas disponíveis
    log('Listando caixas disponíveis...', 'cyan');
    const availableResponse = await axios.get(`${API_BASE_URL}/cashier/registers/available`, {
      headers: getHeaders()
    });
    log(`✅ ${availableResponse.data.data.length} caixas disponíveis`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de caixas: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar sessões de caixa
async function testCashSessions() {
  log('\n🏦 Testando Sessões de Caixa...', 'blue');

  try {
    // Listar sessões
    log('Listando sessões de caixa...', 'cyan');
    const listResponse = await axios.get(`${API_BASE_URL}/cashier/sessions`, {
      headers: getHeaders()
    });
    log(`✅ ${listResponse.data.data.length} sessões encontradas`, 'green');

    // Buscar sessão ativa
    log('Buscando sessão ativa do usuário...', 'cyan');
    const activeResponse = await axios.get(`${API_BASE_URL}/cashier/sessions/active`, {
      headers: getHeaders()
    });
    
    if (activeResponse.data.data) {
      log(`✅ Sessão ativa encontrada: ID ${activeResponse.data.data.id}`, 'green');
    } else {
      log(`✅ Nenhuma sessão ativa (esperado)`, 'green');
    }

    // Obter métricas
    log('Obtendo métricas de caixa...', 'cyan');
    const metricsResponse = await axios.get(`${API_BASE_URL}/cashier/metrics`, {
      headers: getHeaders()
    });
    const metrics = metricsResponse.data.data;
    log(`✅ Métricas obtidas:`, 'green');
    log(`   - Sessões ativas: ${metrics.active_sessions}`, 'cyan');
    log(`   - Vendas hoje: R$ ${metrics.total_sales_today.toFixed(2)}`, 'cyan');
    log(`   - Transações: ${metrics.transactions_today}`, 'cyan');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de sessões: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar fluxo completo de caixa
async function testCashFlow() {
  log('\n🔄 Testando Fluxo Completo de Caixa...', 'blue');

  try {
    // Abrir sessão de caixa
    log('Abrindo sessão de caixa...', 'cyan');
    const openSessionData = {
      cash_register_id: 1,
      opening_balance: 200.00,
      notes: 'Teste de abertura de caixa'
    };

    const openResponse = await axios.post(`${API_BASE_URL}/cashier/sessions`, openSessionData, {
      headers: getHeaders()
    });
    
    const sessionId = openResponse.data.data.id;
    log(`✅ Sessão de caixa aberta com ID: ${sessionId}`, 'green');

    // Buscar sessão por ID
    log('Buscando sessão por ID...', 'cyan');
    const getSessionResponse = await axios.get(`${API_BASE_URL}/cashier/sessions/${sessionId}?include_movements=true`, {
      headers: getHeaders()
    });
    log(`✅ Sessão encontrada: ${getSessionResponse.data.data.cash_register_name}`, 'green');

    // Adicionar movimento de sangria
    log('Adicionando sangria...', 'cyan');
    const withdrawalData = {
      cash_session_id: sessionId,
      type: 'out',
      category: 'withdrawal',
      amount: 50.00,
      description: 'Sangria para troco'
    };

    await axios.post(`${API_BASE_URL}/cashier/movements`, withdrawalData, {
      headers: getHeaders()
    });
    log('✅ Sangria adicionada', 'green');

    // Adicionar movimento de suprimento
    log('Adicionando suprimento...', 'cyan');
    const depositData = {
      cash_session_id: sessionId,
      type: 'in',
      category: 'deposit',
      amount: 100.00,
      description: 'Suprimento de caixa'
    };

    await axios.post(`${API_BASE_URL}/cashier/movements`, depositData, {
      headers: getHeaders()
    });
    log('✅ Suprimento adicionado', 'green');

    // Buscar movimentos da sessão
    log('Buscando movimentos da sessão...', 'cyan');
    const movementsResponse = await axios.get(`${API_BASE_URL}/cashier/sessions/${sessionId}/movements`, {
      headers: getHeaders()
    });
    const movements = movementsResponse.data.data;
    log(`✅ ${movements.length} movimentos encontrados`, 'green');

    // Fechar sessão de caixa
    log('Fechando sessão de caixa...', 'cyan');
    const closeSessionData = {
      closing_balance: 250.00, // 200 inicial - 50 sangria + 100 suprimento
      notes: 'Teste de fechamento de caixa'
    };

    await axios.put(`${API_BASE_URL}/cashier/sessions/${sessionId}/close`, closeSessionData, {
      headers: getHeaders()
    });
    log('✅ Sessão de caixa fechada', 'green');

    return true;
  } catch (error) {
    log(`❌ Erro no fluxo de caixa: ${error.response?.data?.error || error.message}`, 'red');
    if (error.response?.data?.details) {
      console.log('Detalhes:', error.response.data.details);
    }
    return false;
  }
}

// Testar filtros de sessões
async function testSessionFilters() {
  log('\n🔍 Testando Filtros de Sessões...', 'blue');

  try {
    // Filtrar por status
    log('Filtrando sessões por status...', 'cyan');
    const statusFilterResponse = await axios.get(`${API_BASE_URL}/cashier/sessions?status=closed`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por status funcionando`, 'green');

    // Filtrar por usuário
    log('Filtrando sessões por usuário...', 'cyan');
    const userFilterResponse = await axios.get(`${API_BASE_URL}/cashier/sessions?user_id=1`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por usuário funcionando`, 'green');

    // Filtrar por data
    log('Filtrando sessões por data...', 'cyan');
    const today = new Date().toISOString().slice(0, 10);
    const dateFilterResponse = await axios.get(`${API_BASE_URL}/cashier/sessions?date_from=${today}&date_to=${today}`, {
      headers: getHeaders()
    });
    log(`✅ Filtro por data funcionando`, 'green');

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de filtros: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Testar validações
async function testValidations() {
  log('\n✅ Testando Validações...', 'blue');

  try {
    // Tentar abrir sessão sem caixa
    log('Testando abertura sem caixa...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/cashier/sessions`, {
        opening_balance: 100.00
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de caixa funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar abrir sessão com saldo negativo
    log('Testando saldo negativo...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/cashier/sessions`, {
        cash_register_id: 1,
        opening_balance: -50.00
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de saldo funcionando', 'green');
      } else {
        throw error;
      }
    }

    // Tentar adicionar movimento sem descrição
    log('Testando movimento sem descrição...', 'cyan');
    try {
      await axios.post(`${API_BASE_URL}/cashier/movements`, {
        cash_session_id: 1,
        type: 'out',
        category: 'withdrawal',
        amount: 10.00
      }, {
        headers: getHeaders()
      });
      log('❌ Deveria ter falhado', 'red');
    } catch (error) {
      if (error.response?.status === 400) {
        log('✅ Validação de descrição funcionando', 'green');
      } else {
        throw error;
      }
    }

    return true;
  } catch (error) {
    log(`❌ Erro nos testes de validação: ${error.response?.data?.error || error.message}`, 'red');
    return false;
  }
}

// Função principal
async function main() {
  try {
    log('🧪 Testando APIs de Caixa - Sistema PDV Adib\n', 'cyan');

    // Login
    const loginSuccess = await login();
    if (!loginSuccess) {
      log('\n❌ Falha no login. Verifique se o backend está rodando.', 'red');
      process.exit(1);
    }

    // Executar testes
    const results = {
      registers: await testCashRegisters(),
      sessions: await testCashSessions(),
      flow: await testCashFlow(),
      filters: await testSessionFilters(),
      validations: await testValidations(),
    };

    // Resumo dos resultados
    log('\n📋 Resumo dos Testes:', 'cyan');
    
    Object.entries(results).forEach(([test, passed]) => {
      const status = passed ? '✅ PASSOU' : '❌ FALHOU';
      const color = passed ? 'green' : 'red';
      log(`${test.toUpperCase()}: ${status}`, color);
    });

    const allPassed = Object.values(results).every(result => result);
    
    if (allPassed) {
      log('\n🎉 Todos os testes passaram! APIs de caixa funcionando corretamente.', 'green');
    } else {
      log('\n⚠️ Alguns testes falharam. Verifique os erros acima.', 'yellow');
    }

  } catch (error) {
    log(`\n❌ Erro durante os testes: ${error.message}`, 'red');
    process.exit(1);
  }
}

// Executar testes
main();
