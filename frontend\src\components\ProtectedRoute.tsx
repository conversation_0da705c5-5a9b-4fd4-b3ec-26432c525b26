import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthStore } from '../services/auth';
import { UserRole } from '../types';
import { Box, CircularProgress } from '@mui/material';

interface ProtectedRouteProps {
  children: React.ReactNode;
  roles?: UserRole[];
  requireAuth?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  roles = [],
  requireAuth = true,
}) => {
  const { isAuthenticated, user, isLoading } = useAuthStore();

  // Mostrar loading enquanto verifica autenticação
  if (isLoading) {
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
        }}
      >
        <CircularProgress />
      </Box>
    );
  }

  // Verificar se requer autenticação
  if (requireAuth && !isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // Verificar roles específicos
  if (roles.length > 0 && user && !roles.includes(user.role)) {
    // Redirecionar para página apropriada baseada no role
    const redirectPath = getDefaultPathForRole(user.role);
    return <Navigate to={redirectPath} replace />;
  }

  return <>{children}</>;
};

// Função para obter caminho padrão baseado no role
const getDefaultPathForRole = (role: UserRole): string => {
  switch (role) {
    case 'manager':
      return '/dashboard';
    case 'cashier':
      return '/pos';
    case 'kitchen':
      return '/kitchen';
    default:
      return '/dashboard';
  }
};

export default ProtectedRoute;
