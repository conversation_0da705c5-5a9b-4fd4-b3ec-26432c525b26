import { executeQuery, executeQuerySingle, executeUpdate } from '../utils/database';
import { 
  KitchenStation, 
  CreateKitchenStationRequest, 
  UpdateKitchenStationRequest 
} from '../types/kitchen';

export class KitchenStationModel {
  // Buscar estação por ID
  static async findById(id: number): Promise<KitchenStation | null> {
    const station = await executeQuerySingle<KitchenStation>(
      'SELECT * FROM kitchen_stations WHERE id = ?',
      [id]
    );
    return station || null;
  }

  // Buscar estação por nome
  static async findByName(name: string): Promise<KitchenStation | null> {
    const station = await executeQuerySingle<KitchenStation>(
      'SELECT * FROM kitchen_stations WHERE name = ? AND is_active = 1',
      [name]
    );
    return station || null;
  }

  // Listar todas as estações
  static async findAll(activeOnly: boolean = true): Promise<KitchenStation[]> {
    let query = 'SELECT * FROM kitchen_stations';
    const params: any[] = [];

    if (activeOnly) {
      query += ' WHERE is_active = 1';
    }

    query += ' ORDER BY order_priority ASC, name ASC';

    return await executeQuery<KitchenStation>(query, params);
  }

  // Criar estação
  static async create(stationData: CreateKitchenStationRequest): Promise<KitchenStation> {
    // Verificar se nome já existe
    const existingStation = await this.findByName(stationData.name);
    if (existingStation) {
      throw new Error('Station name already exists');
    }

    // Obter próxima ordem de prioridade se não fornecida
    let orderPriority = stationData.order_priority;
    if (orderPriority === undefined) {
      const maxPriority = await executeQuerySingle<{ max_priority: number }>(
        'SELECT COALESCE(MAX(order_priority), 0) as max_priority FROM kitchen_stations'
      );
      orderPriority = (maxPriority?.max_priority || 0) + 1;
    }

    // Inserir estação
    const result = await executeUpdate(
      `INSERT INTO kitchen_stations (name, description, printer_name, order_priority, is_active)
       VALUES (?, ?, ?, ?, 1)`,
      [
        stationData.name,
        stationData.description || null,
        stationData.printer_name || null,
        orderPriority,
      ]
    );

    if (!result.lastID) {
      throw new Error('Failed to create kitchen station');
    }

    // Retornar estação criada
    const newStation = await this.findById(result.lastID);
    if (!newStation) {
      throw new Error('Failed to retrieve created kitchen station');
    }

    return newStation;
  }

  // Atualizar estação
  static async update(id: number, stationData: UpdateKitchenStationRequest): Promise<KitchenStation> {
    const station = await this.findById(id);
    if (!station) {
      throw new Error('Kitchen station not found');
    }

    // Verificar se novo nome já existe (se estiver sendo alterado)
    if (stationData.name && stationData.name !== station.name) {
      const existingStation = await this.findByName(stationData.name);
      if (existingStation) {
        throw new Error('Station name already exists');
      }
    }

    const updateFields: string[] = [];
    const params: any[] = [];

    // Campos que podem ser atualizados
    const updatableFields = ['name', 'description', 'printer_name', 'is_active', 'order_priority'];

    updatableFields.forEach(field => {
      if (stationData[field as keyof UpdateKitchenStationRequest] !== undefined) {
        updateFields.push(`${field} = ?`);
        let value = stationData[field as keyof UpdateKitchenStationRequest];
        
        // Converter boolean para integer
        if (field === 'is_active') {
          value = value ? 1 : 0;
        }
        
        params.push(value);
      }
    });

    if (updateFields.length === 0) {
      return station; // Nada para atualizar
    }

    updateFields.push('updated_at = CURRENT_TIMESTAMP');
    params.push(id);

    await executeUpdate(
      `UPDATE kitchen_stations SET ${updateFields.join(', ')} WHERE id = ?`,
      params
    );

    // Retornar estação atualizada
    const updatedStation = await this.findById(id);
    if (!updatedStation) {
      throw new Error('Failed to retrieve updated kitchen station');
    }

    return updatedStation;
  }

  // Deletar estação (soft delete)
  static async delete(id: number): Promise<void> {
    const station = await this.findById(id);
    if (!station) {
      throw new Error('Kitchen station not found');
    }

    // Verificar se há itens ativos nesta estação
    const activeItems = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM kitchen_order_items WHERE station_id = ? AND status IN ("pending", "preparing")',
      [id]
    );

    if (activeItems && activeItems.count > 0) {
      throw new Error('Cannot delete station with active items');
    }

    await executeUpdate(
      'UPDATE kitchen_stations SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
      [id]
    );
  }

  // Obter estatísticas da estação
  static async getStats(id: number, dateFrom?: string, dateTo?: string): Promise<{
    total_items_processed: number;
    average_processing_time: number;
    items_in_progress: number;
    efficiency_score: number;
  }> {
    let whereClause = 'WHERE koi.station_id = ?';
    const params: any[] = [id];

    if (dateFrom) {
      whereClause += ' AND DATE(koi.created_at) >= ?';
      params.push(dateFrom);
    }

    if (dateTo) {
      whereClause += ' AND DATE(koi.created_at) <= ?';
      params.push(dateTo);
    }

    // Itens processados
    const processedItems = await executeQuerySingle<{ count: number }>(
      `SELECT COUNT(*) as count FROM kitchen_order_items koi ${whereClause} AND koi.status = 'ready'`,
      params
    );

    // Tempo médio de processamento
    const avgTime = await executeQuerySingle<{ avg_time: number }>(
      `SELECT AVG(
         (julianday(koi.finished_at) - julianday(koi.started_at)) * 24 * 60
       ) as avg_time
       FROM kitchen_order_items koi 
       ${whereClause} AND koi.status = 'ready' AND koi.started_at IS NOT NULL AND koi.finished_at IS NOT NULL`,
      params
    );

    // Itens em progresso
    const inProgress = await executeQuerySingle<{ count: number }>(
      `SELECT COUNT(*) as count FROM kitchen_order_items koi ${whereClause.replace('WHERE', 'WHERE')} AND koi.status IN ('pending', 'preparing')`,
      [id] // Apenas station_id para itens em progresso
    );

    const totalProcessed = processedItems?.count || 0;
    const averageTime = avgTime?.avg_time || 0;
    const itemsInProgress = inProgress?.count || 0;

    // Calcular score de eficiência (baseado em tempo vs tempo esperado)
    const expectedTime = 15; // TODO: Calcular baseado nos produtos
    const efficiencyScore = expectedTime > 0 && averageTime > 0 
      ? Math.max(0, Math.min(100, (expectedTime / averageTime) * 100))
      : 100;

    return {
      total_items_processed: totalProcessed,
      average_processing_time: averageTime,
      items_in_progress: itemsInProgress,
      efficiency_score: efficiencyScore,
    };
  }

  // Buscar estações com carga de trabalho
  static async findWithWorkload(): Promise<Array<KitchenStation & { 
    active_items: number; 
    pending_items: number; 
    preparing_items: number;
    utilization_percentage: number;
  }>> {
    const stations = await this.findAll();
    
    const stationsWithWorkload = await Promise.all(
      stations.map(async (station) => {
        // Buscar itens ativos
        const workload = await executeQuerySingle<{
          active_items: number;
          pending_items: number;
          preparing_items: number;
        }>(
          `SELECT 
             COUNT(*) as active_items,
             SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_items,
             SUM(CASE WHEN status = 'preparing' THEN 1 ELSE 0 END) as preparing_items
           FROM kitchen_order_items 
           WHERE station_id = ? AND status IN ('pending', 'preparing')`,
          [station.id]
        );

        const activeItems = workload?.active_items || 0;
        const pendingItems = workload?.pending_items || 0;
        const preparingItems = workload?.preparing_items || 0;

        // Calcular utilização (baseado em capacidade máxima estimada)
        const maxCapacity = 10; // TODO: Configurável por estação
        const utilizationPercentage = (activeItems / maxCapacity) * 100;

        return {
          ...station,
          active_items: activeItems,
          pending_items: pendingItems,
          preparing_items: preparingItems,
          utilization_percentage: Math.min(100, utilizationPercentage),
        };
      })
    );

    return stationsWithWorkload;
  }

  // Reordenar estações
  static async reorder(stationOrders: Array<{ id: number; order_priority: number }>): Promise<void> {
    for (const { id, order_priority } of stationOrders) {
      await executeUpdate(
        'UPDATE kitchen_stations SET order_priority = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [order_priority, id]
      );
    }
  }

  // Verificar se estação pode ser deletada
  static async canDelete(id: number): Promise<boolean> {
    const activeItems = await executeQuerySingle<{ count: number }>(
      'SELECT COUNT(*) as count FROM kitchen_order_items WHERE station_id = ? AND status IN ("pending", "preparing")',
      [id]
    );

    return (activeItems?.count || 0) === 0;
  }
}
