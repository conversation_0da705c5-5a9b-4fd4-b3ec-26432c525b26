import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  TextField,
  InputAdornment,
  Chip,
  Divider,
  IconButton,
  Badge,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
} from '@mui/material';
import {
  Search,
  Add,
  Remove,
  Delete,
  ShoppingCart,
  Payment,
  Print,
  Person,
  TableRestaurant,
  LocalShipping,
  Clear,
} from '@mui/icons-material';
import { Product, Category } from '../types';
import { useAuthStore } from '../services/auth';

interface CartItem {
  product: Product;
  quantity: number;
  notes?: string;
}

interface OrderData {
  type: 'counter' | 'table' | 'delivery';
  customer_id?: number;
  table_id?: number;
  notes?: string;
  delivery_address?: string;
}

const POS: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [cart, setCart] = useState<CartItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [orderData, setOrderData] = useState<OrderData>({ type: 'counter' });
  const [paymentDialog, setPaymentDialog] = useState(false);
  const [loading, setLoading] = useState(false);

  const { user } = useAuthStore();

  // Simular dados para demonstração
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        
        // Simular categorias
        const mockCategories: Category[] = [
          { id: 1, name: 'Lanches', description: 'Hambúrgueres e sanduíches', color: '#FF5722', icon: 'fastfood', is_active: true, created_at: '2024-12-19', updated_at: '2024-12-19' },
          { id: 2, name: 'Bebidas', description: 'Refrigerantes e sucos', color: '#2196F3', icon: 'local_drink', is_active: true, created_at: '2024-12-19', updated_at: '2024-12-19' },
          { id: 3, name: 'Sobremesas', description: 'Doces e sobremesas', color: '#E91E63', icon: 'cake', is_active: true, created_at: '2024-12-19', updated_at: '2024-12-19' },
        ];

        // Simular produtos
        const mockProducts: Product[] = [
          {
            id: 1, code: 'PROD0001', name: 'Hambúrguer Clássico', description: 'Hambúrguer com carne, queijo, alface e tomate',
            category_id: 1, category_name: 'Lanches', category_color: '#FF5722', price: 15.90, cost: 8.50, unit: 'UN',
            stock_control: true, current_stock: 50, min_stock: 10, max_stock: 100, is_active: true,
            preparation_time: 15, cfop: '5102', tax_rate: 0, created_at: '2024-12-19', updated_at: '2024-12-19',
          },
          {
            id: 2, code: 'PROD0002', name: 'Coca-Cola 350ml', description: 'Refrigerante Coca-Cola lata 350ml',
            category_id: 2, category_name: 'Bebidas', category_color: '#2196F3', price: 4.50, cost: 2.20, unit: 'UN',
            stock_control: true, current_stock: 100, min_stock: 20, max_stock: 200, is_active: true,
            preparation_time: 0, cfop: '5102', tax_rate: 0, created_at: '2024-12-19', updated_at: '2024-12-19',
          },
          {
            id: 3, code: 'PROD0003', name: 'Pudim de Leite', description: 'Pudim de leite condensado caseiro',
            category_id: 3, category_name: 'Sobremesas', category_color: '#E91E63', price: 8.90, cost: 4.20, unit: 'UN',
            stock_control: true, current_stock: 20, min_stock: 5, max_stock: 50, is_active: true,
            preparation_time: 5, cfop: '5102', tax_rate: 0, created_at: '2024-12-19', updated_at: '2024-12-19',
          },
          {
            id: 4, code: 'PROD0004', name: 'Batata Frita', description: 'Porção de batata frita crocante',
            category_id: 1, category_name: 'Lanches', category_color: '#FF5722', price: 12.90, cost: 6.50, unit: 'UN',
            stock_control: true, current_stock: 30, min_stock: 10, max_stock: 100, is_active: true,
            preparation_time: 10, cfop: '5102', tax_rate: 0, created_at: '2024-12-19', updated_at: '2024-12-19',
          },
        ];

        setCategories(mockCategories);
        setProducts(mockProducts);
      } catch (error) {
        console.error('Error loading data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadData();
  }, []);

  // Filtrar produtos
  const filteredProducts = products.filter(product => {
    const matchesCategory = !selectedCategory || product.category_id === selectedCategory;
    const matchesSearch = !searchTerm || 
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.code?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch && product.is_active;
  });

  // Adicionar produto ao carrinho
  const addToCart = (product: Product) => {
    const existingItem = cart.find(item => item.product.id === product.id);
    
    if (existingItem) {
      setCart(cart.map(item =>
        item.product.id === product.id
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCart([...cart, { product, quantity: 1 }]);
    }
  };

  // Remover produto do carrinho
  const removeFromCart = (productId: number) => {
    const existingItem = cart.find(item => item.product.id === productId);
    
    if (existingItem && existingItem.quantity > 1) {
      setCart(cart.map(item =>
        item.product.id === productId
          ? { ...item, quantity: item.quantity - 1 }
          : item
      ));
    } else {
      setCart(cart.filter(item => item.product.id !== productId));
    }
  };

  // Limpar carrinho
  const clearCart = () => {
    setCart([]);
  };

  // Calcular totais
  const subtotal = cart.reduce((sum, item) => sum + (item.product.price * item.quantity), 0);
  const deliveryFee = orderData.type === 'delivery' ? 5.00 : 0;
  const total = subtotal + deliveryFee;

  // Finalizar pedido
  const finishOrder = () => {
    if (cart.length === 0) {
      return;
    }
    setPaymentDialog(true);
  };

  // Processar pagamento
  const processPayment = () => {
    // Simular processamento
    console.log('Processing order:', {
      ...orderData,
      items: cart.map(item => ({
        product_id: item.product.id,
        quantity: item.quantity,
        unit_price: item.product.price,
        notes: item.notes,
      })),
      subtotal,
      delivery_fee: deliveryFee,
      total,
    });

    // Limpar carrinho e fechar dialog
    clearCart();
    setPaymentDialog(false);
    setOrderData({ type: 'counter' });
  };

  const cartItemCount = cart.reduce((sum, item) => sum + item.quantity, 0);

  return (
    <Box sx={{ height: '100vh', display: 'flex', flexDirection: 'column' }}>
      {/* Header */}
      <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider', bgcolor: 'background.paper' }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="h5" component="h1">
              PDV - Ponto de Venda
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Operador: {user?.full_name}
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{ display: 'flex', gap: 1, justifyContent: 'flex-end' }}>
              <FormControl size="small" sx={{ minWidth: 120 }}>
                <InputLabel>Tipo</InputLabel>
                <Select
                  value={orderData.type}
                  onChange={(e) => setOrderData(prev => ({ ...prev, type: e.target.value as any }))}
                  label="Tipo"
                >
                  <MenuItem value="counter">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShoppingCart fontSize="small" />
                      Balcão
                    </Box>
                  </MenuItem>
                  <MenuItem value="table">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <TableRestaurant fontSize="small" />
                      Mesa
                    </Box>
                  </MenuItem>
                  <MenuItem value="delivery">
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocalShipping fontSize="small" />
                      Delivery
                    </Box>
                  </MenuItem>
                </Select>
              </FormControl>
            </Box>
          </Grid>
        </Grid>
      </Box>

      <Box sx={{ flexGrow: 1, display: 'flex' }}>
        {/* Produtos */}
        <Box sx={{ flexGrow: 1, p: 2 }}>
          {/* Filtros */}
          <Box sx={{ mb: 2 }}>
            <TextField
              fullWidth
              placeholder="Buscar produtos..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Search />
                  </InputAdornment>
                ),
              }}
              sx={{ mb: 2 }}
            />

            {/* Categorias */}
            <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              <Chip
                label="Todas"
                onClick={() => setSelectedCategory(null)}
                color={selectedCategory === null ? 'primary' : 'default'}
                variant={selectedCategory === null ? 'filled' : 'outlined'}
              />
              {categories.map(category => (
                <Chip
                  key={category.id}
                  label={category.name}
                  onClick={() => setSelectedCategory(category.id)}
                  color={selectedCategory === category.id ? 'primary' : 'default'}
                  variant={selectedCategory === category.id ? 'filled' : 'outlined'}
                  sx={{
                    backgroundColor: selectedCategory === category.id ? category.color : undefined,
                    '&:hover': {
                      backgroundColor: selectedCategory === category.id ? category.color : undefined,
                    },
                  }}
                />
              ))}
            </Box>
          </Box>

          {/* Grid de produtos */}
          <Grid container spacing={2}>
            {filteredProducts.map(product => (
              <Grid item xs={12} sm={6} md={4} lg={3} key={product.id}>
                <Card 
                  sx={{ 
                    cursor: 'pointer',
                    transition: 'transform 0.2s',
                    '&:hover': {
                      transform: 'scale(1.02)',
                    },
                  }}
                  onClick={() => addToCart(product)}
                >
                  <CardContent>
                    <Chip
                      label={product.category_name}
                      size="small"
                      sx={{
                        backgroundColor: product.category_color,
                        color: 'white',
                        mb: 1,
                      }}
                    />
                    <Typography variant="h6" component="h3" gutterBottom>
                      {product.name}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      {product.description}
                    </Typography>
                    <Typography variant="h5" color="primary">
                      R$ {product.price.toFixed(2)}
                    </Typography>
                    {product.stock_control && (
                      <Typography variant="caption" color="text.secondary">
                        Estoque: {product.current_stock} {product.unit}
                      </Typography>
                    )}
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Carrinho */}
        <Box sx={{ width: 400, borderLeft: 1, borderColor: 'divider', display: 'flex', flexDirection: 'column' }}>
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6">
                Carrinho
                <Badge badgeContent={cartItemCount} color="primary" sx={{ ml: 1 }}>
                  <ShoppingCart />
                </Badge>
              </Typography>
              {cart.length > 0 && (
                <IconButton onClick={clearCart} color="error">
                  <Clear />
                </IconButton>
              )}
            </Box>
          </Box>

          <Box sx={{ flexGrow: 1, overflow: 'auto' }}>
            {cart.length === 0 ? (
              <Box sx={{ p: 3, textAlign: 'center' }}>
                <ShoppingCart sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography color="text.secondary">
                  Carrinho vazio
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Adicione produtos para começar
                </Typography>
              </Box>
            ) : (
              <List>
                {cart.map(item => (
                  <ListItem key={item.product.id}>
                    <ListItemText
                      primary={item.product.name}
                      secondary={`R$ ${item.product.price.toFixed(2)} x ${item.quantity}`}
                    />
                    <ListItemSecondaryAction>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <IconButton
                          size="small"
                          onClick={() => removeFromCart(item.product.id)}
                        >
                          <Remove />
                        </IconButton>
                        <Typography>{item.quantity}</Typography>
                        <IconButton
                          size="small"
                          onClick={() => addToCart(item.product)}
                        >
                          <Add />
                        </IconButton>
                      </Box>
                    </ListItemSecondaryAction>
                  </ListItem>
                ))}
              </List>
            )}
          </Box>

          {cart.length > 0 && (
            <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography>Subtotal:</Typography>
                  <Typography>R$ {subtotal.toFixed(2)}</Typography>
                </Box>
                {deliveryFee > 0 && (
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography>Taxa de entrega:</Typography>
                    <Typography>R$ {deliveryFee.toFixed(2)}</Typography>
                  </Box>
                )}
                <Divider sx={{ my: 1 }} />
                <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                  <Typography variant="h6">Total:</Typography>
                  <Typography variant="h6" color="primary">
                    R$ {total.toFixed(2)}
                  </Typography>
                </Box>
              </Box>

              <Button
                fullWidth
                variant="contained"
                size="large"
                startIcon={<Payment />}
                onClick={finishOrder}
              >
                Finalizar Pedido
              </Button>
            </Box>
          )}
        </Box>
      </Box>

      {/* Dialog de Pagamento */}
      <Dialog open={paymentDialog} onClose={() => setPaymentDialog(false)} maxWidth="sm" fullWidth>
        <DialogTitle>Finalizar Pedido</DialogTitle>
        <DialogContent>
          <Alert severity="info" sx={{ mb: 2 }}>
            Esta é uma demonstração. O pagamento será simulado.
          </Alert>
          
          <Typography variant="h6" gutterBottom>
            Resumo do Pedido
          </Typography>
          
          <List dense>
            {cart.map(item => (
              <ListItem key={item.product.id}>
                <ListItemText
                  primary={`${item.quantity}x ${item.product.name}`}
                  secondary={`R$ ${item.product.price.toFixed(2)} cada`}
                />
                <Typography>
                  R$ {(item.product.price * item.quantity).toFixed(2)}
                </Typography>
              </ListItem>
            ))}
          </List>
          
          <Divider sx={{ my: 2 }} />
          
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6">Total:</Typography>
            <Typography variant="h6" color="primary">
              R$ {total.toFixed(2)}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setPaymentDialog(false)}>
            Cancelar
          </Button>
          <Button variant="contained" onClick={processPayment}>
            Confirmar Pagamento
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default POS;
