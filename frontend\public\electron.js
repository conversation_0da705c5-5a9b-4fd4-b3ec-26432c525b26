const { app, BrowserWindow, Menu, ipcMain, dialog } = require('electron');
const path = require('path');
const isDev = require('electron-is-dev');

// Manter referência global da janela
let mainWindow;

function createWindow() {
  // Criar a janela principal
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'icon.png'),
    show: false,
    titleBarStyle: 'default'
  });

  // Carregar a aplicação
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../build/index.html')}`;
  
  mainWindow.loadURL(startUrl);

  // Mostrar janela quando estiver pronta
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
    
    // Abrir DevTools apenas em desenvolvimento
    if (isDev) {
      mainWindow.webContents.openDevTools();
    }
  });

  // Emitir evento quando a janela for fechada
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Configurar menu
  createMenu();
}

function createMenu() {
  const template = [
    {
      label: 'Arquivo',
      submenu: [
        {
          label: 'Novo Pedido',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('menu-action', 'new-order');
          }
        },
        {
          label: 'Abrir Caixa',
          accelerator: 'CmdOrCtrl+O',
          click: () => {
            mainWindow.webContents.send('menu-action', 'open-cash');
          }
        },
        { type: 'separator' },
        {
          label: 'Configurações',
          accelerator: 'CmdOrCtrl+,',
          click: () => {
            mainWindow.webContents.send('menu-action', 'settings');
          }
        },
        { type: 'separator' },
        {
          label: 'Sair',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit();
          }
        }
      ]
    },
    {
      label: 'Vendas',
      submenu: [
        {
          label: 'Balcão',
          accelerator: 'F1',
          click: () => {
            mainWindow.webContents.send('menu-action', 'counter-sale');
          }
        },
        {
          label: 'Mesas',
          accelerator: 'F2',
          click: () => {
            mainWindow.webContents.send('menu-action', 'table-sale');
          }
        },
        {
          label: 'Delivery',
          accelerator: 'F3',
          click: () => {
            mainWindow.webContents.send('menu-action', 'delivery-sale');
          }
        }
      ]
    },
    {
      label: 'Caixa',
      submenu: [
        {
          label: 'Sangria',
          click: () => {
            mainWindow.webContents.send('menu-action', 'cash-withdrawal');
          }
        },
        {
          label: 'Suprimento',
          click: () => {
            mainWindow.webContents.send('menu-action', 'cash-supply');
          }
        },
        {
          label: 'Fechar Caixa',
          accelerator: 'CmdOrCtrl+Shift+F',
          click: () => {
            mainWindow.webContents.send('menu-action', 'close-cash');
          }
        }
      ]
    },
    {
      label: 'Relatórios',
      submenu: [
        {
          label: 'Vendas do Dia',
          click: () => {
            mainWindow.webContents.send('menu-action', 'daily-sales');
          }
        },
        {
          label: 'Produtos Mais Vendidos',
          click: () => {
            mainWindow.webContents.send('menu-action', 'top-products');
          }
        },
        {
          label: 'Insights IA',
          accelerator: 'CmdOrCtrl+I',
          click: () => {
            mainWindow.webContents.send('menu-action', 'ai-insights');
          }
        }
      ]
    },
    {
      label: 'Ajuda',
      submenu: [
        {
          label: 'Sobre',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'Sobre o Adib PDV',
              message: 'Adib PDV v1.0.0',
              detail: 'Sistema de Ponto de Venda offline-first\nDesenvolvido com Electron + React'
            });
          }
        },
        {
          label: 'Documentação',
          click: () => {
            require('electron').shell.openExternal('https://docs.adibpdv.com');
          }
        }
      ]
    }
  ];

  const menu = Menu.buildFromTemplate(template);
  Menu.setApplicationMenu(menu);
}

// Eventos do Electron
app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// IPC Handlers
ipcMain.handle('get-app-version', () => {
  return app.getVersion();
});

ipcMain.handle('show-save-dialog', async () => {
  const result = await dialog.showSaveDialog(mainWindow, {
    filters: [
      { name: 'PDF', extensions: ['pdf'] },
      { name: 'CSV', extensions: ['csv'] },
      { name: 'Todos os arquivos', extensions: ['*'] }
    ]
  });
  return result;
});

ipcMain.handle('show-open-dialog', async () => {
  const result = await dialog.showOpenDialog(mainWindow, {
    properties: ['openFile'],
    filters: [
      { name: 'Backup', extensions: ['db', 'sqlite'] },
      { name: 'Todos os arquivos', extensions: ['*'] }
    ]
  });
  return result;
});

// Prevenir navegação externa
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault();
    require('electron').shell.openExternal(navigationUrl);
  });
});
