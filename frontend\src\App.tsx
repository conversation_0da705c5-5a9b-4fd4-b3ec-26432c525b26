import { Box, CircularProgress, CssBaseline } from '@mui/material';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import React, { useEffect } from 'react';
import { Navigate, Route, BrowserRouter as Router, Routes } from 'react-router-dom';
import { useAuthStore } from './services/auth';

// Páginas
import Cashier from './pages/Cashier';
import Dashboard from './pages/Dashboard';
import Fiscal from './pages/Fiscal';
import Kitchen from './pages/Kitchen';
import Login from './pages/Login';
import POS from './pages/POS';
import Products from './pages/Products';
import Tef from './pages/Tef';

// Componentes
import ErrorBoundary from './components/ErrorBoundary';
import ProtectedRoute from './components/ProtectedRoute';

// Tema personalizado
const theme = createTheme({
  palette: {
    primary: {
      main: '#667eea',
      light: '#9bb5ff',
      dark: '#3f51b5',
    },
    secondary: {
      main: '#764ba2',
      light: '#a777d3',
      dark: '#4a2c73',
    },
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: '"Inter", "Roboto", "Helvetica", "Arial", sans-serif',
    h1: {
      fontWeight: 700,
    },
    h2: {
      fontWeight: 600,
    },
    h3: {
      fontWeight: 600,
    },
    h4: {
      fontWeight: 600,
    },
    h5: {
      fontWeight: 500,
    },
    h6: {
      fontWeight: 500,
    },
  },
  shape: {
    borderRadius: 8,
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          fontWeight: 500,
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
        },
      },
    },
  },
});

// Componente de loading
const LoadingScreen: React.FC = () => (
  <Box
    sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
      flexDirection: 'column',
      gap: 2,
    }}
  >
    <CircularProgress size={40} />
    <Box sx={{ textAlign: 'center' }}>
      Carregando...
    </Box>
  </Box>
);

// Hook para verificar autenticação na inicialização
const useInitialAuth = () => {
  const { checkAuth, isAuthenticated, isLoading } = useAuthStore();
  const [initializing, setInitializing] = React.useState(true);

  useEffect(() => {
    const initAuth = async () => {
      try {
        await checkAuth();
      } catch (error) {
        console.error('Initial auth check failed:', error);
      } finally {
        setInitializing(false);
      }
    };

    initAuth();
  }, [checkAuth]);

  return { initializing, isAuthenticated, isLoading };
};

const App: React.FC = () => {
  const { initializing, isAuthenticated, isLoading } = useInitialAuth();

  // Mostrar loading durante inicialização
  if (initializing || isLoading) {
    return (
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <LoadingScreen />
      </ThemeProvider>
    );
  }

  return (
    <ErrorBoundary>
      <ThemeProvider theme={theme}>
        <CssBaseline />
        <Router>
          <Routes>
            {/* Rota de login */}
            <Route
              path="/login"
              element={
                isAuthenticated ? (
                  <Navigate to="/dashboard" replace />
                ) : (
                  <Login />
                )
              }
            />

            {/* Rotas protegidas */}
            <Route
              path="/dashboard"
              element={
                <ProtectedRoute>
                  <Dashboard />
                </ProtectedRoute>
              }
            />

            <Route
              path="/products"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <Products />
                </ProtectedRoute>
              }
            />

            <Route
              path="/pos"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <POS />
                </ProtectedRoute>
              }
            />

            <Route
              path="/kitchen"
              element={
                <ProtectedRoute roles={['manager', 'kitchen']}>
                  <Kitchen />
                </ProtectedRoute>
              }
            />

            <Route
              path="/cashier"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <Cashier />
                </ProtectedRoute>
              }
            />

            <Route
              path="/fiscal"
              element={
                <ProtectedRoute roles={['manager', 'fiscal']}>
                  <Fiscal />
                </ProtectedRoute>
              }
            />

            <Route
              path="/tef"
              element={
                <ProtectedRoute roles={['manager', 'cashier']}>
                  <Tef />
                </ProtectedRoute>
              }
            />

            {/* Rota padrão */}
            <Route
              path="/"
              element={
                <Navigate
                  to={isAuthenticated ? "/dashboard" : "/login"}
                  replace
                />
              }
            />

            {/* Rota 404 */}
            <Route
              path="*"
              element={
                <Navigate
                  to={isAuthenticated ? "/dashboard" : "/login"}
                  replace
                />
              }
            />
          </Routes>
        </Router>
      </ThemeProvider>
    </ErrorBoundary>
  );
};

export default App;
