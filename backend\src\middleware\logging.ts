import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';
import { AuthenticatedRequest } from '../types';

// Estender Request para incluir requestId e startTime
declare global {
  namespace Express {
    interface Request {
      requestId?: string;
      startTime?: number;
    }
  }
}

/**
 * Middleware para logging de requisições HTTP
 */
export const requestLogger = (req: Request, res: Response, next: NextFunction): void => {
  // Gerar ID único para a requisição
  req.requestId = uuidv4();
  req.startTime = Date.now();

  // Obter informações da requisição
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  const userAgent = req.get('User-Agent') || 'unknown';
  const method = req.method;
  const url = req.originalUrl || req.url;

  // Log de início da requisição (apenas para DEBUG)
  logger.debug(`Request started: ${method} ${url}`, 'http', {
    requestId: req.requestId,
    ip,
    userAgent,
    method,
    url,
  });

  // Interceptar o final da resposta
  const originalSend = res.send;
  const originalJson = res.json;
  const originalEnd = res.end;

  let responseBody: any;
  let responseSent = false;

  // Interceptar res.send
  res.send = function(body: any) {
    if (!responseSent) {
      responseBody = body;
      logResponse();
      responseSent = true;
    }
    return originalSend.call(this, body);
  };

  // Interceptar res.json
  res.json = function(body: any) {
    if (!responseSent) {
      responseBody = body;
      logResponse();
      responseSent = true;
    }
    return originalJson.call(this, body);
  };

  // Interceptar res.end
  res.end = function(chunk?: any, encoding?: any) {
    if (!responseSent) {
      responseBody = chunk;
      logResponse();
      responseSent = true;
    }
    return originalEnd.call(this, chunk, encoding);
  };

  function logResponse() {
    const duration = Date.now() - (req.startTime || 0);
    const statusCode = res.statusCode;
    const authReq = req as AuthenticatedRequest;
    const userId = authReq.user?.id;
    const username = authReq.user?.username;

    // Log da requisição completa
    logger.request(
      method,
      url,
      statusCode,
      duration,
      userId,
      username,
      ip,
      userAgent,
      req.requestId
    );

    // Log adicional para erros
    if (statusCode >= 400) {
      let errorMessage = 'HTTP Error';
      
      try {
        if (typeof responseBody === 'string') {
          const parsed = JSON.parse(responseBody);
          errorMessage = parsed.error || parsed.message || errorMessage;
        } else if (responseBody && typeof responseBody === 'object') {
          errorMessage = responseBody.error || responseBody.message || errorMessage;
        }
      } catch {
        // Ignorar erros de parsing
      }

      logger.error(`HTTP ${statusCode}: ${errorMessage}`, 'http', {
        requestId: req.requestId,
        method,
        url,
        statusCode,
        duration,
        userId,
        username,
        ip,
        userAgent,
        responseBody: statusCode >= 500 ? responseBody : undefined, // Log body apenas para erros de servidor
      });
    }

    // Log de performance para requisições lentas
    if (duration > 1000) {
      logger.performance(`Slow request: ${method} ${url}`, duration, {
        requestId: req.requestId,
        statusCode,
        userId,
        username,
      });
    }
  }

  next();
};

/**
 * Middleware para logging de ações do usuário
 */
export const actionLogger = (action: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const originalSend = res.send;
    const originalJson = res.json;

    let responseSent = false;

    const logAction = (success: boolean, data?: any) => {
      if (responseSent) return;
      responseSent = true;

      const userId = req.user?.id;
      const username = req.user?.username;
      const ip = req.ip || req.connection.remoteAddress || 'unknown';

      if (success) {
        logger.audit(`User action: ${action}`, {
          requestId: req.requestId,
          userId,
          username,
          ip,
          action,
          data: data ? JSON.stringify(data).substring(0, 500) : undefined, // Limitar tamanho
        });
      } else {
        logger.warn(`Failed user action: ${action}`, 'audit', {
          requestId: req.requestId,
          userId,
          username,
          ip,
          action,
          statusCode: res.statusCode,
        });
      }
    };

    // Interceptar res.send
    res.send = function(body: any) {
      logAction(res.statusCode < 400, body);
      return originalSend.call(this, body);
    };

    // Interceptar res.json
    res.json = function(body: any) {
      logAction(res.statusCode < 400, body);
      return originalJson.call(this, body);
    };

    next();
  };
};

/**
 * Middleware para logging de segurança
 */
export const securityLogger = (event: string) => {
  return (req: AuthenticatedRequest, res: Response, next: NextFunction): void => {
    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';
    const userId = req.user?.id;
    const username = req.user?.username;

    logger.security(`Security event: ${event}`, {
      requestId: req.requestId,
      event,
      userId,
      username,
      ip,
      userAgent,
      url: req.originalUrl || req.url,
      method: req.method,
    });

    next();
  };
};

/**
 * Middleware para capturar erros e fazer log
 */
export const errorLogger = (
  error: Error,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  const authReq = req as AuthenticatedRequest;
  const userId = authReq.user?.id;
  const username = authReq.user?.username;
  const ip = req.ip || req.connection.remoteAddress || 'unknown';

  logger.captureException(error, 'express', {
    requestId: req.requestId,
    method: req.method,
    url: req.originalUrl || req.url,
    userId,
    username,
    ip,
    userAgent: req.get('User-Agent'),
    body: req.method !== 'GET' ? JSON.stringify(req.body).substring(0, 1000) : undefined,
  });

  // Se a resposta ainda não foi enviada, enviar erro genérico
  if (!res.headersSent) {
    res.status(500).json({
      success: false,
      error: 'Internal server error',
      requestId: req.requestId,
      timestamp: new Date().toISOString(),
    });
  }

  next(error);
};

/**
 * Middleware para logging de tentativas de login
 */
export const loginAttemptLogger = (req: Request, res: Response, next: NextFunction): void => {
  const originalSend = res.send;
  const originalJson = res.json;

  let responseSent = false;

  const logAttempt = (success: boolean, username?: string) => {
    if (responseSent) return;
    responseSent = true;

    const ip = req.ip || req.connection.remoteAddress || 'unknown';
    const userAgent = req.get('User-Agent') || 'unknown';

    if (success) {
      logger.security('Successful login', {
        requestId: req.requestId,
        username,
        ip,
        userAgent,
      });
    } else {
      logger.security('Failed login attempt', {
        requestId: req.requestId,
        username,
        ip,
        userAgent,
        statusCode: res.statusCode,
      });
    }
  };

  // Interceptar res.send
  res.send = function(body: any) {
    try {
      const parsed = typeof body === 'string' ? JSON.parse(body) : body;
      const success = res.statusCode < 400 && parsed.success;
      const username = req.body?.username || parsed.data?.user?.username;
      logAttempt(success, username);
    } catch {
      logAttempt(false, req.body?.username);
    }
    return originalSend.call(this, body);
  };

  // Interceptar res.json
  res.json = function(body: any) {
    const success = res.statusCode < 400 && body.success;
    const username = req.body?.username || body.data?.user?.username;
    logAttempt(success, username);
    return originalJson.call(this, body);
  };

  next();
};
