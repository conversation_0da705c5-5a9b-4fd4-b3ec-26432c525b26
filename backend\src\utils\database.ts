import sqlite3 from 'sqlite3';
import { Database, open } from 'sqlite';
import path from 'path';
import fs from 'fs';

let db: Database | null = null;

const DB_PATH = path.join(__dirname, '..', '..', '..', 'database', 'adib.db');

export async function initDatabase(): Promise<Database> {
  if (db) {
    return db;
  }

  try {
    // Verificar se o arquivo do banco existe
    if (!fs.existsSync(DB_PATH)) {
      throw new Error(`Database file not found: ${DB_PATH}`);
    }

    // Abrir conexão com o banco
    db = await open({
      filename: DB_PATH,
      driver: sqlite3.Database,
    });

    // Configurar SQLite para melhor performance
    await db.exec(`
      PRAGMA foreign_keys = ON;
      PRAGMA journal_mode = WAL;
      PRAGMA synchronous = NORMAL;
      PRAGMA cache_size = 1000;
      PRAGMA temp_store = MEMORY;
    `);

    console.log('✅ Database connected successfully');
    return db;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    throw error;
  }
}

export async function getDatabase(): Promise<Database> {
  if (!db) {
    return await initDatabase();
  }
  return db;
}

export async function closeDatabase(): Promise<void> {
  if (db) {
    await db.close();
    db = null;
    console.log('Database connection closed');
  }
}

// Função para executar queries com tratamento de erro
export async function executeQuery<T = any>(
  query: string,
  params: any[] = []
): Promise<T[]> {
  const database = await getDatabase();
  try {
    return await database.all(query, params);
  } catch (error) {
    console.error('Query execution failed:', { query, params, error });
    throw error;
  }
}

// Função para executar query que retorna um único resultado
export async function executeQuerySingle<T = any>(
  query: string,
  params: any[] = []
): Promise<T | undefined> {
  const database = await getDatabase();
  try {
    return await database.get(query, params);
  } catch (error) {
    console.error('Query execution failed:', { query, params, error });
    throw error;
  }
}

// Função para executar INSERT/UPDATE/DELETE
export async function executeUpdate(
  query: string,
  params: any[] = []
): Promise<{ lastID?: number; changes: number }> {
  const database = await getDatabase();
  try {
    const result = await database.run(query, params);
    return {
      lastID: result.lastID,
      changes: result.changes || 0,
    };
  } catch (error) {
    console.error('Update execution failed:', { query, params, error });
    throw error;
  }
}

// Função para executar transações
export async function executeTransaction<T>(
  callback: (db: Database) => Promise<T>
): Promise<T> {
  const database = await getDatabase();
  
  try {
    await database.exec('BEGIN TRANSACTION');
    const result = await callback(database);
    await database.exec('COMMIT');
    return result;
  } catch (error) {
    await database.exec('ROLLBACK');
    console.error('Transaction failed:', error);
    throw error;
  }
}

// Função para verificar se uma tabela existe
export async function tableExists(tableName: string): Promise<boolean> {
  const result = await executeQuerySingle<{ count: number }>(
    "SELECT COUNT(*) as count FROM sqlite_master WHERE type='table' AND name=?",
    [tableName]
  );
  return (result?.count || 0) > 0;
}

// Função para obter informações da tabela
export async function getTableInfo(tableName: string): Promise<any[]> {
  return await executeQuery(`PRAGMA table_info(${tableName})`);
}

// Função para backup do banco
export async function backupDatabase(backupPath: string): Promise<void> {
  const database = await getDatabase();
  
  try {
    // Criar diretório de backup se não existir
    const backupDir = path.dirname(backupPath);
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }

    // Copiar arquivo do banco
    fs.copyFileSync(DB_PATH, backupPath);
    console.log(`Database backed up to: ${backupPath}`);
  } catch (error) {
    console.error('Backup failed:', error);
    throw error;
  }
}

// Função para restaurar backup
export async function restoreDatabase(backupPath: string): Promise<void> {
  if (!fs.existsSync(backupPath)) {
    throw new Error(`Backup file not found: ${backupPath}`);
  }

  try {
    // Fechar conexão atual
    await closeDatabase();
    
    // Restaurar arquivo
    fs.copyFileSync(backupPath, DB_PATH);
    
    // Reconectar
    await initDatabase();
    
    console.log(`Database restored from: ${backupPath}`);
  } catch (error) {
    console.error('Restore failed:', error);
    throw error;
  }
}
